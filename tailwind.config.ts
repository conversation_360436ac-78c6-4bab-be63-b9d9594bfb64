/* eslint-disable ts/no-require-imports */
import type { Config } from 'tailwindcss';

const config = {
  darkMode: ['class', '[data-theme="dark"]'], // Updated to support data-theme attribute for dark mode
  content: ['./src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      fontFamily: {
        sans: ['var(--font-family-body)'],
        headings: ['var(--font-family-headings)'],
      },
      fontSize: {
        xs: 'var(--font-size-xs)',
        sm: 'var(--font-size-sm)',
        md: 'var(--font-size-md)',
        lg: 'var(--font-size-lg)',
        xl: 'var(--font-size-xl)',
        h1: 'var(--font-size-h1)',
        h2: 'var(--font-size-h2)',
        h3: 'var(--font-size-h3)',
        h4: 'var(--font-size-h4)',
        h5: 'var(--font-size-h5)',
        h6: 'var(--font-size-h6)',
      },
      spacing: {
        'unit': 'var(--space-unit)',
        'xs': 'var(--space-xs)',
        'sm': 'var(--space-sm)',
        'md': 'var(--space-md)',
        'lg': 'var(--space-lg)',
        'xl': 'var(--space-xl)',
        '2xl': 'var(--space-2xl)',
        '3xl': 'var(--space-3xl)',
        'section-padding-y': 'var(--space-section-padding-y)',
      },
      colors: {
        // TechnoKids Color System v18 - Direct CSS Variable References
        // Based on colorguidev18.md

        // Primary Brand Colors (Purple Scale)
        'tk-purple': {
          50: 'var(--tk-purple-50)',
          100: 'var(--tk-purple-100)',
          200: 'var(--tk-purple-200)',
          300: 'var(--tk-purple-300)',
          400: 'var(--tk-purple-400)',
          500: 'var(--tk-purple-500)', // Primary Brand Color
          600: 'var(--tk-purple-600)',
          700: 'var(--tk-purple-700)',
          800: 'var(--tk-purple-800)',
          900: 'var(--tk-purple-900)',
          950: 'var(--tk-purple-950)',
        },

        // Secondary Brand Colors (Indigo Scale)
        'tk-indigo': {
          50: 'var(--tk-indigo-50)',
          100: 'var(--tk-indigo-100)',
          200: 'var(--tk-indigo-200)',
          300: 'var(--tk-indigo-300)',
          400: 'var(--tk-indigo-400)',
          500: 'var(--tk-indigo-500)', // Secondary Brand Color
          600: 'var(--tk-indigo-600)',
          700: 'var(--tk-indigo-700)',
          800: 'var(--tk-indigo-800)',
          900: 'var(--tk-indigo-900)',
          950: 'var(--tk-indigo-950)',
        },

        // Key Accent Colors (Violet Scale)
        'tk-violet': {
          50: 'var(--tk-violet-50)',
          100: 'var(--tk-violet-100)',
          200: 'var(--tk-violet-200)',
          300: 'var(--tk-violet-300)',
          400: 'var(--tk-violet-400)', // Primary Violet Accent
          500: 'var(--tk-violet-500)',
          600: 'var(--tk-violet-600)',
          700: 'var(--tk-violet-700)',
          800: 'var(--tk-violet-800)',
          900: 'var(--tk-violet-900)',
          950: 'var(--tk-violet-950)',
        },

        // Key Warm Accent Colors (Pink Scale)
        'tk-pink': {
          50: 'var(--tk-pink-50)',
          100: 'var(--tk-pink-100)',
          200: 'var(--tk-pink-200)',
          300: 'var(--tk-pink-300)',
          400: 'var(--tk-pink-400)',
          500: 'var(--tk-pink-500)', // Primary Pink Accent
          600: 'var(--tk-pink-600)',
          700: 'var(--tk-pink-700)',
          800: 'var(--tk-pink-800)',
          900: 'var(--tk-pink-900)',
        },

        // Other Accent Colors
        'tk-rose': {
          500: 'var(--tk-rose-500)',
          600: 'var(--tk-rose-600)',
        },
        'tk-teal': {
          500: 'var(--tk-teal-500)',
          600: 'var(--tk-teal-600)',
          700: 'var(--tk-teal-700)',
        },
        'tk-cyan': {
          50: 'var(--tk-cyan-50)',
          500: 'var(--tk-cyan-500)',
          600: 'var(--tk-cyan-600)',
        },

        // Semantic Colors
        'tk-green': {
          50: 'var(--tk-green-50)',
          100: 'var(--tk-green-100)',
          300: 'var(--tk-green-300)',
          400: 'var(--tk-green-400)',
          500: 'var(--tk-green-500)',
          700: 'var(--tk-green-700)',
          800: 'var(--tk-green-800)',
        },
        'tk-yellow': {
          50: 'var(--tk-yellow-50)',
          100: 'var(--tk-yellow-100)',
          300: 'var(--tk-yellow-300)',
          500: 'var(--tk-yellow-500)',
          700: 'var(--tk-yellow-700)',
        },
        'tk-blue': {
          50: 'var(--tk-blue-50)',
          100: 'var(--tk-blue-100)',
          300: 'var(--tk-blue-300)',
          400: 'var(--tk-blue-400)',
          500: 'var(--tk-blue-500)',
          600: 'var(--tk-blue-600)',
          700: 'var(--tk-blue-700)',
          800: 'var(--tk-blue-800)',
          900: 'var(--tk-blue-900)',
        },
        'tk-red': {
          100: 'var(--tk-red-100)',
          300: 'var(--tk-red-300)',
          600: 'var(--tk-red-600)',
          700: 'var(--tk-red-700)',
        },

        // Neutral Colors
        'tk-white': 'var(--tk-white)',
        'tk-black': 'var(--tk-black)',
        'tk-gray': {
          50: 'var(--tk-gray-50)',
          100: 'var(--tk-gray-100)',
          200: 'var(--tk-gray-200)',
          300: 'var(--tk-gray-300)',
          400: 'var(--tk-gray-400)',
          500: 'var(--tk-gray-500)',
          600: 'var(--tk-gray-600)',
          700: 'var(--tk-gray-700)',
          800: 'var(--tk-gray-800)',
          900: 'var(--tk-gray-900)',
        },

        // ShadCN UI theme variables
        'border': 'hsl(var(--border))',
        'input': 'hsl(var(--input))',
        'ring': 'hsl(var(--ring))',
        'background': 'hsl(var(--background))',
        'foreground': 'hsl(var(--foreground))',
        'primary': {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        'secondary': {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        'destructive': {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        'muted': {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        'accent': {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        'popover': {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        'card': {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
      },
      borderRadius: {
        lg: 'var(--radius)', // default 0.5rem (8px)
        md: 'calc(var(--radius) - 2px)', // 6px
        sm: 'calc(var(--radius) - 4px)', // 4px
      },
      keyframes: {
        'accordion-down': {
          from: { height: '0' },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: '0' },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
      },
    },
  },
  plugins: [require('tailwindcss-animate')],
} satisfies Config;

export default config;

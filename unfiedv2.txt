

## Part 1: Complete Drizzle ORM Schema (In-Depth)

This section defines the complete Drizzle ORM schema for all database tables and enums, including relations, as specified in `prdunifiedv18.txt`. Each element is given a short reference code for easy interlinking and management across documents.

**File Structure Assumption:**
*   `packages/db/schema/enums.ts`
*   `packages/db/schema/core.ts` (for `users`, `organizations`)
*   `packages/db/schema/rbac.ts` (for `organizationMembers`, `appRoles`, `appPermissions`, `rolePermissions`)
*   `packages/db/schema/saas.ts` (for `auditLogs`, `notifications`, `appApiKeys`)
*   `packages/db/schema/saas_crud_examples.ts` (for `appCollaborativeDocuments`, `appDocumentVersions`, `appDocumentComments`, `appProjects`, `appProjectMembers`, `appTasks`, `appTaskComments`)
*   `packages/db/schema/ai.ts` (for `aiJobs`, `aiMessages`, `aiUploadedFilesMetadata`, `aiModelConfigurations`, `aiGlobalSystemSettings`, `aiStreamTokens`)
*   `packages/db/schema/ed_data.ts` (for all `app_` prefixed educational tables)
*   `packages/db/schema/relations.ts` (for all Drizzle relations)
*   `packages/db/schema/index.ts` (barrel file exporting all)

---

### 1.1. Enums Definitions (`SCH-ENUM`)

**File:** `packages/db/schema/enums.ts`

```typescript
// SCH-ENUM: Core Enum Definitions
import { pgEnum } from 'drizzle-orm/pg-core';

// SCH-ENUM-APP-USER-ROLE: Application-specific roles for users within educational contexts
export const appUserRoleEnum = pgEnum('app_user_role_enum', [
  'teacher', 'student', 'admin', 'parent', 'guardian', 'aide', 'relative', 'proctor', 'system_support', 'observer'
]);

// SCH-ENUM-APP-ACAD-SESS-TYPE: Defines types of academic periods
export const appAcademicSessionTypeEnum = pgEnum('app_academic_session_type_enum', [
  'semester', 'schoolYear', 'gradingPeriod', 'term', 'summerSession', 'intersession'
]);

// SCH-ENUM-APP-ENTITY-STATUS: Defines statuses for various entities
export const appEntityStatusEnum = pgEnum('app_entity_status_enum', [
  'active', 'inactive', 'archived', 'pending', 'declined', 'deleted', 'tobedeleted', 'provisioned', 'completed', 'planned'
]);

// SCH-ENUM-APP-STREAM-ITEM-TYPE: Defines types of content items in a section's stream
export const appStreamItemTypeEnum = pgEnum('app_stream_item_type_enum', [
  'assignment', 'material', 'announcement', 'question'
]);

// SCH-ENUM-APP-SUBMISSION-STATE: Defines states for student submissions
export const appSubmissionStateEnum = pgEnum('app_submission_state_enum', [
  'new', 'created', 'submitted', 'returned', 'reclaimed_by_student', 'graded', 'late', 'missing'
]);

// SCH-ENUM-APP-INVITATION-STATUS: Defines statuses for invitations
export const appInvitationStatusEnum = pgEnum('app_invitation_status_enum', [
  'pending', 'accepted', 'declined', 'expired'
]);

// SCH-ENUM-APP-EXTERNAL-SYSTEM: Defines types of external systems
export const appExternalSystemEnum = pgEnum('app_external_system_enum', [
  'google_classroom', 'oneroster_webhook', 'lti_tool', 'custom_api'
]);

// SCH-ENUM-APP-SECTION-ALIAS-NAMESPACE: Defines namespaces for section aliases
export const appSectionAliasNamespaceEnum = pgEnum('app_section_alias_namespace_enum', [
  'DOMAIN', 'DEVELOPER_PROJECT'
]);

// SCH-ENUM-APP-ORG-TYPE: Defines types for organizational units
export const appOrgTypeEnum = pgEnum('app_org_type_enum', [
  'district', 'school', 'department', 'team', 'group', 'state', 'national', 'agency', 'consortium', 'other'
]);

// SCH-ENUM-APP-ONEROSTER-USER-STATUS: Defines specific user statuses from OneRoster
export const appOneRosterUserStatusEnum = pgEnum('app_one_roster_user_status_enum', [
  'active', 'tobedeleted', 'inactive'
]);

// SCH-ENUM-APP-ATTACHMENT-TYPE: Defines generic types for attachments
export const appAttachmentTypeEnum = pgEnum('app_attachment_type_enum', [
  'driveFile', 'youtubeVideo', 'link', 'form', 'add_on', 'file', 'image', 'video', 'audio', 'pdf', 'document', 'spreadsheet', 'presentation', 'other'
]);

// SCH-ENUM-APP-ASSIGNMENT-WORK-TYPE: Defines the type of work expected for an assignment
export const appAssignmentWorkTypeEnum = pgEnum('app_assignment_work_type_enum', [
  'ASSIGNMENT', 'SHORT_ANSWER_QUESTION', 'MULTIPLE_CHOICE_QUESTION', 'EXTERNAL_TOOL'
]);

// SCH-ENUM-APP-GLOBAL-ROLE: Defines application-specific global role for a user
export const appGlobalRoleEnum = pgEnum('app_global_role_enum', [
  'SuperAdmin', 'AppCreator', 'StandardUser'
]);

// SCH-ENUM-APP-API-KEY-STATUS: Defines the status of an API Key
export const appApiKeyStatusEnum = pgEnum('app_api_key_status_enum', ['active', 'revoked', 'expired']);

// SCH-ENUM-APP-SAAS-ENTITY-STATUS: Defines the status of a general SaaS entity
export const appSaaSEntityStatusEnum = pgEnum('app_saas_entity_status_enum', [
  'active', 'archived', 'pending', 'completed', 'on_hold', 'cancelled', 'draft', 'published'
]);

// SCH-ENUM-APP-PRIORITY: Defines priority levels
export const appPriorityEnum = pgEnum('app_priority_enum', ['low', 'medium', 'high', 'critical']);

// SCH-ENUM-AI-JOB-STATUS: Defines AI Chat Job Status
export const aiJobStatusEnum = pgEnum('ai_job_status_enum', [
  'active', 'archived', 'processing_user_input', 'awaiting_ai_stream_initiation', 'streaming_ai_response', 'completed_turn', 'failed_ai_response', 'stopped_by_user'
]);

// SCH-ENUM-AI-MESSAGE-ROLE: Defines AI Message Roles
export const aiMessageRoleEnum = pgEnum('ai_message_role_enum', ['user', 'model', 'system_info', 'error_info', 'tool_request', 'tool_response']);

// SCH-ENUM-AI-FILE-UPLOAD-STATUS: Defines AI File Upload Status
export const aiFileUploadStatusEnum = pgEnum('ai_file_upload_status_enum', [
  'pending_client_upload', 'uploading_to_storage', 'uploaded_to_storage', 'pending_processing', 'processing_text_extraction', 'processing_vision_analysis', 'processed_successfully', 'error_client_upload', 'error_storage_upload', 'error_processing'
]);

// SCH-ENUM-AI-MODEL-CONFIG-STATUS: Defines AI Model Configuration Status
export const aiModelConfigStatusEnum = pgEnum('ai_model_config_status_enum', ['active', 'beta', 'deprecated', 'restricted', 'disabled']);

// SCH-ENUM-AI-FEEDBACK-RATING: Defines AI Feedback Rating
export const aiFeedbackRatingEnum = pgEnum('ai_feedback_rating_enum', ['thumbs_up', 'thumbs_down']);
```

---

### 1.2. Core Application Tables (`users`, `organizations`) (`SCH-CORE-TBL`)

**File:** `packages/db/schema/core.ts`

```typescript
// SCH-CORE-TBL: Core Application Tables
import { pgTable, text, timestamp, jsonb, varchar, boolean, index, primaryKey, uniqueIndex } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import {
  appGlobalRoleEnum, appOneRosterUserStatusEnum, appOrgTypeEnum, appApiKeyStatusEnum
} from './enums'; // SCH-ENUM

// SCH-TBL-USERS: Users Table (Extended)
export const users = pgTable('users', {
  id: varchar('id', { length: 255 }).primaryKey(), // SCH-TBL-USERS-ID: Clerk User ID
  email: text('email').notNull().unique(),         // SCH-TBL-USERS-EMAIL: User's primary email
  name: text('name'),                            // SCH-TBL-USERS-NAME: User's full display name
  profileImageUrl: text('profile_image_url'),    // SCH-TBL-USERS-PROFILE-IMG: URL to user's profile image

  appGlobalRole: appGlobalRoleEnum('app_global_role').default('StandardUser').notNull(), // SCH-TBL-USERS-APP-GLOBAL-ROLE: Global app role (e.g., SuperAdmin)

  givenName: text('given_name'),                   // SCH-TBL-USERS-GIVEN-NAME: User's first name (OneRoster)
  familyName: text('family_name'),                 // SCH-TBL-USERS-FAMILY-NAME: User's last name (OneRoster)
  middleName: text('middle_name'),                 // SCH-TBL-USERS-MIDDLE-NAME: User's middle name (OneRoster)
  usernameApp: text('username_app'),               // SCH-TBL-USERS-USERNAME-APP: App-specific username or OneRoster username
  phone: text('phone'),                            // SCH-TBL-USERS-PHONE: User's phone number (OneRoster)

  userProfileJsonb: jsonb('user_profile_jsonb').$type<{ // SCH-TBL-USERS-PROFILE-JSONB: Flexible app-specific profile data
    bio?: string | null;
    socialLinks?: { linkedin?: string | null; twitter?: string | null; github?: string | null; website?: string | null } | null;
    jobTitle?: string | null;
    pronouns?: string | null;
    location?: string | null;
  }>().default({}),

  preferencesJsonb: jsonb('preferences_jsonb').$type<{ // SCH-TBL-USERS-PREFS-JSONB: User-configurable app preferences
    theme?: 'light' | 'dark' | 'system' | null;
    language?: string | null;
    timezone?: string | null;
    notifications?: {
      emailEnabled?: boolean | null;
      inAppEnabled?: boolean | null;
      digestFrequency?: 'daily' | 'weekly' | 'never' | null;
    } | null;
    aiChatDefaults?: {
      defaultModelSlug?: string | null;
      sendOnEnter?: boolean | null;
      autoScrollChat?: boolean | null;
    } | null;
  }>().default({ theme: 'system', notifications: { emailEnabled: true, inAppEnabled: true, digestFrequency: 'daily' } }),

  apiKeysJsonb: jsonb('api_keys_jsonb').$type<Array<{ // SCH-TBL-USERS-API-KEYS-JSONB: (Alternative to app_api_keys table)
    keyId: string;
    keyHash: string;
    prefix: string;
    label: string;
    scopes: string[];
    createdAt: string;
    lastUsedAt?: string | null;
    expiresAt?: string | null;
    status: 'active' | 'revoked' | 'expired';
    ipRestrictions?: string[] | null;
  }>>().default([]),

  // SCH-TBL-USERS-ONEROSTER-SOURCED-ID: OneRoster User.sourcedId
  onerosterUserSourcedId: varchar('oneroster_user_sourced_id', { length: 255 }).unique(),
  // SCH-TBL-USERS-ONEROSTER-STATUS: OneRoster User.status
  onerosterUserStatus: appOneRosterUserStatusEnum('oneroster_user_status'),
  // SCH-TBL-USERS-ONEROSTER-LAST-MOD: OneRoster User.dateLastModified
  onerosterUserDateLastModified: timestamp('oneroster_user_date_last_modified', { withTimezone: true, mode: 'date' }),

  googleUserId: varchar('google_user_id', { length: 255 }).unique(), // SCH-TBL-USERS-GOOGLE-ID: Google User Profile ID

  sourceSpecificMetadataJsonb: jsonb('source_specific_metadata_jsonb').$type<Record<string, any>>().default({}), // SCH-TBL-USERS-SOURCE-META: Flexible field for additional source-specific data

  appCreatedAt: timestamp('app_created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-USERS-APP-CREATED-AT
  appUpdatedAt: timestamp('app_updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-USERS-APP-UPDATED-AT
  appLastActivityAt: timestamp('app_last_activity_at', { withTimezone: true, mode: 'date' }), // SCH-TBL-USERS-APP-LAST-ACTIVITY
  appLastLoginAt: timestamp('app_last_login_at', { withTimezone: true, mode: 'date' }),     // SCH-TBL-USERS-APP-LAST-LOGIN
}, (table) => {
  return {
    emailIdx: index('user_email_idx').on(table.email),
    appGlobalRoleIdx: index('user_app_global_role_idx').on(table.appGlobalRole),
    onerosterSourcedIdIdx: uniqueIndex('user_oneroster_sourced_id_idx').on(table.onerosterUserSourcedId).nullsNotDistinct(),
    googleUserIdIdx: uniqueIndex('user_google_user_id_idx').on(table.googleUserId).nullsNotDistinct(),
    usernameAppIdx: index('user_username_app_idx').on(table.usernameApp).nullsNotDistinct(),
  };
});

// SCH-TBL-ORGS: Organizations Table (Extended)
export const organizations = pgTable('organizations', {
  id: varchar('id', { length: 255 }).primaryKey(), // SCH-TBL-ORGS-ID: Clerk Organization ID
  name: text('name').notNull(),                    // SCH-TBL-ORGS-NAME: Organization's display name
  slug: varchar('slug', { length: 255 }).notNull().unique(), // SCH-TBL-ORGS-SLUG: Application-generated URL-friendly unique identifier
  imageUrl: text('image_url'),                   // SCH-TBL-ORGS-IMG-URL: URL to organization's logo
  createdByClerkUserId: varchar('created_by_clerk_user_id', { length: 255 }), // SCH-TBL-ORGS-CREATED-BY: Clerk User ID of the creator

  appSettingsJsonb: jsonb('app_settings_jsonb').$type<{ // SCH-TBL-ORGS-APP-SETTINGS-JSONB: App-specific settings for this organization
    defaultAiModelSlug?: string | null;
    defaultLocale?: string | null;
    defaultTimezone?: string | null;
    featureFlags?: Record<string, boolean>;
    branding?: { primaryColor?: string; secondaryColor?: string; appFont?: string; appLogoOverrideUrl?: string };
    dataRetentionPolicyDays?: number | null;
    aiChatOrgContextFileIds?: string[];
  }>().default({}),

  subscriptionJsonb: jsonb('subscription_jsonb').$type<{ // SCH-TBL-ORGS-SUB-JSONB: Stubs for billing provider integration
    provider?: 'stripe' | 'paddle' | 'none' | string;
    providerCustomerId?: string | null;
    providerSubscriptionId?: string | null;
    planId?: string;
    status?: 'active' | 'trialing' | 'past_due' | 'canceled' | 'incomplete' | 'unpaid' | 'paused';
    currentPeriodStart?: string;
    currentPeriodEnd?: string;
    trialEndsAt?: string | null;
    cancelAtPeriodEnd?: boolean;
    canceledAt?: string | null;
    usageLimits?: {
      maxUsers?: number;
      maxAiPromptTokensPerMonth?: number;
      maxAiCompletionTokensPerMonth?: number;
      maxStorageGb?: number;
      maxActiveSections?: number;
      maxStudentsPerOrg?: number;
    };
    currentUsage?: {
      activeUsers?: number;
      aiPromptTokensThisPeriod?: number;
      aiCompletionTokensThisPeriod?: number;
      storageUsedGb?: number;
      activeSections?: number;
      studentsEnrolled?: number;
    };
  }>(),

  statsJsonb: jsonb('stats_jsonb').$type<{ // SCH-TBL-ORGS-STATS-JSONB: Denormalized counters for quick display
    activeAppMemberCount?: number;
    projectCount?: number;
    documentCount?: number;
    courseCount?: number;
    sectionCount?: number;
    teacherCount?: number;
    studentCount?: number;
    totalStorageUsedBytes?: number;
  }>(),

  orgType: appOrgTypeEnum('org_type'), // SCH-TBL-ORGS-ORG-TYPE: Type of organization (e.g., 'district', 'school')
  externalIdentifier: text('external_identifier'), // SCH-TBL-ORGS-EXT-ID: Human-readable external ID (e.g., NCES ID)
  parentAppOrganizationId: varchar('parent_app_organization_id', { length: 255 }), // SCH-TBL-ORGS-PARENT-ID: FK to self for hierarchical app organizations

  onerosterOrgSourcedId: varchar('oneroster_org_sourced_id', { length: 255 }).unique(), // SCH-TBL-ORGS-ONEROSTER-SOURCED-ID: OneRoster Organization.sourcedId
  onerosterOrgParentSourcedId: varchar('oneroster_org_parent_sourced_id', { length: 255 }), // SCH-TBL-ORGS-ONEROSTER-PARENT-SOURCED-ID: OneRoster Organization.parent.sourcedId

  googleDomain: text('google_domain'), // SCH-TBL-ORGS-GOOGLE-DOMAIN: Google Workspace domain linked to this organization
  googleCustomerId: text('google_customer_id'), // SCH-TBL-ORGS-GOOGLE-CUSTOMER-ID: Google Workspace Customer ID

  sourceSpecificMetadataJsonb: jsonb('source_specific_metadata_jsonb').$type<Record<string, any>>(), // SCH-TBL-ORGS-SOURCE-META: Flexible field for additional source-specific data

  appCreatedAt: timestamp('app_created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-ORGS-APP-CREATED-AT
  appUpdatedAt: timestamp('app_updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-ORGS-APP-UPDATED-AT
}, (table) => {
  return {
    slugIdx: uniqueIndex('org_slug_idx').on(table.slug),
    onerosterOrgSourcedIdIdx: uniqueIndex('org_oneroster_sourced_id_idx').on(table.onerosterOrgSourcedId).nullsNotDistinct(),
    googleDomainIdx: uniqueIndex('org_google_domain_idx').on(table.googleDomain).nullsNotDistinct(),
    orgTypeIdx: index('org_org_type_idx').on(table.orgType),
    parentAppOrganizationIdIdx: index('org_parent_app_org_id_idx').on(table.parentAppOrganizationId),
  };
});
```

---

### 1.3. Application-Specific Role-Based Access Control (RBAC) Tables (`SCH-RBAC-TBL`)

**File:** `packages/db/schema/rbac.ts`

```typescript
// SCH-RBAC-TBL: Application-Specific Role-Based Access Control (RBAC) Tables
import { pgTable, text, timestamp, serial, jsonb, varchar, index, primaryKey, uniqueIndex, integer, boolean } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { users, organizations } from './core'; // SCH-CORE-TBL

// SCH-TBL-ORG-MEM: organization_members Table (Application-Level Membership & Roles)
export const organizationMembers = pgTable('organization_members', {
  userId: varchar('user_id', { length: 255 }).references(() => users.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-ORG-MEM-USER-ID
  organizationId: varchar('organization_id', { length: 255 }).references(() => organizations.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-ORG-MEM-ORG-ID

  appRoleSlugs: jsonb('app_role_slugs').$type<string[]>().default([]).notNull(), // SCH-TBL-ORG-MEM-APP-ROLE-SLUGS: Array of app-specific role slugs

  appMembershipStatus: text('app_membership_status').default('active_app_access').notNull(), // SCH-TBL-ORG-MEM-STATUS

  appJoinedAt: timestamp('app_joined_at', { withTimezone: true, mode: 'date' }).defaultNow(), // SCH-TBL-ORG-MEM-JOINED-AT
  appLastAccessedOrgAt: timestamp('app_last_accessed_org_at', { withTimezone: true, mode: 'date' }), // SCH-TBL-ORG-MEM-LAST-ACCESSED

  appMembershipCreatedAt: timestamp('app_membership_created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-ORG-MEM-CREATED-AT
  appMembershipUpdatedAt: timestamp('app_membership_updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-ORG-MEM-UPDATED-AT
}, (table) => {
  return {
    pk: primaryKey({ columns: [table.userId, table.organizationId] }),
    userIdIdx: index('org_member_user_id_idx').on(table.userId),
    organizationIdIdx: index('org_member_organization_id_idx').on(table.organizationId),
  };
});

// SCH-TBL-APP-ROLES: app_roles Table (Defines Application-Specific Roles)
export const appRoles = pgTable('app_roles', {
  id: serial('id').primaryKey(), // SCH-TBL-APP-ROLES-ID
  roleSlug: varchar('role_slug', { length: 100 }).notNull().unique(), // SCH-TBL-APP-ROLES-SLUG: Programmatic, unique name
  displayName: text('display_name').notNull(), // SCH-TBL-APP-ROLES-DISPLAY-NAME: User-friendly name
  description: text('description'), // SCH-TBL-APP-ROLES-DESC

  scope: text('scope').default('organization').notNull(), // SCH-TBL-APP-ROLES-SCOPE: 'organization', 'system', 'project'
  isSystemDefined: boolean('is_system_defined').default(true).notNull(), // SCH-TBL-APP-ROLES-IS-SYSTEM-DEF
  category: text('category'), // SCH-TBL-APP-ROLES-CATEGORY

  appCreatedAt: timestamp('app_created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-APP-ROLES-CREATED-AT
  appUpdatedAt: timestamp('app_updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-APP-ROLES-UPDATED-AT
}, (table) => {
  return {
    roleSlugIdx: uniqueIndex('app_role_slug_idx').on(table.roleSlug),
    scopeCategoryIdx: index('app_role_scope_category_idx').on(table.scope, table.category),
  };
});

// SCH-TBL-APP-PERM: app_permissions Table (Defines Granular Application Permissions)
export const appPermissions = pgTable('app_permissions', {
  id: serial('id').primaryKey(), // SCH-TBL-APP-PERM-ID
  permissionSlug: varchar('permission_slug', { length: 150 }).notNull().unique(), // SCH-TBL-APP-PERM-SLUG: Programmatic, unique name
  description: text('description').notNull(), // SCH-TBL-APP-PERM-DESC
  category: text('category').notNull(), // SCH-TBL-APP-PERM-CATEGORY

  appCreatedAt: timestamp('app_created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-APP-PERM-CREATED-AT
  appUpdatedAt: timestamp('app_updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-APP-PERM-UPDATED-AT
}, (table) => {
  return {
    permissionSlugIdx: uniqueIndex('app_permission_slug_idx').on(table.permissionSlug),
    categoryIdx: index('app_permission_category_idx').on(table.category),
  };
});

// SCH-TBL-ROLE-PERM: role_permissions Join Table (Maps app_roles to app_permissions)
export const rolePermissions = pgTable('role_permissions', {
  roleId: integer('role_id').references(() => appRoles.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-ROLE-PERM-ROLE-ID
  permissionId: integer('permission_id').references(() => appPermissions.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-ROLE-PERM-PERM-ID
}, (table) => {
  return {
    pk: primaryKey({ columns: [table.roleId, table.permissionId] }),
  };
});
```

---

### 1.4. General SaaS Support Tables (`SCH-SAAS-TBL`)

**File:** `packages/db/schema/saas.ts`

```typescript
// SCH-SAAS-TBL: General SaaS Support Tables
import { pgTable, text, timestamp, serial, jsonb, varchar, index, uniqueIndex } from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { users, organizations } from './core'; // SCH-CORE-TBL
import { appApiKeyStatusEnum } from './enums'; // SCH-ENUM

// SCH-TBL-AUDIT: audit_logs Table
export const auditLogs = pgTable('audit_logs', {
  id: serial('id').primaryKey(), // SCH-TBL-AUDIT-ID

  organizationId: varchar('organization_id', { length: 255 }).references(() => organizations.id, { onDelete: 'set null' }), // SCH-TBL-AUDIT-ORG-ID
  actorUserId: varchar('actor_user_id', { length: 255 }).references(() => users.id, { onDelete: 'set null' }), // SCH-TBL-AUDIT-ACTOR-ID
  actorUserDisplayNameSnapshot: text('actor_user_display_name_snapshot'), // SCH-TBL-AUDIT-ACTOR-NAME-SNAP
  actorUserEmailSnapshot: text('actor_user_email_snapshot'), // SCH-TBL-AUDIT-ACTOR-EMAIL-SNAP

  impersonatorUserId: varchar('impersonator_user_id', { length: 255 }).references(() => users.id, { onDelete: 'set null' }), // SCH-TBL-AUDIT-IMPERSONATOR-ID

  actionSlug: varchar('action_slug', { length: 255 }).notNull(), // SCH-TBL-AUDIT-ACTION-SLUG
  targetResource: jsonb('target_resource').$type<{ // SCH-TBL-AUDIT-TARGET-RES
    type?: string | null;
    id?: string | null;
    name?: string | null;
  }>().default({}),

  changeDetails: jsonb('change_details').$type<{ // SCH-TBL-AUDIT-CHANGE-DETAILS
    before?: Record<string, any> | null;
    after?: Record<string, any> | null;
    diffSummary?: string | null;
    fieldsUpdated?: string[] | null;
  }>().default({}),

  ipAddress: varchar('ip_address', { length: 100 }), // SCH-TBL-AUDIT-IP
  userAgent: text('user_agent'),                   // SCH-TBL-AUDIT-USER-AGENT

  status: text('status').default('success').notNull(), // SCH-TBL-AUDIT-STATUS
  failureReason: text('failure_reason'),              // SCH-TBL-AUDIT-FAIL-REASON

  clientContext: jsonb('client_context').$type<{ // SCH-TBL-AUDIT-CLIENT-CTX
    appVersion?: string | null;
    routeName?: string | null;
    clientRequestId?: string | null;
  }>().default({}),

  sessionId: varchar('session_id', { length: 255 }), // SCH-TBL-AUDIT-SESSION-ID
  traceId: varchar('trace_id', { length: 255 }),    // SCH-TBL-AUDIT-TRACE-ID

  eventTimestamp: timestamp('event_timestamp', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-AUDIT-TIMESTAMP
}, (table) => {
  return {
    organizationIdIdx: index('audit_log_org_id_idx').on(table.organizationId).nullsLast(),
    actorUserIdIdx: index('audit_log_actor_user_id_idx').on(table.actorUserId).nullsLast(),
    actionSlugIdx: index('audit_log_action_slug_idx').on(table.actionSlug),
    eventTimestampIdx: index('audit_log_event_timestamp_idx').on(table.eventTimestamp).desc(),
    targetResourceTypeIdx: index('audit_log_target_res_type_idx').on(sql`(target_resource->>'type')`),
    targetResourceIdIdx: index('audit_log_target_res_id_idx').on(sql`(target_resource->>'id')`),
    statusIdx: index('audit_log_status_idx').on(table.status),
    impersonatorUserIdIdx: index('audit_log_impersonator_user_id_idx').on(table.impersonatorUserId).nullsLast(),
  };
});

// SCH-TBL-NOTIF: notifications Table
export const notifications = pgTable('notifications', {
  id: serial('id').primaryKey(), // SCH-TBL-NOTIF-ID

  recipientUserId: varchar('recipient_user_id', { length: 255 }).references(() => users.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-NOTIF-RECIPIENT-ID
  organizationId: varchar('organization_id', { length: 255 }).references(() => organizations.id, { onDelete: 'cascade' }), // SCH-TBL-NOTIF-ORG-ID

  typeSlug: varchar('type_slug', { length: 100 }).notNull(), // SCH-TBL-NOTIF-TYPE-SLUG
  titleLocalizationKey: text('title_localization_key').notNull(), // SCH-TBL-NOTIF-TITLE-KEY
  messageLocalizationKey: text('message_localization_key').notNull(), // SCH-TBL-NOTIF-MSG-KEY
  messagePayloadJsonb: jsonb('message_payload_jsonb').$type<Record<string, string | number | boolean>>(), // SCH-TBL-NOTIF-MSG-PAYLOAD

  actionLink: text('action_link'),               // SCH-TBL-NOTIF-ACTION-LINK
  actionTextLocalizationKey: text('action_text_localization_key'), // SCH-TBL-NOTIF-ACTION-TEXT-KEY

  isRead: boolean('is_read').default(false).notNull(), // SCH-TBL-NOTIF-IS-READ
  readAt: timestamp('read_at', { withTimezone: true, mode: 'date' }), // SCH-TBL-NOTIF-READ-AT
  isArchived: boolean('is_archived').default(false).notNull(), // SCH-TBL-NOTIF-IS-ARCHIVED
  archivedAt: timestamp('archived_at', { withTimezone: true, mode: 'date' }), // SCH-TBL-NOTIF-ARCHIVED-AT

  priority: text('priority').default('medium'), // SCH-TBL-NOTIF-PRIORITY
  expiresAt: timestamp('expires_at', { withTimezone: true, mode: 'date' }), // SCH-TBL-NOTIF-EXPIRES-AT

  senderInfoJsonb: jsonb('sender_info_jsonb').$type<{ // SCH-TBL-NOTIF-SENDER-INFO
    type: "user" | "system" | "ai_model" | "organization_event";
    id?: string | null;
    name?: string | null;
    avatarUrl?: string | null;
  }>().default({type: 'system'}),

  metadataJsonb: jsonb('metadata_jsonb').$type<Record<string, any>>().default({}), // SCH-TBL-NOTIF-METADATA

  createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-NOTIF-CREATED-AT
}, (table) => {
  return {
    recipientIsReadCreatedIdx: index('notif_recipient_read_created_idx').on(table.recipientUserId, table.isRead, table.createdAt).desc(table.createdAt),
    recipientUserIdIdx: index('notif_recipient_user_id_idx').on(table.recipientUserId),
    organizationIdIdx: index('notif_org_id_idx').on(table.organizationId).nullsLast(),
    typeSlugIdx: index('notif_type_slug_idx').on(table.typeSlug),
    expiresAtIdx: index('notif_expires_at_idx').on(table.expiresAt).nullsLast(),
  };
});

// SCH-TBL-API-KEYS: app_api_keys Table (If custom application-specific API keys are implemented)
export const appApiKeys = pgTable('app_api_keys', {
  id: serial('id').primaryKey(), // SCH-TBL-API-KEYS-ID
  keyId: varchar('key_id', {length: 100}).notNull().unique(), // SCH-TBL-API-KEYS-KEY-ID

  keyHash: varchar('key_hash', { length: 255 }).notNull(), // SCH-TBL-API-KEYS-KEY-HASH
  keyPrefix: varchar('key_prefix', { length: 20 }).notNull(), // SCH-TBL-API-KEYS-KEY-PREFIX

  label: text('label').notNull(), // SCH-TBL-API-KEYS-LABEL
  scopes: jsonb('scopes').$type<string[]>().default([]).notNull(), // SCH-TBL-API-KEYS-SCOPES

  userId: varchar('user_id', { length: 255 }).references(() => users.id, { onDelete: 'cascade' }), // SCH-TBL-API-KEYS-USER-ID
  organizationId: varchar('organization_id', { length: 255 }).references(() => organizations.id, { onDelete: 'cascade' }), // SCH-TBL-API-KEYS-ORG-ID

  status: appApiKeyStatusEnum('status').default('active').notNull(), // SCH-TBL-API-KEYS-STATUS

  expiresAt: timestamp('expires_at', { withTimezone: true, mode: 'date' }), // SCH-TBL-API-KEYS-EXPIRES-AT
  lastUsedAt: timestamp('last_used_at', { withTimezone: true, mode: 'date' }), // SCH-TBL-API-KEYS-LAST-USED-AT
  ipRestrictionsJsonb: jsonb('ip_restrictions_jsonb').$type<string[]>().default([]), // SCH-TBL-API-KEYS-IP-RESTRICTIONS

  createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-API-KEYS-CREATED-AT
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-API-KEYS-UPDATED-AT
}, (table) => {
  return {
    keyIdIdx: uniqueIndex('app_api_key_key_id_idx').on(table.keyId),
    userIdIdx: index('app_api_key_user_id_idx').on(table.userId).nullsLast(),
    organizationIdIdx: index('app_api_key_org_id_idx').on(table.organizationId).nullsLast(),
  };
});
```

---

### 1.5. Example General-Purpose CRUD Entity Tables (`SCH-SAAS-CRUD-TBL`)

**File:** `packages/db/schema/saas_crud_examples.ts`

```typescript
// SCH-SAAS-CRUD-TBL: Example General-Purpose CRUD Entity Tables
import { pgTable, text, timestamp, serial, jsonb, varchar, integer, index, uuid, bigint } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { organizations, users } from './core'; // SCH-CORE-TBL
import { appSaaSEntityStatusEnum, appPriorityEnum } from './enums'; // SCH-ENUM

// SCH-TBL-DOCS: app_collaborative_documents Table
export const appCollaborativeDocuments = pgTable('app_collaborative_documents', {
  id: serial('id').primaryKey(), // SCH-TBL-DOCS-ID

  organizationId: varchar('organization_id', { length: 255 }).references(() => organizations.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-DOCS-ORG-ID

  title: text('title').notNull(), // SCH-TBL-DOCS-TITLE
  description: text('description'), // SCH-TBL-DOCS-DESC

  contentJson: jsonb('content_json').default('{}'), // SCH-TBL-DOCS-CONTENT-JSON

  ownerUserId: varchar('user_id', { length: 255 }).references(() => users.id, { onDelete: 'set null' }).notNull(), // SCH-TBL-DOCS-OWNER-ID

  status: appSaaSEntityStatusEnum('status').default('draft').notNull(), // SCH-TBL-DOCS-STATUS

  lastUpdatedByUserId: varchar('last_updated_by_user_id', { length: 255 }).references(() => users.id, { onDelete: 'set null' }), // SCH-TBL-DOCS-LAST-UPDATED-BY

  tagsJsonb: jsonb('tags_jsonb').$type<string[]>().default([]), // SCH-TBL-DOCS-TAGS

  accessControlJsonb: jsonb('access_control_jsonb').$type<{ // SCH-TBL-DOCS-ACCESS-CONTROL
    visibility: 'private_to_owner_and_explicit_shares' | 'organization_members_can_view' | 'organization_members_can_comment' | 'organization_members_can_edit';
    sharedWithUsers?: Array<{
      userId: string;
      permissionLevel: 'view' | 'comment' | 'edit' | 'manage_sharing';
      sharedByUserId: string;
      sharedAt: string;
    }> | null;
  }>().default({ visibility: 'private_to_owner_and_explicit_shares' }),

  currentVersionNumber: integer('current_version_number').default(1).notNull(), // SCH-TBL-DOCS-CURRENT-VER

  wordCount: integer('word_count').default(0), // SCH-TBL-DOCS-WORD-COUNT
  characterCount: integer('character_count').default(0), // SCH-TBL-DOCS-CHAR-COUNT

  createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-DOCS-CREATED-AT
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-DOCS-UPDATED-AT
  lastSignificantSaveAt: timestamp('last_significant_save_at', { withTimezone: true, mode: 'date' }), // SCH-TBL-DOCS-LAST-SIG-SAVE
}, (table) => {
  return {
    organizationIdIdx: index('app_doc_org_id_idx').on(table.organizationId),
    ownerUserIdIdx: index('app_doc_owner_user_id_idx').on(table.ownerUserId),
    statusIdx: index('app_doc_status_idx').on(table.status),
    titleSearchIdx: index('app_doc_title_search_idx').on(table.title),
  };
});

// SCH-TBL-DOC-VER: app_document_versions Table
export const appDocumentVersions = pgTable('app_document_versions', {
  id: serial('id').primaryKey(), // SCH-TBL-DOC-VER-ID
  documentId: integer('document_id').references(() => appCollaborativeDocuments.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-DOC-VER-DOC-ID

  versionNumber: integer('version_number').notNull(), // SCH-TBL-DOC-VER-NUMBER

  contentJsonSnapshot: jsonb('content_json_snapshot').notNull(), // SCH-TBL-DOC-VER-CONTENT-SNAP

  savedByUserId: varchar('saved_by_user_id', { length: 255 }).references(() => users.id, { onDelete: 'set null' }), // SCH-TBL-DOC-VER-SAVED-BY

  savedAt: timestamp('saved_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-DOC-VER-SAVED-AT

  changeSummary: text('change_summary'), // SCH-TBL-DOC-VER-CHANGE-SUMMARY
}, (table) => {
  return {
    documentIdVersionNumberUniqueIdx: index('app_doc_ver_doc_id_ver_num_idx').on(table.documentId, table.versionNumber).unique(),
    documentIdIdx: index('app_doc_ver_doc_id_idx').on(table.documentId),
    savedByUserIdIdx: index('app_doc_ver_saved_by_idx').on(table.savedByUserId),
  };
});

// SCH-TBL-DOC-COMM: app_document_comments Table
export const appDocumentComments = pgTable('app_document_comments', {
  id: serial('id').primaryKey(), // SCH-TBL-DOC-COMM-ID
  documentId: integer('document_id').references(() => appCollaborativeDocuments.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-DOC-COMM-DOC-ID

  userId: varchar('user_id', { length: 255 }).references(() => users.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-DOC-COMM-USER-ID

  content: text('content').notNull(), // SCH-TBL-DOC-COMM-CONTENT

  selectionAnchorPathJsonb: jsonb('selection_anchor_path_jsonb').$type<any>(), // SCH-TBL-DOC-COMM-ANCHOR-PATH

  threadId: integer('thread_id'), // SCH-TBL-DOC-COMM-THREAD-ID
  parentCommentId: integer('parent_comment_id'), // SCH-TBL-DOC-COMM-PARENT-ID

  status: text('status').default('active').notNull(), // SCH-TBL-DOC-COMM-STATUS
  resolvedByUserId: varchar('resolved_by_user_id', { length: 255 }).references(() => users.id, { onDelete: 'set null' }), // SCH-TBL-DOC-COMM-RESOLVED-BY
  resolvedAt: timestamp('resolved_at', { withTimezone: true, mode: 'date' }), // SCH-TBL-DOC-COMM-RESOLVED-AT

  reactionsJsonb: jsonb('reactions_jsonb').$type<Record<string, string[]>>().default({}), // SCH-TBL-DOC-COMM-REACTIONS

  createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-DOC-COMM-CREATED-AT
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-DOC-COMM-UPDATED-AT
}, (table) => {
  return {
    documentIdIdx: index('app_doc_comment_doc_id_idx').on(table.documentId),
    userIdIdx: index('app_doc_comment_user_id_idx').on(table.userId),
    threadIdIdx: index('app_doc_comment_thread_id_idx').on(table.threadId).nullsLast(),
    parentCommentIdIdx: index('app_doc_comment_parent_id_idx').on(table.parentCommentId).nullsLast(),
  };
});

// SCH-TBL-PROJ: app_projects Table
export const appProjects = pgTable('app_projects', {
  id: serial('id').primaryKey(), // SCH-TBL-PROJ-ID
  organizationId: varchar('organization_id', { length: 255 }).references(() => organizations.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-PROJ-ORG-ID
  name: text('name').notNull(), // SCH-TBL-PROJ-NAME
  description: text('description'), // SCH-TBL-PROJ-DESC
  status: appSaaSEntityStatusEnum('status').default('active').notNull(), // SCH-TBL-PROJ-STATUS
  startDate: timestamp('start_date', { mode: 'date', withTimezone: false }), // SCH-TBL-PROJ-START-DATE
  endDate: timestamp('end_date', { mode: 'date', withTimezone: false }), // SCH-TBL-PROJ-END-DATE
  ownerUserId: varchar('owner_user_id', { length: 255 }).references(() => users.id, { onDelete: 'set null' }).notNull(), // SCH-TBL-PROJ-OWNER-ID
  projectSettingsJsonb: jsonb('project_settings_jsonb').$type<Record<string, any>>(), // SCH-TBL-PROJ-SETTINGS
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-PROJ-CREATED-AT
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-PROJ-UPDATED-AT
}, (table) => {
  return {
    organizationIdIdx: index('app_proj_org_id_idx').on(table.organizationId),
    ownerUserIdIdx: index('app_proj_owner_user_id_idx').on(table.ownerUserId),
    statusIdx: index('app_proj_status_idx').on(table.status),
    nameSearchIdx: index('app_proj_name_search_idx').on(table.name),
  };
});

// SCH-TBL-PROJ-MEM: app_project_members Table
export const appProjectMembers = pgTable('app_project_members', {
  projectId: serial('project_id').references(() => appProjects.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-PROJ-MEM-PROJ-ID
  userId: varchar('user_id', { length: 255 }).references(() => users.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-PROJ-MEM-USER-ID
  projectRole: text('project_role'), // SCH-TBL-PROJ-MEM-ROLE
  assignedAt: timestamp('assigned_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-PROJ-MEM-ASSIGNED-AT
}, (table) => {
  return {
    pk: primaryKey({ columns: [table.projectId, table.userId] }),
    projectIdIdx: index('app_proj_mem_proj_id_idx').on(table.projectId),
    userIdIdx: index('app_proj_mem_user_id_idx').on(table.userId),
  };
});

// SCH-TBL-TASK: app_tasks Table
export const appTasks = pgTable('app_tasks', {
  id: serial('id').primaryKey(), // SCH-TBL-TASK-ID
  projectId: integer('project_id').references(() => appProjects.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-TASK-PROJ-ID
  organizationId: varchar('organization_id', { length: 255 }).references(() => organizations.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-TASK-ORG-ID
  title: text('title').notNull(), // SCH-TBL-TASK-TITLE
  description: text('description'), // SCH-TBL-TASK-DESC
  status: appSaaSEntityStatusEnum('status').default('pending').notNull(), // SCH-TBL-TASK-STATUS
  assigneeUserId: varchar('assignee_user_id', { length: 255 }).references(() => users.id, { onDelete: 'set null' }), // SCH-TBL-TASK-ASSIGNEE-ID
  reporterUserId: varchar('reporter_user_id', { length: 255 }).references(() => users.id, { onDelete: 'set null' }).notNull(), // SCH-TBL-TASK-REPORTER-ID
  priority: appPriorityEnum('priority').default('medium'), // SCH-TBL-TASK-PRIORITY
  dueDate: timestamp('due_date', { mode: 'date', withTimezone: false }), // SCH-TBL-TASK-DUE-DATE
  storyPoints: integer('story_points'), // SCH-TBL-TASK-STORY-POINTS
  tagsJsonb: jsonb('tags_jsonb').$type<string[]>().default([]), // SCH-TBL-TASK-TAGS
  attachmentsJsonb: jsonb('attachments_jsonb').$type<Array<{ // SCH-TBL-TASK-ATTACHMENTS
    fileName: string;
    filePath: string;
    fileType: string;
    uploadedByUserId: string;
    uploadedAt: string;
  }>>().default([]),
  parentTaskId: integer('parent_task_id'), // SCH-TBL-TASK-PARENT-ID
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-TASK-CREATED-AT
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-TASK-UPDATED-AT
}, (table) => {
  return {
    projectIdIdx: index('app_task_proj_id_idx').on(table.projectId),
    organizationIdIdx: index('app_task_org_id_idx').on(table.organizationId),
    assigneeUserIdIdx: index('app_task_assignee_id_idx').on(table.assigneeUserId),
    statusIdx: index('app_task_status_idx').on(table.status),
    priorityIdx: index('app_task_priority_idx').on(table.priority),
    dueDateIdx: index('app_task_due_date_idx').on(table.dueDate),
    parentTaskIdIdx: index('app_task_parent_id_idx').on(table.parentTaskId).nullsLast(),
  };
});

// SCH-TBL-TASK-COMM: app_task_comments Table
export const appTaskComments = pgTable('app_task_comments', {
  id: serial('id').primaryKey(), // SCH-TBL-TASK-COMM-ID
  taskId: integer('task_id').references(() => appTasks.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-TASK-COMM-TASK-ID
  userId: varchar('user_id', { length: 255 }).references(() => users.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-TASK-COMM-USER-ID
  content: text('content').notNull(), // SCH-TBL-TASK-COMM-CONTENT
  threadId: integer('thread_id'), // SCH-TBL-TASK-COMM-THREAD-ID
  parentCommentId: integer('parent_comment_id'), // SCH-TBL-TASK-COMM-PARENT-ID
  status: text('status').default('active').notNull(), // SCH-TBL-TASK-COMM-STATUS
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-TASK-COMM-CREATED-AT
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-TASK-COMM-UPDATED-AT
}, (table) => {
  return {
    taskIdIdx: index('app_task_comment_task_id_idx').on(table.taskId),
    userIdIdx: index('app_task_comment_user_id_idx').on(table.userId),
    threadIdIdx: index('app_task_comment_thread_id_idx').on(table.threadId).nullsLast(),
    parentCommentIdIdx: index('app_task_comment_parent_id_idx').on(table.parentCommentId).nullsLast(),
  };
});
```

---

### 1.6. AI Chat Application Data Tables (`SCH-AI-TBL`)

**File:** `packages/db/schema/ai.ts`

```typescript
// SCH-AI-TBL: AI Chat Application Data Tables
import { pgTable, text, timestamp, jsonb, varchar, serial, index, integer, boolean, bigint, uniqueIndex } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { users, organizations } from './core'; // SCH-CORE-TBL
import {
  aiJobStatusEnum, aiMessageRoleEnum, aiFileUploadStatusEnum, aiModelConfigStatusEnum, aiFeedbackRatingEnum
} from './enums'; // SCH-ENUM

// SCH-TBL-AI-JOB: ai_jobs Table (AI Chat Sessions)
export const aiJobs = pgTable('ai_jobs', {
  id: serial('id').primaryKey(), // SCH-TBL-AI-JOB-ID

  userId: varchar('user_id', { length: 255 }).references(() => users.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-AI-JOB-USER-ID
  organizationId: varchar('organization_id', { length: 255 }).references(() => organizations.id, { onDelete: 'cascade' }), // SCH-TBL-AI-JOB-ORG-ID

  jobNamePreview: text('job_name_preview').notNull(), // SCH-TBL-AI-JOB-NAME-PREVIEW

  modelConfigurationSnapshotJsonb: jsonb('model_configuration_snapshot_jsonb').notNull().$type<{ // SCH-TBL-AI-JOB-MODEL-CFG-SNAP
    modelConfigId?: number | null;
    modelSlug: string;
    modelApiIdentifier: string;
    provider: string;
    configParams?: Record<string, any> | null;
    safetySettings?: Record<string, any>[] | null;
    generationConfig?: Record<string, any> | null;
    capabilities?: string[] | null;
    maxInputTokensSnapshot?: number | null;
    maxOutputTokensSnapshot?: number | null;
  }>(),

  status: aiJobStatusEnum('status').default('active').notNull(), // SCH-TBL-AI-JOB-STATUS

  errorDetailsJsonb: jsonb('error_details_jsonb').$type<{ // SCH-TBL-AI-JOB-ERROR-DETAILS
    code?: string | null;
    message?: string | null;
    timestamp?: string | null;
    isRetryable?: boolean | null;
    providerErrorDetails?: string | null;
  }>().default({}),

  totalUserMessages: integer('total_user_messages').default(0).notNull(), // SCH-TBL-AI-JOB-TOTAL-USER-MSG
  totalAiMessages: integer('total_ai_messages').default(0).notNull(), // SCH-TBL-AI-JOB-TOTAL-AI-MSG
  totalPromptTokensInJob: integer('total_prompt_tokens_in_job').default(0).notNull(), // SCH-TBL-AI-JOB-TOTAL-PROMPT-TOK
  totalCompletionTokensInJob: integer('total_completion_tokens_in_job').default(0).notNull(), // SCH-TBL-AI-JOB-TOTAL-COMPLETION-TOK

  tagsJsonb: jsonb('tags_jsonb').$type<string[]>().default([]), // SCH-TBL-AI-JOB-TAGS

  visibility: text('visibility').default('private').notNull(), // SCH-TBL-AI-JOB-VISIBILITY
  shareToken: varchar('share_token', { length: 128 }).unique(), // SCH-TBL-AI-JOB-SHARE-TOKEN

  lastMessagePreview: text('last_message_preview'), // SCH-TBL-AI-JOB-LAST-MSG-PREVIEW

  contextCacheJsonb: jsonb('context_cache_jsonb').$type<{ // SCH-TBL-AI-JOB-CONTEXT-CACHE
    cacheId?: string | null;
    lastUsedAt?: string | null;
    tokenCount?: number | null;
    modelApiIdentifierUsed?: string | null;
  }>().default({}),

  activeFileContextIdsJsonb: jsonb('active_file_context_ids_jsonb').$type<number[]>().default([]), // SCH-TBL-AI-JOB-ACTIVE-FILE-CTX-IDS

  pinnedAt: timestamp('pinned_at', { withTimezone: true, mode: 'date' }), // SCH-TBL-AI-JOB-PINNED-AT
  archivedAt: timestamp('archived_at', { withTimezone: true, mode: 'date' }), // SCH-TBL-AI-JOB-ARCHIVED-AT
  lastActivityAt: timestamp('last_activity_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-AI-JOB-LAST-ACTIVITY

  createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-AI-JOB-CREATED-AT
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-AI-JOB-UPDATED-AT
}, (table) => {
  return {
    userIdIdx: index('ai_job_user_id_idx').on(table.userId),
    organizationIdIdx: index('ai_job_org_id_idx').on(table.organizationId).nullsLast(),
    statusIdx: index('ai_job_status_idx').on(table.status),
    lastActivityAtIdx: index('ai_job_last_activity_at_idx').on(table.lastActivityAt).desc(),
    pinnedAtIdx: index('ai_job_pinned_at_idx').on(table.pinnedAt).desc().nullsLast(),
    shareTokenIdx: uniqueIndex('ai_job_share_token_idx').on(table.shareToken).nullsNotDistinct(),
  };
});

// SCH-TBL-AI-MSG: ai_messages Table (AI Chat Messages)
export const aiMessages = pgTable('ai_messages', {
  id: serial('id').primaryKey(), // SCH-TBL-AI-MSG-ID
  jobId: integer('job_id').references(() => aiJobs.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-AI-MSG-JOB-ID

  senderIdentifier: text('sender_identifier').notNull(), // SCH-TBL-AI-MSG-SENDER-ID
  role: aiMessageRoleEnum('role').notNull(), // SCH-TBL-AI-MSG-ROLE

  content: text('content').notNull(), // SCH-TBL-AI-MSG-CONTENT
  orderIndex: integer('order_index').notNull(), // SCH-TBL-AI-MSG-ORDER-INDEX

  modelRequestDetailsJsonb: jsonb('model_request_details_jsonb').$type<{ // SCH-TBL-AI-MSG-REQ-DETAILS
    promptTokens?: number | null;
    includedHistoryMessageOrderIndices?: number[] | null;
    includedFileContextIds?: number[] | null;
    modelConfigSnapshotForTurn?: Record<string, any> | null;
    contextCacheUsedId?: string | null;
  }>().default({}),

  modelResponseMetadataJsonb: jsonb('model_response_metadata_jsonb').$type<{ // SCH-TBL-AI-MSG-RES-METADATA
    completionTokens?: number | null;
    finishReason?: string | null;
    safetyRatings?: Record<string, any>[] | null;
    rawApiResponsePreview?: string | null;
    timeToFirstTokenMs?: number | null;
    totalGenerationTimeMs?: number | null;
    toolCalls?: Record<string, any>[] | null;
  }>().default({}),

  streamStateJsonb: jsonb('stream_state_jsonb').$type<{ // SCH-TBL-AI-MSG-STREAM-STATE
    isStreamingComplete?: boolean | null;
  }>().default({ isStreamingComplete: false }),

  feedbackJsonb: jsonb('feedback_jsonb').$type<{ // SCH-TBL-AI-MSG-FEEDBACK
    rating?: 'thumbs_up' | 'thumbs_down' | null;
    comment?: string | null;
    tags?: string[] | null;
    correctedContent?: string | null;
    feedbackSubmittedAt?: string | null;
  }>().default({}),

  isErrorPlaceholder: boolean('is_error_placeholder').default(false).notNull(), // SCH-TBL-AI-MSG-IS-ERROR-PLACEHOLDER

  attachmentsJsonb: jsonb('attachments_jsonb').$type<Array<{ // SCH-TBL-AI-MSG-ATTACHMENTS
    fileId: number;
    fileNameSnapshot: string;
    fileTypeSnapshot: string;
  }>>().default([]),

  suggestedFollowUpPromptsJsonb: jsonb('suggested_follow_up_prompts_jsonb').$type<string[]>().default([]), // SCH-TBL-AI-MSG-SUGGESTED-PROMPTS
  isPinned: boolean('is_pinned').default(false).notNull(), // SCH-TBL-AI-MSG-IS-PINNED

  createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-AI-MSG-CREATED-AT
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-AI-MSG-UPDATED-AT
  editedAt: timestamp('edited_at', { withTimezone: true, mode: 'date' }), // SCH-TBL-AI-MSG-EDITED-AT
}, (table) => {
  return {
    jobIdOrderIndexUniqueIdx: index('ai_msg_job_id_order_idx').on(table.jobId, table.orderIndex).unique(),
    jobIdIdx: index('ai_msg_job_id_idx').on(table.jobId),
    senderIdentifierIdx: index('ai_msg_sender_id_idx').on(table.senderIdentifier),
    roleIdx: index('ai_msg_role_idx').on(table.role),
  };
});

// SCH-TBL-AI-FILE: ai_uploaded_files_metadata Table (AI Chat File Uploads Metadata)
export const aiUploadedFilesMetadata = pgTable('ai_uploaded_files_metadata', {
  id: serial('id').primaryKey(), // SCH-TBL-AI-FILE-ID

  userId: varchar('user_id', { length: 255 }).references(() => users.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-AI-FILE-USER-ID
  organizationId: varchar('organization_id', { length: 255 }).references(() => organizations.id, { onDelete: 'cascade' }), // SCH-TBL-AI-FILE-ORG-ID

  storagePath: text('storage_path').notNull(), // SCH-TBL-AI-FILE-STORAGE-PATH
  fileName: text('file_name').notNull(),         // SCH-TBL-AI-FILE-NAME
  fileType: text('file_type').notNull(),         // SCH-TBL-AI-FILE-TYPE
  fileSizeBytes: bigint('file_size_bytes', {mode: 'number'}).notNull(), // SCH-TBL-AI-FILE-SIZE

  status: aiFileUploadStatusEnum('status').notNull(), // SCH-TBL-AI-FILE-STATUS

  processingErrorJsonb: jsonb('processing_error_jsonb').$type<{ // SCH-TBL-AI-FILE-PROCESSING-ERROR
    code?: string | null;
    message?: string | null;
    stage?: 'upload' | 'text_extraction' | 'vision_analysis' | 'general_processing' | null;
    retriesAttempted?: number | null;
  }>().default({}),

  extractedTextStoragePath: text('extracted_text_storage_path'), // SCH-TBL-AI-FILE-EXTRACTED-TEXT-PATH
  extractedTextCharCount: integer('extracted_text_char_count'), // SCH-TBL-AI-FILE-EXTRACTED-TEXT-COUNT

  extractedImageEntitiesJsonb: jsonb('extracted_image_entities_jsonb').$type<Array<{ // SCH-TBL-AI-FILE-EXTRACTED-IMG-ENTITIES
    label: string;
    score: number;
    boundingBoxes?: Array<{ x: number, y: number, width: number, height: number }> | null;
    ocrText?: string | null;
  }>>().default([]),

  checksum: varchar('checksum', {length: 64}), // SCH-TBL-AI-FILE-CHECKSUM
  userProvidedDescription: text('user_provided_description'), // SCH-TBL-AI-FILE-USER-DESC

  expiresAt: timestamp('expires_at', { withTimezone: true, mode: 'date' }), // SCH-TBL-AI-FILE-EXPIRES-AT
  isArchived: boolean('is_archived').default(false).notNull(), // SCH-TBL-AI-FILE-IS-ARCHIVED

  createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-AI-FILE-CREATED-AT
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-AI-FILE-UPDATED-AT
}, (table) => {
  return {
    userIdIdx: index('ai_file_meta_user_id_idx').on(table.userId),
    organizationIdIdx: index('ai_file_meta_org_id_idx').on(table.organizationId).nullsLast(),
    statusIdx: index('ai_file_meta_status_idx').on(table.status),
    fileTypeIdx: index('ai_file_meta_type_idx').on(table.fileType),
    checksumIdx: index('ai_file_meta_checksum_idx').on(table.checksum).nullsLast(),
  };
});

// SCH-TBL-AI-MODEL-CFG: ai_model_configurations Table (AI Model Admin Settings)
export const aiModelConfigurations = pgTable('ai_model_configurations', {
  id: serial('id').primaryKey(), // SCH-TBL-AI-MODEL-CFG-ID
  modelSlug: varchar('model_slug', {length: 100}).notNull().unique(), // SCH-TBL-AI-MODEL-CFG-SLUG

  modelName: text('model_name').notNull(), // SCH-TBL-AI-MODEL-CFG-NAME
  modelApiIdentifier: text('model_api_identifier').notNull(), // SCH-TBL-AI-MODEL-CFG-API-ID
  provider: text('provider').default('GoogleGemini').notNull(), // SCH-TBL-AI-MODEL-CFG-PROVIDER

  description: text('description'), // SCH-TBL-AI-MODEL-CFG-DESC
  status: aiModelConfigStatusEnum('status').default('active').notNull(), // SCH-TBL-AI-MODEL-CFG-STATUS

  configParamsJsonb: jsonb('config_params_jsonb').$type<{ // SCH-TBL-AI-MODEL-CFG-PARAMS
    temperature?: number | null;
    topP?: number | null;
    topK?: number | null;
    candidateCount?: number | null;
  }>().default({}),

  safetySettingsJsonb: jsonb('safety_settings_jsonb').$type<Array<{ // SCH-TBL-AI-MODEL-CFG-SAFETY
    category: string;
    threshold: string;
  }>>().default([]),

  generationConfigJsonb: jsonb('generation_config_jsonb').$type<{ // SCH-TBL-AI-MODEL-CFG-GEN-CONFIG
    maxOutputTokens?: number | null;
    stopSequences?: string[] | null;
  }>().default({}),

  capabilitiesJsonb: jsonb('capabilities_jsonb').$type<string[]>().default([]), // SCH-TBL-AI-MODEL-CFG-CAPABILITIES

  maxInputTokens: integer('max_input_tokens'), // SCH-TBL-AI-MODEL-CFG-MAX-INPUT-TOK
  maxOutputTokens: integer('max_output_tokens'), // SCH-TBL-AI-MODEL-CFG-MAX-OUTPUT-TOK

  tokenizationStrategyInfoJsonb: jsonb('tokenization_strategy_info_jsonb').$type<{ // SCH-TBL-AI-MODEL-CFG-TOKEN-STRATEGY
    libraryHint?: 'GOOGLE_GEMINI_SENTENCEPIECE' | 'TIKTOKEN_CL100K_BASE' | string | null;
    approxCharsPerToken?: number | null;
  }>().default({}),

  isDefaultModel: boolean('is_default_model').default(false).notNull(), // SCH-TBL-AI-MODEL-CFG-IS-DEFAULT
  isEnabledGlobally: boolean('is_enabled_globally').default(true).notNull(), // SCH-TBL-AI-MODEL-CFG-IS-ENABLED-GLOBAL

  accessControlJsonb: jsonb('access_control_jsonb').$type<{ // SCH-TBL-AI-MODEL-CFG-ACCESS-CONTROL
    allowedOrgIds?: string[] | null;
    allowedUserIds?: string[] | null;
    restrictedOrgIds?: string[] | null;
  }>().default({}),

  costPerMillionInputTokensUsd: text('cost_per_million_input_tokens_usd'), // SCH-TBL-AI-MODEL-CFG-COST-INPUT
  costPerMillionOutputTokensUsd: text('cost_per_million_output_tokens_usd'), // SCH-TBL-AI-MODEL-CFG-COST-OUTPUT

  notesForAdmins: text('notes_for_admins'), // SCH-TBL-AI-MODEL-CFG-ADMIN-NOTES

  createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-AI-MODEL-CFG-CREATED-AT
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-AI-MODEL-CFG-UPDATED-AT
}, (table) => {
  return {
    modelSlugIdx: uniqueIndex('ai_model_cfg_slug_idx').on(table.modelSlug),
    modelApiIdentifierProviderUniqueIdx: uniqueIndex('ai_model_cfg_api_id_provider_idx').on(table.modelApiIdentifier, table.provider),
    providerStatusIdx: index('ai_model_cfg_prov_stat_idx').on(table.provider, table.status),
    isEnabledGloballyDefaultIdx: index('ai_model_cfg_enabled_default_idx').on(table.isEnabledGlobally, table.isDefaultModel),
  };
});

// SCH-TBL-AI-SYS-SET: ai_global_system_settings Table
export const aiGlobalSystemSettings = pgTable('ai_global_system_settings', {
  singletonKey: varchar('singleton_key', {length: 30}).primaryKey().default('ai_settings_global_v1'), // SCH-TBL-AI-SYS-SET-SINGLETON-KEY

  aiChatEnabledGlobally: boolean('ai_chat_enabled_globally').default(true).notNull(), // SCH-TBL-AI-SYS-SET-ENABLED-GLOBAL

  defaultModelConfigSlug: varchar('default_model_config_slug', {length: 100}), // SCH-TBL-AI-SYS-SET-DEFAULT-MODEL-SLUG

  maxHistoryMessagesForPrompt: integer('max_history_messages_for_prompt').default(20).notNull(), // SCH-TBL-AI-SYS-SET-MAX-HISTORY
  maxFileUploadSizeMbPerFileChat: integer('max_file_upload_size_mb_per_file_chat').default(25).notNull(), // SCH-TBL-AI-SYS-SET-MAX-FILE-SIZE
  allowedFileTypesChatJsonb: jsonb('allowed_file_types_chat_jsonb').$type<string[]>().default(['text/plain', 'application/pdf', 'image/jpeg', 'image/png', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']).notNull(), // SCH-TBL-AI-SYS-SET-ALLOWED-FILE-TYPES
  maxTotalFileContextSizeBytesPerTurn: bigint('max_total_file_context_size_bytes_per_turn', {mode: 'number'}).default(100 * 1024 * 1024), // SCH-TBL-AI-SYS-SET-MAX-TOTAL-FILE-CTX
  maxActiveFilesAllowedInJobContext: integer('max_active_files_allowed_in_job_context').default(10).notNull(), // SCH-TBL-AI-SYS-SET-MAX-ACTIVE-FILES
  textExtractionTruncateCharsPerFile: integer('text_extraction_truncate_chars_per_file').default(200000).notNull(), // SCH-TBL-AI-SYS-SET-TEXT-EXTRACT-TRUNCATE

  enableModelContextCachingGlobally: boolean('enable_model_context_caching_globally').default(true).notNull(), // SCH-TBL-AI-SYS-SET-ENABLE-CACHE
  aiChatSseFunctionTimeoutSeconds: integer('ai_chat_sse_function_timeout_seconds').default(540).notNull(), // SCH-TBL-AI-SYS-SET-SSE-TIMEOUT
  maxConcurrentStreamsPerUserGlobal: integer('max_concurrent_streams_per_user_global').default(3).notNull(), // SCH-TBL-AI-SYS-SET-MAX-CONCURRENT-STREAMS

  defaultSystemPromptV1: text('default_system_prompt_v1').default('You are a helpful AI assistant. Please be concise and accurate. Format responses using Markdown where appropriate, especially for code blocks, lists, and tables.').notNull(), // SCH-TBL-AI-SYS-SET-DEFAULT-PROMPT

  allowUserMessageEditMinutes: integer('allow_user_message_edit_minutes').default(5).notNull(), // SCH-TBL-AI-SYS-SET-ALLOW-MSG-EDIT
  jobInactivityArchiveDays: integer('job_inactivity_archive_days').default(90).notNull(), // SCH-TBL-AI-SYS-SET-JOB-INACTIVITY-ARCHIVE
  allowUserModelParameterOverrides: boolean('allow_user_model_parameter_overrides').default(true).notNull(), // SCH-TBL-AI-SYS-SET-ALLOW-USER-OVERRIDES

  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-AI-SYS-SET-UPDATED-AT
});

// SCH-TBL-AI-STREAM-TOK: ai_stream_tokens Table
export const aiStreamTokens = pgTable('ai_stream_tokens', {
  id: serial('id').primaryKey(), // SCH-TBL-AI-STREAM-TOK-ID

  tokenHash: varchar('token_hash', { length: 128 }).notNull().unique(), // SCH-TBL-AI-STREAM-TOK-HASH

  jobId: integer('job_id').references(() => aiJobs.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-AI-STREAM-TOK-JOB-ID
  userId: varchar('user_id', { length: 255 }).references(() => users.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-AI-STREAM-TOK-USER-ID

  targetAiMessageId: integer('target_ai_message_id').references(() => aiMessages.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-AI-STREAM-TOK-TARGET-MSG-ID

  turnContextHash: varchar('turn_context_hash', { length: 128 }), // SCH-TBL-AI-STREAM-TOK-TURN-CTX-HASH

  createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-AI-STREAM-TOK-CREATED-AT
  expiresAt: timestamp('expires_at', { withTimezone: true, mode: 'date' }).notNull(), // SCH-TBL-AI-STREAM-TOK-EXPIRES-AT
}, (table) => {
  return {
    tokenHashIdx: uniqueIndex('ai_stream_token_hash_idx').on(table.tokenHash),
    expiresAtIdx: index('ai_stream_token_expires_at_idx').on(table.expiresAt),
    jobIdUserIdIdx: index('ai_stream_token_job_user_idx').on(table.jobId, table.userId),
  };
});
```

---

### 1.7. Unified Educational Data Model Tables (`SCH-EDU-TBL`)

**File:** `packages/db/schema/ed_data.ts`

```typescript
// SCH-EDU-TBL: Unified Educational Data Model Tables
import { pgTable, text, timestamp, uuid, varchar, index, jsonb, integer, boolean, numeric, primaryKey, date } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { users, organizations } from './core'; // SCH-CORE-TBL
import {
  appAcademicSessionTypeEnum, appEntityStatusEnum, appUserRoleEnum, appInvitationStatusEnum,
  appStreamItemTypeEnum, appAssignmentWorkTypeEnum, appAttachmentTypeEnum, appSubmissionStateEnum,
  appSectionAliasNamespaceEnum, appExternalSystemEnum
} from './enums'; // SCH-ENUM

// SCH-TBL-EDU-SESS: app_academic_sessions Table
export const appAcademicSessions = pgTable('app_academic_sessions', {
  id: uuid('id').primaryKey().defaultRandom(), // SCH-TBL-EDU-SESS-ID

  organizationId: varchar('organization_id', { length: 255 }).references(() => organizations.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-EDU-SESS-ORG-ID

  title: varchar('title', { length: 255 }).notNull(), // SCH-TBL-EDU-SESS-TITLE
  startDate: timestamp('start_date', { mode: 'date', withTimezone: false }).notNull(), // SCH-TBL-EDU-SESS-START-DATE
  endDate: timestamp('end_date', { mode: 'date', withTimezone: false }).notNull(),   // SCH-TBL-EDU-SESS-END-DATE
  type: appAcademicSessionTypeEnum('type').notNull(), // SCH-TBL-EDU-SESS-TYPE

  parentId: uuid('parent_id'), // SCH-TBL-EDU-SESS-PARENT-ID
  schoolYear: integer('school_year'), // SCH-TBL-EDU-SESS-SCHOOL-YEAR

  onerosterAcademicSessionSourcedId: varchar('oneroster_academic_session_sourced_id', { length: 255 }).unique(), // SCH-TBL-EDU-SESS-OR-SOURCED-ID
  onerosterDateLastModified: timestamp('oneroster_date_last_modified', { withTimezone: true, mode: 'date' }), // SCH-TBL-EDU-SESS-OR-LAST-MOD
  onerosterStatus: appEntityStatusEnum('oneroster_status'), // SCH-TBL-EDU-SESS-OR-STATUS

  sourceSpecificMetadataJsonb: jsonb('source_specific_metadata_jsonb').$type<Record<string, any>>().default({}), // SCH-TBL-EDU-SESS-SOURCE-META

  createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-EDU-SESS-CREATED-AT
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-EDU-SESS-UPDATED-AT
}, (table) => {
  return {
    organizationIdIdx: index('app_acad_session_org_id_idx').on(table.organizationId),
    typeIdx: index('app_acad_session_type_idx').on(table.type),
    parentIdIdx: index('app_acad_session_parent_id_idx').on(table.parentId).nullsLast(),
    schoolYearIdx: index('app_acad_session_school_year_idx').on(table.schoolYear),
    onerosterSourcedIdIdx: uniqueIndex('app_acad_session_or_sourced_id_idx').on(table.onerosterAcademicSessionSourcedId).nullsNotDistinct(),
  };
});

// SCH-TBL-EDU-COURSE: app_courses Table
export const appCourses = pgTable('app_courses', {
  id: uuid('id').primaryKey().defaultRandom(), // SCH-TBL-EDU-COURSE-ID

  organizationId: varchar('organization_id', { length: 255 }).references(() => organizations.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-EDU-COURSE-ORG-ID

  title: varchar('title', { length: 255 }).notNull(), // SCH-TBL-EDU-COURSE-TITLE
  courseCode: varchar('course_code', { length: 50 }), // SCH-TBL-EDU-COURSE-CODE
  description: text('description'), // SCH-TBL-EDU-COURSE-DESC

  gradesJsonb: jsonb('grades_jsonb').$type<string[]>().default([]), // SCH-TBL-EDU-COURSE-GRADES
  subjectsJsonb: jsonb('subjects_jsonb').$type<string[]>().default([]), // SCH-TBL-EDU-COURSE-SUBJECTS
  subjectCodesJsonb: jsonb('subject_codes_jsonb').$type<string[]>().default([]), // SCH-TBL-EDU-COURSE-SUBJECT-CODES

  onerosterCourseSourcedId: varchar('oneroster_course_sourced_id', { length: 255 }).unique(), // SCH-TBL-EDU-COURSE-OR-SOURCED-ID
  onerosterDateLastModified: timestamp('oneroster_date_last_modified', { withTimezone: true, mode: 'date' }), // SCH-TBL-EDU-COURSE-OR-LAST-MOD
  onerosterStatus: appEntityStatusEnum('oneroster_status'), // SCH-TBL-EDU-COURSE-OR-STATUS

  sourceSpecificMetadataJsonb: jsonb('source_specific_metadata_jsonb').$type<Record<string, any>>().default({}), // SCH-TBL-EDU-COURSE-SOURCE-META

  createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-EDU-COURSE-CREATED-AT
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-EDU-COURSE-UPDATED-AT
}, (table) => {
  return {
    organizationIdIdx: index('app_course_org_id_idx').on(table.organizationId),
    courseCodeOrgIdx: uniqueIndex('app_course_code_org_idx').on(table.organizationId, table.courseCode).nullsNotDistinct(),
    titleSearchIdx: index('app_course_title_search_idx').on(table.title),
    onerosterSourcedIdIdx: uniqueIndex('app_course_or_sourced_id_idx').on(table.onerosterCourseSourcedId).nullsNotDistinct(),
  };
});

// SCH-TBL-EDU-SECTION: app_sections Table (Classes/Course Sections)
export const appSections = pgTable('app_sections', {
  id: uuid('id').primaryKey().defaultRandom(), // SCH-TBL-EDU-SECTION-ID

  appCourseId: uuid('app_course_id').references(() => appCourses.id, { onDelete: 'restrict' }).notNull(), // SCH-TBL-EDU-SECTION-COURSE-ID

  schoolOrgId: varchar('school_org_id', { length: 255 }).references(() => organizations.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-EDU-SECTION-SCHOOL-ORG-ID

  primaryAcademicSessionId: uuid('primary_academic_session_id').references(() => appAcademicSessions.id, { onDelete: 'set null' }), // SCH-TBL-EDU-SECTION-PRIMARY-SESS-ID

  title: varchar('title', { length: 255 }).notNull(), // SCH-TBL-EDU-SECTION-TITLE
  sectionCode: varchar('section_code', { length: 100 }), // SCH-TBL-EDU-SECTION-CODE
  location: varchar('location', { length: 255 }), // SCH-TBL-EDU-SECTION-LOCATION
  status: appEntityStatusEnum('status').default('active').notNull(), // SCH-TBL-EDU-SECTION-STATUS
  description: text('description'), // SCH-TBL-EDU-SECTION-DESC

  gradesOverrideJsonb: jsonb('grades_override_jsonb').$type<string[]>().default([]), // SCH-TBL-EDU-SECTION-GRADES-OVERRIDE
  subjectsOverrideJsonb: jsonb('subjects_override_jsonb').$type<string[]>().default([]), // SCH-TBL-EDU-SECTION-SUBJECTS-OVERRIDE
  subjectCodesOverrideJsonb: jsonb('subject_codes_override_jsonb').$type<string[]>().default([]), // SCH-TBL-EDU-SECTION-SUBJECT-CODES-OVERRIDE

  sectionType: text('section_type'), // SCH-TBL-EDU-SECTION-TYPE

  onerosterClassSourcedId: varchar('oneroster_class_sourced_id', { length: 255 }).unique(), // SCH-TBL-EDU-SECTION-OR-SOURCED-ID
  onerosterClassDateLastModified: timestamp('oneroster_class_date_last_modified', { withTimezone: true, mode: 'date' }), // SCH-TBL-EDU-SECTION-OR-LAST-MOD

  googleClassroomId: varchar('google_classroom_id', { length: 255 }).unique(), // SCH-TBL-EDU-SECTION-GC-ID
  googleClassroomOwnerId: varchar('google_classroom_owner_id', { length: 255 }), // SCH-TBL-EDU-SECTION-GC-OWNER-ID
  googleClassroomEnrollmentCode: varchar('google_classroom_enrollment_code', { length: 50 }), // SCH-TBL-EDU-SECTION-GC-ENROLL-CODE
  googleClassroomAlternateLink: text('google_classroom_alternate_link'), // SCH-TBL-EDU-SECTION-GC-ALT-LINK
  googleClassroomCreationTime: timestamp('google_classroom_creation_time', { withTimezone: true, mode: 'date' }), // SCH-TBL-EDU-SECTION-GC-CREATED-AT
  googleClassroomUpdateTime: timestamp('google_classroom_update_time', { withTimezone: true, mode: 'date' }), // SCH-TBL-EDU-SECTION-GC-UPDATED-AT
  googleClassroomCalendarId: text('google_classroom_calendar_id'), // SCH-TBL-EDU-SECTION-GC-CALENDAR-ID
  googleClassroomTeacherFolderId: text('google_classroom_teacher_folder_id'), // SCH-TBL-EDU-SECTION-GC-TEACHER-FOLDER-ID
  googleClassroomCourseGroupEmail: text('google_classroom_course_group_email'), // SCH-TBL-EDU-SECTION-GC-GROUP-EMAIL
  googleClassroomGuardiansEnabled: boolean('google_classroom_guardians_enabled'), // SCH-TBL-EDU-SECTION-GC-GUARDIANS-ENABLED

  sourceSpecificMetadataJsonb: jsonb('source_specific_metadata_jsonb').$type<Record<string, any>>().default({}), // SCH-TBL-EDU-SECTION-SOURCE-META

  createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-EDU-SECTION-CREATED-AT
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-EDU-SECTION-UPDATED-AT
}, (table) => {
  return {
    appCourseIdIdx: index('app_section_course_id_idx').on(table.appCourseId),
    schoolOrgIdIdx: index('app_section_school_id_idx').on(table.schoolOrgId),
    primaryAcademicSessionIdIdx: index('app_section_primary_session_id_idx').on(table.primaryAcademicSessionId),
    statusIdx: index('app_section_status_idx').on(table.status),
    onerosterSourcedIdIdx: uniqueIndex('app_section_or_sourced_id_idx').on(table.onerosterClassSourcedId).nullsNotDistinct(),
    googleClassroomIdIdx: uniqueIndex('app_section_gc_id_idx').on(table.googleClassroomId).nullsNotDistinct(),
    schoolSectionCodeTermUniqueIdx: uniqueIndex('app_section_school_code_term_idx').on(table.schoolOrgId, table.sectionCode, table.primaryAcademicSessionId).nullsNotDistinct(),
  };
});

// SCH-TBL-EDU-SEC-TERM: app_section_terms Table (Junction Table)
export const appSectionTerms = pgTable('app_section_terms', {
  sectionId: uuid('section_id').references(() => appSections.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-EDU-SEC-TERM-SECTION-ID
  academicSessionId: uuid('academic_session_id').references(() => appAcademicSessions.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-EDU-SEC-TERM-SESS-ID
}, (table) => ({
  pk: primaryKey({ name: 'app_section_terms_pk', columns: [table.sectionId, table.academicSessionId] }),
}));

// SCH-TBL-EDU-ENROLL: app_enrollments Table
export const appEnrollments = pgTable('app_enrollments', {
  id: uuid('id').primaryKey().defaultRandom(), // SCH-TBL-EDU-ENROLL-ID

  userId: varchar('user_id', { length: 255 }).references(() => users.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-EDU-ENROLL-USER-ID
  sectionId: uuid('section_id').references(() => appSections.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-EDU-ENROLL-SECTION-ID

  role: appUserRoleEnum('role').notNull(), // SCH-TBL-EDU-ENROLL-ROLE

  isPrimaryEnrollment: boolean('is_primary_enrollment').default(false), // SCH-TBL-EDU-ENROLL-IS-PRIMARY
  status: appEntityStatusEnum('status').default('active').notNull(), // SCH-TBL-EDU-ENROLL-STATUS

  beginDate: timestamp('begin_date', { mode: 'date', withTimezone: false }), // SCH-TBL-EDU-ENROLL-BEGIN-DATE
  endDate: timestamp('end_date', { mode: 'date', withTimezone: false }),     // SCH-TBL-EDU-ENROLL-END-DATE

  onerosterEnrollmentSourcedId: varchar('oneroster_enrollment_sourced_id', { length: 255 }).unique(), // SCH-TBL-EDU-ENROLL-OR-SOURCED-ID
  onerosterDateLastModified: timestamp('oneroster_date_last_modified', { withTimezone: true, mode: 'date' }), // SCH-TBL-EDU-ENROLL-OR-LAST-MOD

  googleClassroomEnrolledUserProfileId: varchar('google_classroom_enrolled_user_profile_id', { length: 255 }), // SCH-TBL-EDU-ENROLL-GC-USER-PROFILE-ID

  sourceSpecificMetadataJsonb: jsonb('source_specific_metadata_jsonb').$type<Record<string, any>>().default({}), // SCH-TBL-EDU-ENROLL-SOURCE-META

  createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-EDU-ENROLL-CREATED-AT
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-EDU-ENROLL-UPDATED-AT
}, (table) => {
  return {
    userSectionRoleUniqueIdx: uniqueIndex('app_enroll_user_section_role_idx').on(table.userId, table.sectionId, table.role),
    userIdx: index('app_enroll_user_id_idx').on(table.userId),
    sectionIdIdx: index('app_enroll_section_id_idx').on(table.sectionId),
    roleIdx: index('app_enroll_role_idx').on(table.role),
    statusIdx: index('app_enroll_status_idx').on(table.status),
    onerosterSourcedIdIdx: uniqueIndex('app_enroll_or_sourced_id_idx').on(table.onerosterEnrollmentSourcedId).nullsNotDistinct(),
  };
});

// SCH-TBL-EDU-GUARD: app_guardians Table
export const appGuardians = pgTable('app_guardians', {
  studentUserId: varchar('student_user_id', { length: 255 }).references(() => users.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-EDU-GUARD-STUDENT-ID
  guardianUserId: varchar('guardian_user_id', { length: 255 }).references(() => users.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-EDU-GUARD-GUARDIAN-ID

  relationshipType: text('relationship_type'), // SCH-TBL-EDU-GUARD-REL-TYPE

  googleClassroomGuardianLinkId: varchar('google_classroom_guardian_link_id', { length: 255 }).unique(), // SCH-TBL-EDU-GUARD-GC-LINK-ID
  googleClassroomInvitedEmailAddress: text('google_classroom_invited_email_address'), // SCH-TBL-EDU-GUARD-GC-INVITED-EMAIL

  sourceSpecificMetadataJsonb: jsonb('source_specific_metadata_jsonb').$type<Record<string, any>>().default({}), // SCH-TBL-EDU-GUARD-SOURCE-META

  createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-EDU-GUARD-CREATED-AT
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-EDU-GUARD-UPDATED-AT
}, (table) => {
  return {
    pk: primaryKey({ name: 'app_guardians_pk', columns: [table.studentUserId, table.guardianUserId] }),
    studentUserIdIdx: index('app_guardian_student_id_idx').on(table.studentUserId),
    guardianUserIdIdx: index('app_guardian_guardian_id_idx').on(table.guardianUserId),
    googleGuardianLinkIdIdx: uniqueIndex('app_guardian_gc_link_id_idx').on(table.googleClassroomGuardianLinkId).nullsNotDistinct(),
  };
});

// SCH-TBL-EDU-TOPIC: app_topics Table
export const appTopics = pgTable('app_topics', {
  id: uuid('id').primaryKey().defaultRandom(), // SCH-TBL-EDU-TOPIC-ID

  sectionId: uuid('section_id').references(() => appSections.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-EDU-TOPIC-SECTION-ID

  name: varchar('name', { length: 255 }).notNull(), // SCH-TBL-EDU-TOPIC-NAME

  displayOrder: integer('display_order').default(0), // SCH-TBL-EDU-TOPIC-DISPLAY-ORDER

  googleClassroomTopicId: varchar('google_classroom_topic_id', { length: 255 }).unique(), // SCH-TBL-EDU-TOPIC-GC-ID
  googleClassroomUpdateTime: timestamp('google_classroom_update_time', { withTimezone: true, mode: 'date' }), // SCH-TBL-EDU-TOPIC-GC-UPDATED-AT

  sourceSpecificMetadataJsonb: jsonb('source_specific_metadata_jsonb').$type<Record<string, any>>().default({}), // SCH-TBL-EDU-TOPIC-SOURCE-META

  createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-EDU-TOPIC-CREATED-AT
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-EDU-TOPIC-UPDATED-AT
}, (table) => {
  return {
    sectionIdNameUniqueIdx: uniqueIndex('app_topic_section_name_idx').on(table.sectionId, table.name),
    sectionIdIdx: index('app_topic_section_id_idx').on(table.sectionId),
    googleClassroomTopicIdIdx: uniqueIndex('app_topic_gc_topic_id_idx').on(table.googleClassroomTopicId).nullsNotDistinct(),
  };
});

// SCH-TBL-EDU-ASSIGN: app_assignments Table (Polymorphic Child of app_stream_items)
export const appAssignments = pgTable('app_assignments', {
  id: uuid('id').primaryKey().defaultRandom(), // SCH-TBL-EDU-ASSIGN-ID

  title: varchar('title', { length: 255 }).notNull(), // SCH-TBL-EDU-ASSIGN-TITLE
  description: text('description'), // SCH-TBL-EDU-ASSIGN-DESC

  dueDate: timestamp('due_date', { mode: 'date', withTimezone: false }), // SCH-TBL-EDU-ASSIGN-DUE-DATE
  dueTime: timestamp('due_time', { withTimezone: true, mode: 'date' }), // SCH-TBL-EDU-ASSIGN-DUE-TIME

  maxPoints: numeric('max_points', { precision: 10, scale: 2 }), // SCH-TBL-EDU-ASSIGN-MAX-POINTS

  workType: appAssignmentWorkTypeEnum('work_type'), // SCH-TBL-EDU-ASSIGN-WORK-TYPE

  googleClassroomAssigneeMode: text('google_classroom_assignee_mode'), // SCH-TBL-EDU-ASSIGN-GC-ASSIGNEE-MODE
  googleClassroomIndividualStudentsOptionsJsonb: jsonb('google_classroom_individual_students_options_jsonb').$type<{ studentIds?: string[] | null }>(), // SCH-TBL-EDU-ASSIGN-GC-INDIVIDUAL-STUDENTS
  googleClassroomSubmissionModificationMode: text('google_classroom_submission_modification_mode'), // SCH-TBL-EDU-ASSIGN-GC-SUB-MOD-MODE

  sourceSpecificMetadataJsonb: jsonb('source_specific_metadata_jsonb').$type<Record<string, any>>().default({}), // SCH-TBL-EDU-ASSIGN-SOURCE-META
});

// SCH-TBL-EDU-MAT: app_materials Table (Polymorphic Child of app_stream_items)
export const appMaterials = pgTable('app_materials', {
  id: uuid('id').primaryKey().defaultRandom(), // SCH-TBL-EDU-MAT-ID

  title: varchar('title', { length: 255 }).notNull(), // SCH-TBL-EDU-MAT-TITLE
  description: text('description'), // SCH-TBL-EDU-MAT-DESC

  googleClassroomAssigneeMode: text('google_classroom_assignee_mode'), // SCH-TBL-EDU-MAT-GC-ASSIGNEE-MODE
  googleClassroomIndividualStudentsOptionsJsonb: jsonb('google_classroom_individual_students_options_jsonb').$type<{ studentIds?: string[] | null }>(), // SCH-TBL-EDU-MAT-GC-INDIVIDUAL-STUDENTS

  sourceSpecificMetadataJsonb: jsonb('source_specific_metadata_jsonb').$type<Record<string, any>>().default({}), // SCH-TBL-EDU-MAT-SOURCE-META
});

// SCH-TBL-EDU-ANN: app_announcements Table (Polymorphic Child of app_stream_items)
export const appAnnouncements = pgTable('app_announcements', {
  id: uuid('id').primaryKey().defaultRandom(), // SCH-TBL-EDU-ANN-ID

  textContent: text('text_content').notNull(), // SCH-TBL-EDU-ANN-TEXT-CONTENT

  googleClassroomAssigneeMode: text('google_classroom_assignee_mode'), // SCH-TBL-EDU-ANN-GC-ASSIGNEE-MODE
  googleClassroomIndividualStudentsOptionsJsonb: jsonb('google_classroom_individual_students_options_jsonb').$type<{ studentIds?: string[] | null }>(), // SCH-TBL-EDU-ANN-GC-INDIVIDUAL-STUDENTS
  googleClassroomState: text('google_classroom_state'), // SCH-TBL-EDU-ANN-GC-STATE

  sourceSpecificMetadataJsonb: jsonb('source_specific_metadata_jsonb').$type<Record<string, any>>().default({}), // SCH-TBL-EDU-ANN-SOURCE-META
});

// SCH-TBL-EDU-STREAM: app_stream_items Table (Polymorphic Parent for Section Content)
export const appStreamItems = pgTable('app_stream_items', {
  id: uuid('id').primaryKey().defaultRandom(), // SCH-TBL-EDU-STREAM-ID

  sectionId: uuid('section_id').references(() => appSections.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-EDU-STREAM-SECTION-ID

  creatorUserId: varchar('creator_user_id', { length: 255 }).references(() => users.id, { onDelete: 'set null' }), // SCH-TBL-EDU-STREAM-CREATOR-ID

  topicId: uuid('topic_id').references(() => appTopics.id, { onDelete: 'set null' }), // SCH-TBL-EDU-STREAM-TOPIC-ID

  itemType: appStreamItemTypeEnum('item_type').notNull(), // SCH-TBL-EDU-STREAM-ITEM-TYPE

  assignmentId: uuid('assignment_id').references(() => appAssignments.id, { onDelete: 'cascade' }).unique(), // SCH-TBL-EDU-STREAM-ASSIGN-ID
  materialId: uuid('material_id').references(() => appMaterials.id, { onDelete: 'cascade' }).unique(),   // SCH-TBL-EDU-STREAM-MAT-ID
  announcementId: uuid('announcement_id').references(() => appAnnouncements.id, { onDelete: 'cascade' }).unique(), // SCH-TBL-EDU-STREAM-ANN-ID

  status: appEntityStatusEnum('status').default('active').notNull(), // SCH-TBL-EDU-STREAM-STATUS
  scheduledPublishTime: timestamp('scheduled_publish_time', { withTimezone: true, mode: 'date' }), // SCH-TBL-EDU-STREAM-SCHEDULED-TIME

  googleClassroomStreamItemId: varchar('google_classroom_stream_item_id', { length: 255 }).unique(), // SCH-TBL-EDU-STREAM-GC-STREAM-ID
  googleClassroomCourseWorkId: varchar('google_classroom_course_work_id', { length: 255 }).unique(), // SCH-TBL-EDU-STREAM-GC-COURSEWORK-ID
  googleClassroomMaterialId: varchar('google_classroom_material_id', { length: 255 }).unique(),   // SCH-TBL-EDU-STREAM-GC-MATERIAL-ID
  googleClassroomAnnouncementId: varchar('google_classroom_announcement_id', { length: 255 }).unique(), // SCH-TBL-EDU-STREAM-GC-ANN-ID

  googleClassroomCreationTime: timestamp('google_classroom_creation_time', { withTimezone: true, mode: 'date' }), // SCH-TBL-EDU-STREAM-GC-CREATED-AT
  googleClassroomUpdateTime: timestamp('google_classroom_update_time', { withTimezone: true, mode: 'date' }),     // SCH-TBL-EDU-STREAM-GC-UPDATED-AT
  googleClassroomAlternateLink: text('google_classroom_alternate_link'), // SCH-TBL-EDU-STREAM-GC-ALT-LINK

  sourceSpecificMetadataJsonb: jsonb('source_specific_metadata_jsonb').$type<Record<string, any>>().default({}), // SCH-TBL-EDU-STREAM-SOURCE-META

  createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-EDU-STREAM-CREATED-AT
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-EDU-STREAM-UPDATED-AT
}, (table) => {
  return {
    sectionIdIdx: index('app_stream_item_section_id_idx').on(table.sectionId),
    creatorUserIdIdx: index('app_stream_item_creator_id_idx').on(table.creatorUserId),
    topicIdIdx: index('app_stream_item_topic_id_idx').on(table.topicId).nullsLast(),
    itemTypeIdx: index('app_stream_item_type_idx').on(table.itemType),
    statusIdx: index('app_stream_item_status_idx').on(table.status),
    googleClassroomStreamItemIdIdx: uniqueIndex('app_stream_item_gc_stream_id_idx').on(table.googleClassroomStreamItemId).nullsNotDistinct(),
    googleClassroomCourseWorkIdIdx: uniqueIndex('app_stream_item_gc_coursework_id_idx').on(table.googleClassroomCourseWorkId).nullsNotDistinct(),
    googleClassroomMaterialIdIdx: uniqueIndex('app_stream_item_gc_material_id_idx').on(table.googleClassroomMaterialId).nullsNotDistinct(),
    googleClassroomAnnouncementIdIdx: uniqueIndex('app_stream_item_gc_announce_id_idx').on(table.googleClassroomAnnouncementId).nullsNotDistinct(),
  };
});

// SCH-TBL-EDU-ATTACH: app_attachments Table
export const appAttachments = pgTable('app_attachments', {
  id: uuid('id').primaryKey().defaultRandom(), // SCH-TBL-EDU-ATTACH-ID

  streamItemId: uuid('stream_item_id'), // SCH-TBL-EDU-ATTACH-STREAM-ITEM-ID
  submissionId: uuid('submission_id'), // SCH-TBL-EDU-ATTACH-SUBMISSION-ID

  title: varchar('title', { length: 255 }).notNull(), // SCH-TBL-EDU-ATTACH-TITLE
  description: text('description'), // SCH-TBL-EDU-ATTACH-DESC

  attachmentType: appAttachmentTypeEnum('attachment_type').notNull(), // SCH-TBL-EDU-ATTACH-TYPE
  urlLink: text('url_link'), // SCH-TBL-EDU-ATTACH-URL-LINK
  filePath: text('file_path'), // SCH-TBL-EDU-ATTACH-FILE-PATH
  fileMimeType: varchar('file_mime_type', { length: 100 }), // SCH-TBL-EDU-ATTACH-MIME-TYPE
  fileSizeBytes: bigint('file_size_bytes', {mode: 'number'}), // SCH-TBL-EDU-ATTACH-FILE-SIZE

  uploaderUserId: varchar('uploader_user_id', { length: 255 }).references(() => users.id, { onDelete: 'set null' }), // SCH-TBL-EDU-ATTACH-UPLOADER-ID

  thumbnailUrl: text('thumbnail_url'), // SCH-TBL-EDU-ATTACH-THUMBNAIL-URL

  googleDriveFileJsonb: jsonb('google_drive_file_jsonb').$type<any>(), // SCH-TBL-EDU-ATTACH-GC-DRIVE-FILE
  youtubeVideoJsonb: jsonb('youtube_video_jsonb').$type<any>(), // SCH-TBL-EDU-ATTACH-GC-YOUTUBE
  linkJsonb: jsonb('link_jsonb').$type<any>(), // SCH-TBL-EDU-ATTACH-GC-LINK
  formJsonb: jsonb('form_jsonb').$type<any>(), // SCH-TBL-EDU-ATTACH-GC-FORM

  googleClassroomAddOnAttachmentType: text('google_classroom_add_on_attachment_type'), // SCH-TBL-EDU-ATTACH-GC-ADDON-TYPE
  googleClassroomAddOnSubmissionId: text('google_classroom_add_on_submission_id'), // SCH-TBL-EDU-ATTACH-GC-ADDON-SUB-ID
  googleClassroomAddOnContextJsonb: jsonb('google_classroom_add_on_context_jsonb').$type<any>(), // SCH-TBL-EDU-ATTACH-GC-ADDON-CTX

  sourceSpecificMetadataJsonb: jsonb('source_specific_metadata_jsonb').$type<Record<string, any>>().default({}), // SCH-TBL-EDU-ATTACH-SOURCE-META

  createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-EDU-ATTACH-CREATED-AT
}, (table) => {
  return {
    streamItemIdIdx: index('app_attach_stream_item_id_idx').on(table.streamItemId).nullsLast(),
    submissionIdIdx: index('app_attach_submission_id_idx').on(table.submissionId).nullsLast(),
    uploaderUserIdIdx: index('app_attach_uploader_id_idx').on(table.uploaderUserId).nullsLast(),
    attachmentTypeIdx: index('app_attach_type_idx').on(table.attachmentType),
  };
});

// SCH-TBL-EDU-SUB: app_student_submissions Table
export const appStudentSubmissions = pgTable('app_student_submissions', {
  id: uuid('id').primaryKey().defaultRandom(), // SCH-TBL-EDU-SUB-ID

  assignmentStreamItemId: uuid('assignment_stream_item_id').references(() => appStreamItems.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-EDU-SUB-ASSIGN-STREAM-ID

  studentUserId: varchar('student_user_id', { length: 255 }).references(() => users.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-EDU-SUB-STUDENT-ID

  submissionState: appSubmissionStateEnum('submission_state').notNull(), // SCH-TBL-EDU-SUB-STATE

  submittedAt: timestamp('submitted_at', { withTimezone: true, mode: 'date' }), // SCH-TBL-EDU-SUB-SUBMITTED-AT
  lastSubmittedAt: timestamp('last_submitted_at', { withTimezone: true, mode: 'date' }), // SCH-TBL-EDU-SUB-LAST-SUBMITTED-AT
  lastModifiedByStudentAt: timestamp('last_modified_by_student_at', { withTimezone: true, mode: 'date' }), // SCH-TBL-EDU-SUB-LAST-MOD-STUDENT-AT
  gradedAt: timestamp('graded_at', { withTimezone: true, mode: 'date' }), // SCH-TBL-EDU-SUB-GRADED-AT
  returnedAt: timestamp('returned_at', { withTimezone: true, mode: 'date' }), // SCH-TBL-EDU-SUB-RETURNED-AT

  assignedGrade: numeric('assigned_grade', { precision: 10, scale: 2 }), // SCH-TBL-EDU-SUB-ASSIGNED-GRADE
  draftGrade: numeric('draft_grade', { precision: 10, scale: 2 }), // SCH-TBL-EDU-SUB-DRAFT-GRADE
  teacherFeedbackText: text('teacher_feedback_text'), // SCH-TBL-EDU-SUB-TEACHER-FEEDBACK

  shortAnswerResponse: text('short_answer_response'), // SCH-TBL-EDU-SUB-SHORT-ANSWER
  multipleChoiceResponse: varchar('multiple_choice_response', {length: 255}), // SCH-TBL-EDU-SUB-MULTIPLE-CHOICE

  googleClassroomSubmissionId: varchar('google_classroom_submission_id', { length: 255 }).unique(), // SCH-TBL-EDU-SUB-GC-ID
  googleClassroomLate: boolean('google_classroom_late'), // SCH-TBL-EDU-SUB-GC-LATE

  sourceSpecificMetadataJsonb: jsonb('source_specific_metadata_jsonb').$type<Record<string, any>>().default({}), // SCH-TBL-EDU-SUB-SOURCE-META

  createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-EDU-SUB-CREATED-AT
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-EDU-SUB-UPDATED-AT
}, (table) => {
  return {
    assignmentStudentUniqueIdx: uniqueIndex('app_submission_assign_student_idx').on(table.assignmentStreamItemId, table.studentUserId),
    assignmentStreamItemIdIdx: index('app_submission_assign_item_id_idx').on(table.assignmentStreamItemId),
    studentUserIdIdx: index('app_submission_student_id_idx').on(table.studentUserId),
    submissionStateIdx: index('app_submission_state_idx').on(table.submissionState),
    googleClassroomSubmissionIdIdx: uniqueIndex('app_submission_gc_sub_id_idx').on(table.googleClassroomSubmissionId).nullsNotDistinct(),
  };
});

// SCH-TBL-EDU-ALIAS: app_section_aliases Table
export const appSectionAliases = pgTable('app_section_aliases', {
  id: uuid('id').primaryKey().defaultRandom(), // SCH-TBL-EDU-ALIAS-ID

  sectionId: uuid('section_id').references(() => appSections.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-EDU-ALIAS-SECTION-ID

  alias: varchar('alias', { length: 255 }).notNull(), // SCH-TBL-EDU-ALIAS-ALIAS
  namespace: appSectionAliasNamespaceEnum('namespace').notNull(), // SCH-TBL-EDU-ALIAS-NAMESPACE

  createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-EDU-ALIAS-CREATED-AT
}, (table) => {
  return {
    sectionAliasNamespaceUniqueIdx: uniqueIndex('app_section_alias_sec_alias_ns_idx').on(table.sectionId, table.alias, table.namespace),
    sectionIdIdx: index('app_section_alias_section_id_idx').on(table.sectionId),
    aliasIdx: index('app_section_alias_alias_idx').on(table.alias),
  };
});

// SCH-TBL-EDU-INVITE: app_invitations Table (For Sections)
export const appInvitations = pgTable('app_invitations', {
  id: uuid('id').primaryKey().defaultRandom(), // SCH-TBL-EDU-INVITE-ID

  sectionId: uuid('section_id').references(() => appSections.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-EDU-INVITE-SECTION-ID

  invitedUserEmail: text('invited_user_email'), // SCH-TBL-EDU-INVITE-EMAIL
  invitedUserId: varchar('invited_user_id', { length: 255 }).references(() => users.id, { onDelete: 'cascade' }), // SCH-TBL-EDU-INVITE-USER-ID

  roleToAssignOnAccept: appUserRoleEnum('role_to_assign_on_accept').notNull(), // SCH-TBL-EDU-INVITE-ROLE-ON-ACCEPT

  status: appInvitationStatusEnum('status').default('pending').notNull(), // SCH-TBL-EDU-INVITE-STATUS

  googleClassroomInvitationId: varchar('google_classroom_invitation_id', { length: 255 }).unique(), // SCH-TBL-EDU-INVITE-GC-ID

  invitationToken: varchar('invitation_token', { length: 128 }).unique(), // SCH-TBL-EDU-INVITE-TOKEN
  expiresAt: timestamp('expires_at', { withTimezone: true, mode: 'date' }), // SCH-TBL-EDU-INVITE-EXPIRES-AT

  createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-EDU-INVITE-CREATED-AT
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-EDU-INVITE-UPDATED-AT
}, (table) => {
  return {
    sectionUserRolePendingUniqueIdx: uniqueIndex('app_invite_sec_user_role_pend_idx').on(table.sectionId, table.invitedUserId, table.roleToAssignOnAccept, table.status).nullsNotDistinct(),
    sectionIdIdx: index('app_invite_section_id_idx').on(table.sectionId),
    invitedUserIdIdx: index('app_invite_user_id_idx').on(table.invitedUserId).nullsLast(),
    invitedUserEmailIdx: index('app_invite_email_idx').on(table.invitedUserEmail).nullsLast(),
    statusIdx: index('app_invite_status_idx').on(table.status),
    invitationTokenIdx: uniqueIndex('app_invite_token_idx').on(table.invitationToken).nullsNotDistinct(),
    googleClassroomInvitationIdIdx: uniqueIndex('app_invite_gc_id_idx').on(table.googleClassroomInvitationId).nullsNotDistinct(),
  };
});

// SCH-TBL-EDU-NOTIF-REG: app_notification_registrations Table (For Google Classroom Push Notifications)
export const appNotificationRegistrations = pgTable('app_notification_registrations', {
  id: uuid('id').primaryKey().defaultRandom(), // SCH-TBL-EDU-NOTIF-REG-ID

  sectionId: uuid('section_id').references(() => appSections.id, { onDelete: 'cascade' }), // SCH-TBL-EDU-NOTIF-REG-SECTION-ID
  targetUserId: varchar('target_user_id', { length: 255 }).references(() => users.id, { onDelete: 'cascade' }), // SCH-TBL-EDU-NOTIF-REG-TARGET-USER-ID

  externalSystem: appExternalSystemEnum('external_system').default('google_classroom').notNull(), // SCH-TBL-EDU-NOTIF-REG-EXT-SYS

  googleClassroomRegistrationId: varchar('google_classroom_registration_id', { length: 255 }).unique(), // SCH-TBL-EDU-NOTIF-REG-GC-REG-ID
  googleClassroomFeedType: text('google_classroom_feed_type'), // SCH-TBL-EDU-NOTIF-REG-GC-FEED-TYPE
  googleClassroomCloudPubsubTopicName: text('google_classroom_cloud_pubsub_topic_name'), // SCH-TBL-EDU-NOTIF-REG-GC-PUBSUB-TOPIC
  googleClassroomRegistrationExpiryTime: timestamp('google_classroom_registration_expiry_time', { withTimezone: true, mode: 'date' }), // SCH-TBL-EDU-NOTIF-REG-GC-EXPIRY

  webhookUrl: text('webhook_url'), // SCH-TBL-EDU-NOTIF-REG-WEBHOOK-URL
  secretTokenForWebhookValidation: varchar('secret_token_for_webhook_validation', { length: 255 }), // SCH-TBL-EDU-NOTIF-REG-WEBHOOK-SECRET

  isActive: boolean('is_active').default(true).notNull(), // SCH-TBL-EDU-NOTIF-REG-IS-ACTIVE
  lastSuccessfulSyncOrRenewalAt: timestamp('last_successful_sync_or_renewal_at', { withTimezone: true, mode: 'date' }), // SCH-TBL-EDU-NOTIF-REG-LAST-SYNC
  lastNotificationReceivedAt: timestamp('last_notification_received_at', { withTimezone: true, mode: 'date' }), // SCH-TBL-EDU-NOTIF-REG-LAST-NOTIF
  processingFailureCount: integer('processing_failure_count').default(0), // SCH-TBL-EDU-NOTIF-REG-FAIL-COUNT
  lastFailureReason: text('last_failure_reason'), // SCH-TBL-EDU-NOTIF-REG-FAIL-REASON

  sourceSpecificMetadataJsonb: jsonb('source_specific_metadata_jsonb').$type<Record<string, any>>().default({}), // SCH-TBL-EDU-NOTIF-REG-SOURCE-META

  createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-EDU-NOTIF-REG-CREATED-AT
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-EDU-NOTIF-REG-UPDATED-AT
}, (table) => {
  return {
    sectionIdFeedTypeUniqueIdx: uniqueIndex('app_notif_reg_sec_feed_type_idx').on(table.sectionId, table.googleClassroomFeedType).nullsNotDistinct(),
    googleClassroomRegistrationIdIdx: uniqueIndex('app_notif_reg_gc_reg_id_idx').on(table.googleClassroomRegistrationId).nullsNotDistinct(),
    isActiveIdx: index('app_notif_reg_is_active_idx').on(table.isActive),
    externalSystemIdx: index('app_notif_reg_ext_sys_idx').on(table.externalSystem),
  };
});
```

---

### 1.8. Drizzle ORM Relations Definitions (`SCH-REL`)

**File:** `packages/db/schema/relations.ts`

```typescript
// SCH-REL: Drizzle ORM Relations Definitions
import { relations } from 'drizzle-orm';
import { sql } from 'drizzle-orm'; // For functional indexes if needed in relations

// Import ALL your table schemas
import { users, organizations } from './core'; // SCH-CORE-TBL
import { organizationMembers, appRoles, appPermissions, rolePermissions } from './rbac'; // SCH-RBAC-TBL
import { auditLogs, notifications, appApiKeys } from './saas'; // SCH-SAAS-TBL
import {
  appCollaborativeDocuments, appDocumentVersions, appDocumentComments,
  appProjects, appProjectMembers, appTasks, appTaskComments
} from './saas_crud_examples'; // SCH-SAAS-CRUD-TBL
import {
  aiJobs, aiMessages, aiUploadedFilesMetadata,
  aiModelConfigurations, aiGlobalSystemSettings, aiStreamTokens
} from './ai'; // SCH-AI-TBL
import {
  appAcademicSessions, appCourses, appSections, appSectionTerms,
  appEnrollments, appGuardians, appTopics, appAssignments, appMaterials, appAnnouncements,
  appStreamItems, appAttachments, appStudentSubmissions, appSectionAliases, appInvitations,
  appNotificationRegistrations
} from './ed_data'; // SCH-EDU-TBL

// SCH-REL-USERS: Relations for Users Table
export const usersRelations = relations(users, ({ many }) => ({
  appOrganizationMemberships: many(organizationMembers, { relationName: 'user_to_app_organization_memberships' }), // SCH-REL-USERS-ORG-MEM
  ownedAppCollaborativeDocuments: many(appCollaborativeDocuments, { relationName: 'document_owner', foreignKeys: [appCollaborativeDocuments.ownerUserId] }), // SCH-REL-USERS-OWNED-DOCS
  lastUpdatedAppCollaborativeDocuments: many(appCollaborativeDocuments, { relationName: 'document_last_updater', foreignKeys: [appCollaborativeDocuments.lastUpdatedByUserId] }), // SCH-REL-USERS-UPDATED-DOCS
  appDocumentVersionsSaved: many(appDocumentVersions, { relationName: 'version_saver', foreignKeys: [appDocumentVersions.savedByUserId] }), // SCH-REL-USERS-DOC-VERSIONS
  appDocumentCommentsMade: many(appDocumentComments, { relationName: 'comment_author', foreignKeys: [appDocumentComments.userId] }), // SCH-REL-USERS-DOC-COMMENTS
  appDocumentCommentsResolved: many(appDocumentComments, { relationName: 'comment_resolver', foreignKeys: [appDocumentComments.resolvedByUserId] }), // SCH-REL-USERS-DOC-COMMENTS-RESOLVED
  ownedAppProjects: many(appProjects, { relationName: 'project_owner', foreignKeys: [appProjects.ownerUserId] }), // SCH-REL-USERS-OWNED-PROJ
  projectAssignments: many(appProjectMembers, { relationName: 'user_to_project_assignments' }), // SCH-REL-USERS-PROJ-ASSIGN
  reportedAppTasks: many(appTasks, { relationName: 'task_reporter', foreignKeys: [appTasks.reporterUserId] }), // SCH-REL-USERS-REPORTED-TASK
  assignedAppTasks: many(appTasks, { relationName: 'task_assignee', foreignKeys: [appTasks.assigneeUserId] }), // SCH-REL-USERS-ASSIGNED-TASK
  appTaskCommentsMade: many(appTaskComments, { relationName: 'task_comment_author', foreignKeys: [appTaskComments.userId] }), // SCH-REL-USERS-TASK-COMMENTS
  aiJobs: many(aiJobs, { relationName: 'user_ai_jobs', foreignKeys: [aiJobs.userId] }), // SCH-REL-USERS-AI-JOBS
  appEnrollments: many(appEnrollments, { relationName: 'user_enrollments', foreignKeys: [appEnrollments.userId] }), // SCH-REL-USERS-EDU-ENROLL
  guardianEntriesAsStudent: many(appGuardians, { relationName: 'student_guardianships', foreignKeys: [appGuardians.studentUserId] }), // SCH-REL-USERS-EDU-GUARD-STUDENT
  guardianEntriesAsGuardian: many(appGuardians, { relationName: 'guardian_responsibilities', foreignKeys: [appGuardians.guardianUserId] }), // SCH-REL-USERS-EDU-GUARD-GUARDIAN
  createdAppStreamItems: many(appStreamItems, { relationName: 'stream_item_creator', foreignKeys: [appStreamItems.creatorUserId] }), // SCH-REL-USERS-EDU-STREAM-CREATOR
  studentSubmissions: many(appStudentSubmissions, { relationName: 'student_submissions', foreignKeys: [appStudentSubmissions.studentUserId] }), // SCH-REL-USERS-EDU-SUB
  appInvitationsReceived: many(appInvitations, { relationName: 'user_invitations_received', foreignKeys: [appInvitations.invitedUserId] }), // SCH-REL-USERS-EDU-INVITE
  appNotificationRegistrationsTargeted: many(appNotificationRegistrations, { relationName: 'user_targeted_notification_regs', foreignKeys: [appNotificationRegistrations.targetUserId] }), // SCH-REL-USERS-EDU-NOTIF-REG
  uploadedAppAttachments: many(appAttachments, { relationName: 'user_uploaded_attachments', foreignKeys: [appAttachments.uploaderUserId] }), // SCH-REL-USERS-EDU-ATTACH
  initiatedAuditLogs: many(auditLogs, { relationName: 'audit_log_actor', foreignKeys: [auditLogs.actorUserId] }), // SCH-REL-USERS-AUDIT-ACTOR
  impersonatedAuditLogs: many(auditLogs, { foreignKeys: [auditLogs.impersonatorUserId], relationName: 'audit_log_impersonator' }), // SCH-REL-USERS-AUDIT-IMPERSONATOR
  receivedNotifications: many(notifications, { relationName: 'user_received_notifications', foreignKeys: [notifications.recipientUserId] }), // SCH-REL-USERS-NOTIF-RECIPIENT
  appApiKeysOwned: many(appApiKeys, { relationName: 'user_api_keys', foreignKeys: [appApiKeys.userId] }), // SCH-REL-USERS-API-KEYS
}));

// SCH-REL-ORGS: Relations for Organizations Table
export const organizationsRelations = relations(organizations, ({ one, many }) => ({
  parentAppOrganization: one(organizations, { // SCH-REL-ORGS-PARENT
    fields: [organizations.parentAppOrganizationId],
    references: [organizations.id],
    relationName: 'child_organizations_of_parent'
  }),
  childAppOrganizations: many(organizations, { relationName: 'child_organizations_of_parent' }), // SCH-REL-ORGS-CHILDREN
  appOrganizationMemberships: many(organizationMembers, { relationName: 'org_to_app_organization_memberships' }), // SCH-REL-ORGS-ORG-MEM
  appCollaborativeDocuments: many(appCollaborativeDocuments, { relationName: 'org_documents' }), // SCH-REL-ORGS-DOCS
  appProjects: many(appProjects, { relationName: 'org_projects' }), // SCH-REL-ORGS-PROJ
  aiJobsInOrg: many(aiJobs, { relationName: 'org_ai_jobs', foreignKeys: [aiJobs.organizationId] }), // SCH-REL-ORGS-AI-JOBS
  appAcademicSessions: many(appAcademicSessions, { relationName: 'org_academic_sessions', foreignKeys: [appAcademicSessions.organizationId] }), // SCH-REL-ORGS-EDU-SESS
  appCoursesOffered: many(appCourses, { relationName: 'org_courses_offered', foreignKeys: [appCourses.organizationId] }), // SCH-REL-ORGS-EDU-COURSE
  appSectionsHostedAsSchool: many(appSections, { relationName: 'school_hosted_sections', foreignKeys: [appSections.schoolOrgId] }), // SCH-REL-ORGS-EDU-SECTION-HOSTED
  auditLogsForOrg: many(auditLogs, { relationName: 'org_audit_logs', foreignKeys: [auditLogs.organizationId] }), // SCH-REL-ORGS-AUDIT
  notificationsForOrg: many(notifications, { relationName: 'org_notifications', foreignKeys: [notifications.organizationId] }), // SCH-REL-ORGS-NOTIF
  appApiKeysForOrg: many(appApiKeys, { relationName: 'org_api_keys', foreignKeys: [appApiKeys.organizationId] }), // SCH-REL-ORGS-API-KEYS
}));

// SCH-REL-ORG-MEM: Relations for organizationMembers Table
export const organizationMembersRelations = relations(organizationMembers, ({ one }) => ({
  user: one(users, { fields: [organizationMembers.userId], references: [users.id], relationName: 'user_to_app_organization_memberships' }), // SCH-REL-ORG-MEM-USER
  organization: one(organizations, { fields: [organizationMembers.organizationId], references: [organizations.id], relationName: 'org_to_app_organization_memberships' }), // SCH-REL-ORG-MEM-ORG
}));

// SCH-REL-APP-ROLES: Relations for appRoles Table
export const appRolesRelations = relations(appRoles, ({ many }) => ({
  rolePermissions: many(rolePermissions, { relationName: 'app_role_to_role_permissions' }), // SCH-REL-APP-ROLES-PERM
}));

// SCH-REL-APP-PERM: Relations for appPermissions Table
export const appPermissionsRelations = relations(appPermissions, ({ many }) => ({
  rolePermissions: many(rolePermissions, { relationName: 'app_permission_to_role_permissions' }), // SCH-REL-APP-PERM-ROLES
}));

// SCH-REL-ROLE-PERM: Relations for rolePermissions Table
export const rolePermissionsRelations = relations(rolePermissions, ({ one }) => ({
  role: one(appRoles, { fields: [rolePermissions.roleId], references: [appRoles.id], relationName: 'app_role_to_role_permissions' }), // SCH-REL-ROLE-PERM-ROLE
  permission: one(appPermissions, { fields: [rolePermissions.permissionId], references: [appPermissions.id], relationName: 'app_permission_to_role_permissions' }), // SCH-REL-ROLE-PERM-PERM
}));

// SCH-REL-AUDIT: Relations for auditLogs Table
export const auditLogsRelations = relations(auditLogs, ({ one }) => ({
  organization: one(organizations, { fields: [auditLogs.organizationId], references: [organizations.id], relationName: 'org_audit_logs' }), // SCH-REL-AUDIT-ORG
  actorUser: one(users, { fields: [auditLogs.actorUserId], references: [users.id], relationName: 'audit_log_actor' }), // SCH-REL-AUDIT-ACTOR
  impersonatorUser: one(users, { fields: [auditLogs.impersonatorUserId], references: [users.id], relationName: 'audit_log_impersonator' }), // SCH-REL-AUDIT-IMPERSONATOR
}));

// SCH-REL-NOTIF: Relations for notifications Table
export const notificationsRelations = relations(notifications, ({ one }) => ({
  recipientUser: one(users, { fields: [notifications.recipientUserId], references: [users.id], relationName: 'user_received_notifications' }), // SCH-REL-NOTIF-RECIPIENT
  organization: one(organizations, { fields: [notifications.organizationId], references: [organizations.id], relationName: 'org_notifications' }), // SCH-REL-NOTIF-ORG
}));

// SCH-REL-API-KEYS: Relations for appApiKeys Table
export const appApiKeysRelations = relations(appApiKeys, ({ one }) => ({
  user: one(users, { fields: [appApiKeys.userId], references: [users.id], relationName: 'user_api_keys' }), // SCH-REL-API-KEYS-USER
  organization: one(organizations, { fields: [appApiKeys.organizationId], references: [organizations.id], relationName: 'org_api_keys' }), // SCH-REL-API-KEYS-ORG
}));

// SCH-REL-DOCS: Relations for appCollaborativeDocuments Table
export const appCollaborativeDocumentsRelations = relations(appCollaborativeDocuments, ({ one, many }) => ({
  organization: one(organizations, { fields: [appCollaborativeDocuments.organizationId], references: [organizations.id], relationName: 'org_documents' }), // SCH-REL-DOCS-ORG
  owner: one(users, { fields: [appCollaborativeDocuments.ownerUserId], references: [users.id], relationName: 'document_owner' }), // SCH-REL-DOCS-OWNER
  lastUpdatedByUser: one(users, { fields: [appCollaborativeDocuments.lastUpdatedByUserId], references: [users.id], relationName: 'document_last_updater' }), // SCH-REL-DOCS-LAST-UPDATER
  versions: many(appDocumentVersions, { relationName: 'document_to_versions' }), // SCH-REL-DOCS-VERSIONS
  comments: many(appDocumentComments, { relationName: 'document_to_comments' }), // SCH-REL-DOCS-COMMENTS
}));

// SCH-REL-DOC-VER: Relations for appDocumentVersions Table
export const appDocumentVersionsRelations = relations(appDocumentVersions, ({ one }) => ({
  document: one(appCollaborativeDocuments, { fields: [appDocumentVersions.documentId], references: [appCollaborativeDocuments.id], relationName: 'document_to_versions' }), // SCH-REL-DOC-VER-DOC
  savedByUser: one(users, { fields: [appDocumentVersions.savedByUserId], references: [users.id], relationName: 'version_saver' }), // SCH-REL-DOC-VER-SAVED-BY
}));

// SCH-REL-DOC-COMM: Relations for appDocumentComments Table
export const appDocumentCommentsRelations = relations(appDocumentComments, ({ one, many }) => ({
  document: one(appCollaborativeDocuments, { fields: [appDocumentComments.documentId], references: [appCollaborativeDocuments.id], relationName: 'document_to_comments' }), // SCH-REL-DOC-COMM-DOC
  user: one(users, { fields: [appDocumentComments.userId], references: [users.id], relationName: 'comment_author' }), // SCH-REL-DOC-COMM-USER
  threadOrigin: one(appDocumentComments, {fields: [appDocumentComments.threadId], references: [appDocumentComments.id], relationName: "comment_thread_origin"}), // SCH-REL-DOC-COMM-THREAD-ORIGIN
  parentComment: one(appDocumentComments, {fields: [appDocumentComments.parentCommentId], references: [appDocumentComments.id], relationName: "comment_reply_to_parent"}), // SCH-REL-DOC-COMM-PARENT-COMM
  replies: many(appDocumentComments, {relationName: "comment_reply_to_parent"}), // SCH-REL-DOC-COMM-REPLIES
  resolvedByUser: one(users, { fields: [appDocumentComments.resolvedByUserId], references: [users.id], relationName: 'comment_resolver' }), // SCH-REL-DOC-COMM-RESOLVED-BY
}));

// SCH-REL-PROJ: Relations for appProjects Table
export const appProjectsRelations = relations(appProjects, ({ one, many }) => ({
    organization: one(organizations, { fields: [appProjects.organizationId], references: [organizations.id], relationName: 'org_projects' }), // SCH-REL-PROJ-ORG
    owner: one(users, { fields: [appProjects.ownerUserId], references: [users.id], relationName: 'project_owner' }), // SCH-REL-PROJ-OWNER
    projectMembers: many(appProjectMembers, { relationName: 'project_to_members' }), // SCH-REL-PROJ-MEMBERS
    tasks: many(appTasks, { relationName: 'project_to_tasks' }), // SCH-REL-PROJ-TASKS
}));

// SCH-REL-PROJ-MEM: Relations for appProjectMembers Table
export const appProjectMembersRelations = relations(appProjectMembers, ({ one }) => ({
    project: one(appProjects, { fields: [appProjectMembers.projectId], references: [appProjects.id], relationName: 'project_to_members' }), // SCH-REL-PROJ-MEM-PROJ
    user: one(users, { fields: [appProjectMembers.userId], references: [users.id], relationName: 'user_to_project_assignments' }), // SCH-REL-PROJ-MEM-USER
}));

// SCH-REL-TASK: Relations for appTasks Table
export const appTasksRelations = relations(appTasks, ({ one, many }) => ({
    project: one(appProjects, { fields: [appTasks.projectId], references: [appProjects.id], relationName: 'project_to_tasks' }), // SCH-REL-TASK-PROJ
    organization: one(organizations, { fields: [appTasks.organizationId], references: [organizations.id] }), // SCH-REL-TASK-ORG
    assignee: one(users, { fields: [appTasks.assigneeUserId], references: [users.id], relationName: 'task_assignee' }), // SCH-REL-TASK-ASSIGNEE
    reporter: one(users, { fields: [appTasks.reporterUserId], references: [users.id], relationName: 'task_reporter' }), // SCH-REL-TASK-REPORTER
    parentTask: one(appTasks, { fields: [appTasks.parentTaskId], references: [appTasks.id], relationName: 'subtasks_of_parent_task' }), // SCH-REL-TASK-PARENT
    subTasks: many(appTasks, { relationName: 'subtasks_of_parent_task' }), // SCH-REL-TASK-SUBTASKS
    comments: many(appTaskComments, {relationName: 'task_to_comments'}), // SCH-REL-TASK-COMMENTS
}));

// SCH-REL-TASK-COMM: Relations for appTaskComments Table
export const appTaskCommentsRelations = relations(appTaskComments, ({ one }) => ({
    task: one(appTasks, {fields: [appTaskComments.taskId], references: [appTasks.id], relationName: 'task_to_comments'}), // SCH-REL-TASK-COMM-TASK
    user: one(users, {fields: [appTaskComments.userId], references: [users.id], relationName: 'task_comment_author'}), // SCH-REL-TASK-COMM-USER
}));

// SCH-REL-AI-JOB: Relations for aiJobs Table
export const aiJobsRelations = relations(aiJobs, ({ one, many }) => ({
  user: one(users, { fields: [aiJobs.userId], references: [users.id], relationName: 'user_ai_jobs' }), // SCH-REL-AI-JOB-USER
  organization: one(organizations, { fields: [aiJobs.organizationId], references: [organizations.id], relationName: 'org_ai_jobs' }), // SCH-REL-AI-JOB-ORG
  messages: many(aiMessages, { relationName: 'job_to_ai_messages' }), // SCH-REL-AI-JOB-MESSAGES
}));

// SCH-REL-AI-MSG: Relations for aiMessages Table
export const aiMessagesRelations = relations(aiMessages, ({ one }) => ({
  job: one(aiJobs, { fields: [aiMessages.jobId], references: [aiJobs.id], relationName: 'job_to_ai_messages' }), // SCH-REL-AI-MSG-JOB
}));

// SCH-REL-AI-FILE: Relations for aiUploadedFilesMetadata Table
export const aiUploadedFilesMetadataRelations = relations(aiUploadedFilesMetadata, ({ one }) => ({
  user: one(users, { fields: [aiUploadedFilesMetadata.userId], references: [users.id] }), // SCH-REL-AI-FILE-USER
  organization: one(organizations, { fields: [aiUploadedFilesMetadata.organizationId], references: [organizations.id] }), // SCH-REL-AI-FILE-ORG
}));

// SCH-REL-AI-STREAM-TOK: Relations for aiStreamTokens Table
export const aiStreamTokensRelations = relations(aiStreamTokens, ({ one }) => ({
  job: one(aiJobs, { fields: [aiStreamTokens.jobId], references: [aiJobs.id] }), // SCH-REL-AI-STREAM-TOK-JOB
  user: one(users, { fields: [aiStreamTokens.userId], references: [users.id] }), // SCH-REL-AI-STREAM-TOK-USER
  targetAiMessage: one(aiMessages, { fields: [aiStreamTokens.targetAiMessageId], references: [aiMessages.id] }), // SCH-REL-AI-STREAM-TOK-TARGET-MSG
}));

// SCH-REL-EDU-SESS: Relations for appAcademicSessions Table
export const appAcademicSessionsRelations = relations(appAcademicSessions, ({ one, many }) => ({
  organization: one(organizations, { fields: [appAcademicSessions.organizationId], references: [organizations.id], relationName: 'org_academic_sessions' }), // SCH-REL-EDU-SESS-ORG
  parentSession: one(appAcademicSessions, { fields: [appAcademicSessions.parentId], references: [appAcademicSessions.id], relationName: 'child_academic_sessions_of_parent' }), // SCH-REL-EDU-SESS-PARENT
  childSessions: many(appAcademicSessions, { relationName: 'child_academic_sessions_of_parent' }), // SCH-REL-EDU-SESS-CHILDREN
  sectionsPrimaryToThisSession: many(appSections, { relationName: 'primary_sections_in_session', foreignKeys: [appSections.primaryAcademicSessionId] }), // SCH-REL-EDU-SESS-PRIMARY-SECTIONS
  sectionTerms: many(appSectionTerms, { relationName: 'session_to_section_terms' }), // SCH-REL-EDU-SESS-SEC-TERMS
}));

// SCH-REL-EDU-COURSE: Relations for appCourses Table
export const appCoursesRelations = relations(appCourses, ({ one, many }) => ({
  organization: one(organizations, { fields: [appCourses.organizationId], references: [organizations.id], relationName: 'org_courses_offered' }), // SCH-REL-EDU-COURSE-ORG
  sections: many(appSections, { relationName: 'course_to_sections' }), // SCH-REL-EDU-COURSE-SECTIONS
}));

// SCH-REL-EDU-SECTION: Relations for appSections Table
export const appSectionsRelations = relations(appSections, ({ one, many }) => ({
  course: one(appCourses, { fields: [appSections.appCourseId], references: [appCourses.id], relationName: 'course_to_sections' }), // SCH-REL-EDU-SECTION-COURSE
  school: one(organizations, { fields: [appSections.schoolOrgId], references: [organizations.id], relationName: 'school_hosted_sections' }), // SCH-REL-EDU-SECTION-SCHOOL
  primaryAcademicSession: one(appAcademicSessions, { fields: [appSections.primaryAcademicSessionId], references: [appAcademicSessions.id], relationName: 'primary_sections_in_session' }), // SCH-REL-EDU-SECTION-PRIMARY-SESS
  enrollments: many(appEnrollments, { relationName: 'section_to_enrollments' }), // SCH-REL-EDU-SECTION-ENROLLMENTS
  topics: many(appTopics, { relationName: 'section_to_topics' }), // SCH-REL-EDU-SECTION-TOPICS
  streamItems: many(appStreamItems, { relationName: 'section_to_stream_items' }), // SCH-REL-EDU-SECTION-STREAM-ITEMS
  sectionTerms: many(appSectionTerms, { relationName: 'section_to_section_terms_junction' }), // SCH-REL-EDU-SECTION-SEC-TERMS
  aliases: many(appSectionAliases, { relationName: 'section_to_aliases' }), // SCH-REL-EDU-SECTION-ALIASES
  invitations: many(appInvitations, { relationName: 'section_to_invitations' }), // SCH-REL-EDU-SECTION-INVITES
  notificationRegistrations: many(appNotificationRegistrations, { relationName: 'section_to_notification_regs' }), // SCH-REL-EDU-SECTION-NOTIF-REGS
}));

// SCH-REL-EDU-SEC-TERM: Relations for appSectionTerms Table
export const appSectionTermsRelations = relations(appSectionTerms, ({ one }) => ({
  section: one(appSections, { fields: [appSectionTerms.sectionId], references: [appSections.id], relationName: 'section_to_section_terms_junction' }), // SCH-REL-EDU-SEC-TERM-SECTION
  academicSession: one(appAcademicSessions, { fields: [appSectionTerms.academicSessionId], references: [appAcademicSessions.id], relationName: 'session_to_section_terms' }), // SCH-REL-EDU-SEC-TERM-SESS
}));

// SCH-REL-EDU-ENROLL: Relations for appEnrollments Table
export const appEnrollmentsRelations = relations(appEnrollments, ({ one }) => ({
  user: one(users, { fields: [appEnrollments.userId], references: [users.id], relationName: 'user_enrollments' }), // SCH-REL-EDU-ENROLL-USER
  section: one(appSections, { fields: [appEnrollments.sectionId], references: [appSections.id], relationName: 'section_to_enrollments' }), // SCH-REL-EDU-ENROLL-SECTION
}));

// SCH-REL-EDU-GUARD: Relations for appGuardians Table
export const appGuardiansRelations = relations(appGuardians, ({ one }) => ({
  student: one(users, { fields: [appGuardians.studentUserId], references: [users.id], relationName: 'student_guardianships' }), // SCH-REL-EDU-GUARD-STUDENT
  guardian: one(users, { fields: [appGuardians.guardianUserId], references: [users.id], relationName: 'guardian_responsibilities' }), // SCH-REL-EDU-GUARD-GUARDIAN
}));

// SCH-REL-EDU-TOPIC: Relations for appTopics Table
export const appTopicsRelations = relations(appTopics, ({ one, many }) => ({
  section: one(appSections, { fields: [appTopics.sectionId], references: [appSections.id], relationName: 'section_to_topics' }), // SCH-REL-EDU-TOPIC-SECTION
  streamItems: many(appStreamItems, { relationName: 'topic_to_stream_items' }), // SCH-REL-EDU-TOPIC-STREAM-ITEMS
}));

// SCH-REL-EDU-ASSIGN: Relations for appAssignments Table
export const appAssignmentsRelations = relations(appAssignments, ({ one }) => ({
  streamItemParent: one(appStreamItems, { fields: [appAssignments.id], references: [appStreamItems.assignmentId] }), // SCH-REL-EDU-ASSIGN-STREAM-PARENT
}));

// SCH-REL-EDU-MAT: Relations for appMaterials Table
export const appMaterialsRelations = relations(appMaterials, ({ one }) => ({
  streamItemParent: one(appStreamItems, { fields: [appMaterials.id], references: [appStreamItems.materialId] }), // SCH-REL-EDU-MAT-STREAM-PARENT
}));

// SCH-REL-EDU-ANN: Relations for appAnnouncements Table
export const appAnnouncementsRelations = relations(appAnnouncements, ({ one }) => ({
  streamItemParent: one(appStreamItems, { fields: [appAnnouncements.id], references: [appStreamItems.announcementId] }), // SCH-REL-EDU-ANN-STREAM-PARENT
}));

// SCH-REL-EDU-STREAM: Relations for appStreamItems Table
export const appStreamItemsRelations = relations(appStreamItems, ({ one, many }) => ({
  section: one(appSections, { fields: [appStreamItems.sectionId], references: [appSections.id], relationName: 'section_to_stream_items' }), // SCH-REL-EDU-STREAM-SECTION
  creator: one(users, { fields: [appStreamItems.creatorUserId], references: [users.id], relationName: 'stream_item_creator' }), // SCH-REL-EDU-STREAM-CREATOR
  topic: one(appTopics, { fields: [appStreamItems.topicId], references: [appTopics.id], relationName: 'topic_to_stream_items' }), // SCH-REL-EDU-STREAM-TOPIC
  assignmentContent: one(appAssignments, { fields: [appStreamItems.assignmentId], references: [appAssignments.id] }), // SCH-REL-EDU-STREAM-ASSIGN-CONTENT
  materialContent: one(appMaterials, { fields: [appStreamItems.materialId], references: [appMaterials.id] }), // SCH-REL-EDU-STREAM-MAT-CONTENT
  announcementContent: one(appAnnouncements, { fields: [appStreamItems.announcementId], references: [appAnnouncements.id] }), // SCH-REL-EDU-STREAM-ANN-CONTENT
  attachments: many(appAttachments, { relationName: 'stream_item_to_attachments', foreignKeys: [appAttachments.streamItemId] }), // SCH-REL-EDU-STREAM-ATTACHMENTS
  studentSubmissions: many(appStudentSubmissions, { relationName: 'assignment_to_submissions', foreignKeys: [appStudentSubmissions.assignmentStreamItemId] }), // SCH-REL-EDU-STREAM-SUBMISSIONS
}));

// SCH-REL-EDU-ATTACH: Relations for appAttachments Table
export const appAttachmentsRelations = relations(appAttachments, ({ one }) => ({
  streamItem: one(appStreamItems, { fields: [appAttachments.streamItemId], references: [appStreamItems.id], relationName: 'stream_item_to_attachments' }), // SCH-REL-EDU-ATTACH-STREAM-ITEM
  submission: one(appStudentSubmissions, { fields: [appAttachments.submissionId], references: [appStudentSubmissions.id], relationName: 'submission_to_attachments' }), // SCH-REL-EDU-ATTACH-SUBMISSION
  uploader: one(users, { fields: [appAttachments.uploaderUserId], references: [users.id], relationName: 'user_uploaded_attachments' }), // SCH-REL-EDU-ATTACH-UPLOADER
}));

// SCH-REL-EDU-SUB: Relations for appStudentSubmissions Table
export const appStudentSubmissionsRelations = relations(appStudentSubmissions, ({ one, many }) => ({
  assignmentStreamItem: one(appStreamItems, { fields: [appStudentSubmissions.assignmentStreamItemId], references: [appStreamItems.id], relationName: 'assignment_to_submissions' }), // SCH-REL-EDU-SUB-ASSIGN-STREAM-ITEM
  student: one(users, { fields: [appStudentSubmissions.studentUserId], references: [users.id], relationName: 'student_submissions' }), // SCH-REL-EDU-SUB-STUDENT
  attachments: many(appAttachments, { relationName: 'submission_to_attachments', foreignKeys: [appAttachments.submissionId] }), // SCH-REL-EDU-SUB-ATTACHMENTS
}));

// SCH-REL-EDU-ALIAS: Relations for appSectionAliases Table
export const appSectionAliasesRelations = relations(appSectionAliases, ({ one }) => ({
  section: one(appSections, { fields: [appSectionAliases.sectionId], references: [appSections.id], relationName: 'section_to_aliases' }), // SCH-REL-EDU-ALIAS-SECTION
}));

// SCH-REL-EDU-INVITE: Relations for appInvitations Table
export const appInvitationsRelations = relations(appInvitations, ({ one }) => ({
  section: one(appSections, { fields: [appInvitations.sectionId], references: [appSections.id], relationName: 'section_to_invitations' }), // SCH-REL-EDU-INVITE-SECTION
  invitedUser: one(users, { fields: [appInvitations.invitedUserId], references: [users.id], relationName: 'user_invitations_received' }), // SCH-REL-EDU-INVITE-USER
}));

// SCH-REL-EDU-NOTIF-REG: Relations for appNotificationRegistrations Table
export const appNotificationRegistrationsRelations = relations(appNotificationRegistrations, ({ one }) => ({
  section: one(appSections, { fields: [appNotificationRegistrations.sectionId], references: [appSections.id], relationName: 'section_to_notification_regs' }), // SCH-REL-EDU-NOTIF-REG-SECTION
  targetUser: one(users, { fields: [appNotificationRegistrations.targetUserId], references: [users.id], relationName: 'user_targeted_notification_regs' }), // SCH-REL-EDU-NOTIF-REG-TARGET-USER
}));
```

---

### 1.9. Barrel File (`index.ts`)

**File:** `packages/db/schema/index.ts`

```typescript
// SCH-INDEX: Barrel file for Drizzle Schema
export * from './enums'; // SCH-ENUM
export * from './core'; // SCH-CORE-TBL
export * from './rbac'; // SCH-RBAC-TBL
export * from './saas'; // SCH-SAAS-TBL
export * from './saas_crud_examples'; // SCH-SAAS-CRUD-TBL
export * from './ai'; // SCH-AI-TBL
export * from './ed_data'; // SCH-EDU-TBL
export * from './relations'; // SCH-REL
```

---













---

## Document 1: Core SaaS Boilerplate PRD

**Title:** Definitive Enterprise Blueprint: An Ultimate SaaS & AI Boilerplate (Foundation: `ixartz/SaaS-Boilerplate`; Features: Full SaaS & AI Suite)

**PREAMBLE: DOCUMENT PHILOSOPHY, SCOPE DECLARATION, AND CORE ARCHITECTURAL PRINCIPLES**
    *   **DOC1-PRE-P1:** **Authoritative Specification for a Multi-Layered, Extensible Boilerplate Solution.** This PRD is the singular, comprehensive specification for an advanced, enterprise-grade boilerplate. It accelerates development of modern SaaS applications with integrated AI.
    *   **DOC1-PRE-P2:** **Core Technological Foundation: `ixartz/SaaS-Boilerplate` Modern Stack.** Leverages Next.js (App Router), Clerk, Drizzle ORM (PostgreSQL/PGlite), Tailwind CSS, Shadcn UI, `next-intl`, Pino.js, Better Stack, Checkly. Assumes basic operational understanding of these.
    *   **DOC1-PRE-P3:** **Core Deliverables for V1.0:**
        *   **DOC1-PRE-P3.1:** **Comprehensive General-Purpose SaaS Functionalities.** Includes multi-tenancy, granular app-level RBAC, audit logging, notification system, example CRUD modules, billing stubs, API key management.
        *   **DOC1-PRE-P3.2:** **Full-Featured, Ubiquitous AI Chat Application (Gemini API).** Provides real-time streaming, multi-file context, configurable models, persistent history, intuitive UI. Designed for ubiquitous access and contextual adaptation.
        *   **DOC1-PRE-P3.3:** **Role-Specific User Interfaces, Dashboards, and Workflows.** Tailored UIs for Site Admin, Org Admin, and General SaaS User.
    *   **DOC1-PRE-P4:** **Document as the Singular Source of Truth for V1.0 (Self-Contained for its scope).** Defines all requirements for V1.0; deviations require formal change.

**1.0 Introduction (DOC1-INTRO)**
    *   **DOC1-INTRO-1.1:** **Document Purpose, Intended Audience, and Navigational Guide.** Defines the product, articulates vision, details requirements, specifies tech stack, serves as blueprint, delimits V1.0 scope, facilitates communication, and provides verification basis. Audience: Sponsors, Architects, Dev Teams (Backend, Frontend, DevOps), Tech Writers, Adopters.
    *   **DOC1-INTRO-1.2:** **Project Vision: The Premier Boilerplate for Modern, AI-Powered, Data-Driven SaaS Applications.** To engineer the definitive, open-source, enterprise-grade boilerplate for rapid creation of advanced, general-purpose SaaS and sophisticated, AI-powered applications. Focus on empowering innovation, democratizing AI, ensuring quality, and providing flexibility.
    *   **DOC1-INTRO-1.3:** **Detailed Scope of V1.0:**
        *   **DOC1-INTRO-1.3.1:** Implementation of Core Technology Stack (Ref: DOC1-TECH-7.0).
        *   **DOC1-INTRO-1.3.2:** Delivery of Foundational SaaS Features: Robust Multi-Tenancy (Clerk + app DB), Application-Level RBAC (global `appGlobalRole`, org-scoped `appRoleSlugs` mapped to `app_permissions`), Comprehensive Audit Logging, Notification System (in-app & email), Example General-Purpose CRUD Modules (Collaborative Documents, Project Management), Billing Integration Stubs, Custom API Key Management Stubs.
        *   **DOC1-INTRO-1.3.3:** Delivery of Comprehensive AI Chat Application Suite: Google Gemini API integration, full UI (multi-pane, streaming, file context, model selection, history, feedback), universal access.
        *   **DOC1-INTRO-1.3.4:** Delivery of Role-Specific User Interfaces, Dashboards, and Workflows: Site Admin Panel, Org Admin Dashboard/Settings, Standard User Dashboard.
        *   **DOC1-INTRO-1.3.5:** Establishment of Developer Experience, Security, Operational Readiness, and Comprehensive Documentation.
    *   **DOC1-INTRO-1.4:** **Key Definitions, Acronyms, and Abbreviations Utilized Throughout This Document.** Comprehensive glossary for SaaS, AI, and core tech terms.

**2.0 Product Overview (DOC1-OVERVIEW)**
    *   **DOC1-OVERVIEW-2.1:** **Executive Summary: A Multi-Functional Boilerplate for General SaaS, AI-Enhanced Solutions.** Enterprise-grade, open-source, production-ready foundation built on `ixartz/SaaS-Boilerplate`. Delivers advanced general-purpose SaaS core, full-featured integrated AI Chat (Gemini API).
    *   **DOC1-OVERVIEW-2.2:** **Core Value Proposition: Accelerating Development of Secure, Scalable, Interoperable, and Intelligent Applications.** Empowers rapid time-to-market, ensures enterprise-grade quality/security, provides seamless AI integration, offers exceptional DX, and ensures flexibility/control.
    *   **DOC1-OVERVIEW-2.3:** **Key Differentiators: Unified Data Architecture, Application-Centric Design, Comprehensive Authentication (Clerk), Type-Safe DB (Drizzle), Integrated AI.** Distinguishes through comprehensive Clerk integration, Drizzle ORM for type-safe DB, full-suite advanced SaaS features, production-ready AI Chat, holistic DX, and layered RBAC.
    *   **DOC1-OVERVIEW-2.4:** **Summary of the Definitive Technology Stack (Ref: DOC1-TECH-7.0).** Next.js (App Router), React, TypeScript, Clerk, Drizzle ORM (PostgreSQL/PGlite), Tailwind CSS, Shadcn UI, `next-intl`, Google Gemini API, Sentry, Better Stack, Checkly, GitHub Actions.

**3.0 Goals and Objectives for V1.0 (DOC1-GOALS)**
    *   **DOC1-GOALS-3.1:** **Strategic Product Goals for the Boilerplate.**
        *   **DOC1-GOALS-3.1.1:** Establish a Robust, Modern, and Extensible Multi-Tenant SaaS Core Foundation.
        *   **DOC1-GOALS-3.1.2:** Deliver a Production-Quality, Feature-Rich, and Ubiquitous AI Chat Application.
        *   **DOC1-GOALS-3.1.3:** Provide Intuitive, Role-Specific User Interfaces and Dashboards for All Key General SaaS Personas.
        *   **DOC1-GOALS-3.1.4:** Ensure an Unparalleled Developer Experience (DX), High Code Quality, and Extensibility.
        *   **DOC1-GOALS-3.1.5:** Guarantee a Secure, Scalable, Performant, and Exhaustively Documented Platform Ready for Production Deployment.
    *   **DOC1-GOALS-3.2:** **Specific, Measurable, Achievable, Relevant, Time-bound (SMART) Objectives for Each Primary Goal Area:**
        *   **DOC1-GOALS-3.2.1:** SaaS Core Functionality & Architecture Objectives (e.g., Clerk integration, multi-tenancy, RBAC enforcement, CRUD module delivery).
        *   **DOC1-GOALS-3.2.2:** AI Chat Application Suite Objectives (e.g., full backend/frontend functionality, universal access).
        *   **DOC1-GOALS-3.2.3:** Role-Specific User Interface & User Experience Objectives (General SaaS roles: Site Admin, Org Admin, Standard User).
        *   **DOC1-GOALS-3.2.4:** Developer Experience & Codebase Quality Objectives (e.g., onboarding time, code quality metrics).
        *   **DOC1-GOALS-3.2.5:** Non-Functional Requirements Objectives (e.g., performance targets, security posture, reliability metrics).

**4.0 Target Audience, User Roles, and Detailed Personas (DOC1-AUDIENCE)**
    *   **DOC1-AUD-4.1:** **Definition and Characteristics of Core User Roles Within the Application Ecosystem:**
        *   **DOC1-AUD-4.1.1:** The Site Administrator (`users.appGlobalRole = 'SuperAdmin'`): Global Application Configuration and Oversight.
        *   **DOC1-AUD-4.1.2:** The Organization Administrator (Clerk `org:admin` role + App-Level Org Admin `appRole`): Tenant-Level SaaS Management.
        *   **DOC1-AUD-4.1.3:** The Standard Organization Member / General SaaS User: Utilizes Core Application and AI Features.
    *   **DOC1-AUD-4.2:** **Detailed Personas for Each Core User Role:** Elaborates on background, goals, frustrations, technical proficiency, typical tasks, and interaction with general SaaS and AI Chat features for each role.
    *   **DOC1-AUD-4.3:** **Primary Adopter Audiences for the Boilerplate Product:** Developers/Teams building General SaaS, AI-centric Apps.
    *   **DOC1-AUD-4.4:** **Secondary Audience (Indirect Beneficiaries):** All End-Users of Applications Built Upon This Boilerplate.

**5.0 System Architecture & Detailed Feature Requirements (DOC1-FEAT)**
    *   **DOC1-FEAT-5.1:** **Backend Architecture & Database Layer (Drizzle ORM, PostgreSQL Target, Next.js API Endpoints):**
        *   **DOC1-FEAT-5.1.1:** **Unified & Application-Centric Database Schema (Drizzle ORM Definitions):**
            *   **DOC1-FEAT-5.1.1.1:** Core Enum Definitions (Ref: SCH-ENUM, general SaaS & AI enums).
            *   **DOC1-FEAT-*******:** Extended Core Application Tables (`users` [Ref: SCH-TBL-USERS], `organizations` [Ref: SCH-TBL-ORGS]).
            *   **DOC1-FEAT-*******:** Application-Specific Role-Based Access Control (RBAC) Tables (`organization_members` [Ref: SCH-TBL-ORG-MEM], `app_roles` [Ref: SCH-TBL-APP-ROLES], `app_permissions` [Ref: SCH-TBL-APP-PERM], `role_permissions` [Ref: SCH-TBL-ROLE-PERM]).
            *   **DOC1-FEAT-5.1.1.4:** General SaaS Feature Support Tables (`audit_logs` [Ref: SCH-TBL-AUDIT], `notifications` [Ref: SCH-TBL-NOTIF], `app_api_keys` [Ref: SCH-TBL-API-KEYS]).
            *   **DOC1-FEAT-*******:** Example General-Purpose CRUD Entity Tables & Sub-Tables (`app_collaborative_documents` [Ref: SCH-TBL-DOCS], `app_document_versions` [Ref: SCH-TBL-DOC-VER], `app_document_comments` [Ref: SCH-TBL-DOC-COMM], `app_projects` [Ref: SCH-TBL-PROJ], `app_project_members` [Ref: SCH-TBL-PROJ-MEM], `app_tasks` [Ref: SCH-TBL-TASK], `app_task_comments` [Ref: SCH-TBL-TASK-COMM]).
            *   **DOC1-FEAT-5.1.1.6:** AI Chat Application Data Tables (`ai_jobs` [Ref: SCH-TBL-AI-JOB], `ai_messages` [Ref: SCH-TBL-AI-MSG], `ai_uploaded_files_metadata` [Ref: SCH-TBL-AI-FILE], `ai_model_configurations` [Ref: SCH-TBL-AI-MODEL-CFG], `ai_global_system_settings` [Ref: SCH-TBL-AI-SYS-SET], `ai_stream_tokens` [Ref: SCH-TBL-AI-STREAM-TOK]).
            *   **DOC1-FEAT-*******:** Drizzle ORM Relations Definitions (Ref: SCH-REL, for core, SaaS, AI tables).
        *   **DOC1-FEAT-5.1.2:** **Backend API Endpoints & Server-Side Logic (Next.js Route Handlers / Edge Functions):**
            *   **DOC1-FEAT-*******:** General Principles (Clerk Auth, App-Level AuthZ, Zod Validation, Drizzle, Logging, Error Handling).
            *   **DOC1-FEAT-*******:** Core Platform API Endpoint Groups (Clerk Webhook Handlers, User Profile/Preferences, Org/Membership Management).
            *   **DOC1-FEAT-*******:** General SaaS Feature API Endpoint Groups (CRUD for Documents, Projects, Tasks; Notifications; Audit Logs; Billing Stubs; Custom API Keys).
            *   **DOC1-FEAT-*******:** AI Chat Backend API Endpoint Groups (Full Suite).
            *   **DOC1-FEAT-*******:** Site Administration API Endpoint Groups (Global app settings, AI model configs, global audit logs).
            *   **DOC1-FEAT-*******:** Scheduled Tasks / Cron Job Logic & Secure Endpoints (General maintenance, AI maintenance).
        *   **DOC1-FEAT-5.1.3:** **Data Seeding Scripts, Drizzle Kit Migrations for Unified Schema, Backup/Restore Strategy for PostgreSQL:**
            *   **DOC1-FEAT-*******:** Comprehensive Seeding Script (for local dev/demo with general SaaS & AI data).
            *   **DOC1-FEAT-*******:** Schema Migrations (using Drizzle Kit for unified schema).
            *   **DOC1-FEAT-*******:** Backup and Restore Strategy for PostgreSQL (Production Data).
    *   **DOC1-FEAT-5.2:** **Frontend Architecture & User Interfaces (Next.js App Router, Shadcn UI, Tailwind CSS, Clerk Components, Role-Specific Dashboards & Workflows):**
        *   **DOC1-FEAT-5.2.1:** FR0: Core Frontend Structure & Global Setup.
        *   **DOC1-FEAT-5.2.2:** FR1: Authentication UI (Leveraging Styled Clerk Components).
        *   **DOC1-FEAT-5.2.3:** FR2: SaaS Core UI - Dashboard, Organization Management, User Profile.
        *   **DOC1-FEAT-5.2.4:** FR3: Example General Purpose SaaS CRUD Modules UI.
        *   **DOC1-FEAT-5.2.5:** FR4: Site Administrator Panel UI.
        *   **DOC1-FEAT-5.2.6:** FR5: AI Chat Application User Interface.
        *   **DOC1-FEAT-5.2.7:** FR6: Landing Page & Static Content Pages.
        *   **DOC1-FEAT-5.2.8:** FR7: Internationalization (i18n) & Localization (l10n) UI.
        *   **DOC1-FEAT-5.2.9:** FR8: Responsive Design & General Accessibility (AX).
        *   **DOC1-FEAT-5.2.10:** FR9: Forms (React Hook Form + Zod) & Validation UI Consistency.
        *   **DOC1-FEAT-5.2.11:** FR10: Error Handling (Sentry), Loading States, Feedback UI.
        *   **DOC1-FEAT-5.2.12:** FR11: In-App Notifications UI.
        *   **DOC1-FEAT-5.2.13:** FR12: Custom Application API Key Management UI.
        *   **DOC1-FEAT-5.2.14:** FR13: Storybook Integration and Component Library.
    *   **DOC1-FEAT-5.3:** **Security (Deep Dive - Comprehensive for Full Stack & All Data Types, including PII considerations):**
        *   **DOC1-FEAT-5.3.1:** FR_SEC_01: Authentication & Authorization Best Practices.
        *   **DOC1-FEAT-5.3.2:** FR_SEC_02: Database Security.
        *   **DOC1-FEAT-5.3.3:** FR_SEC_03: Backend API Endpoint Security.
        *   **DOC1-FEAT-5.3.4:** FR_SEC_04: Secure Secret Management.
        *   **DOC1-FEAT-5.3.5:** FR_SEC_05: File Upload Security.
        *   **DOC1-FEAT-5.3.6:** FR_SEC_06: API Key Security.
        *   **DOC1-FEAT-5.3.7:** FR_SEC_07: Content Security Policy (CSP) Implementation.
        *   **DOC1-FEAT-5.3.8:** FR_SEC_08: Standard HTTP Security Headers.
        *   **DOC1-FEAT-5.3.9:** FR_SEC_09: Vulnerability Management & Dependency Security.
        *   **DOC1-FEAT-5.3.10:** FR_SEC_10: User Impersonation Security and Auditing.
        *   **DOC1-FEAT-5.3.11:** FR_SEC_11: Server-Side Request Forgery (SSRF) Prevention.
        *   **DOC1-FEAT-5.3.12:** FR_SEC_12: Data Privacy by Design (General PII).
    *   **DOC1-FEAT-5.4:** **Operational Considerations (Comprehensive for Full Stack, Drizzle PostgreSQL, Data Lifecycle):**
        *   **DOC1-FEAT-5.4.1:** FR_OPS_01: Comprehensive Logging Strategy.
        *   **DOC1-FEAT-5.4.2:** FR_OPS_02: Proactive Monitoring & Alerting.
        *   **DOC1-FEAT-5.4.3:** FR_OPS_03: Robust CI/CD Pipeline.
        *   **DOC1-FEAT-5.4.4:** FR_OPS_04: Scalability Design and Considerations.
        *   **DOC1-FEAT-5.4.5:** FR_OPS_05: Database Management and Optimization.

**6.0 User Stories and Acceptance Criteria (DOC1-US)**
    *   **DOC1-US-6.1:** Site Administrator (`users.isSiteAdmin = true`) User Stories (Global platform management, user/org oversight, system configuration).
    *   **DOC1-US-6.2:** Core User Authentication & Profile Management User Stories (All Roles, Clerk & App Profile).
    *   **DOC1-US-6.3:** Core Organization & Membership Management User Stories (All Roles, Clerk & App Extensions).
    *   **DOC1-US-6.4:** Application-Level Role-Based Access Control (RBAC) User Stories (Defining & Enforcing `isSiteAdmin` via `appGlobalRole`, Clerk Org Roles).
    *   **DOC1-US-6.5:** AI Chat Core Functionality User Stories (Full suite, accessible by relevant roles).
    *   **DOC1-US-6.6:** Example General Purpose SaaS CRUD Modules User Stories (e.g., `app_collaborative_documents`, `app_projects`, `app_tasks`).
    *   **DOC1-US-6.7:** Billing & Subscription Management User Stories (Integration with Stripe/Paddle stubs).
    *   **DOC1-US-6.8:** Application Notification System User Stories (App-specific In-App & Email).
    *   **DOC1-US-6.9:** Custom Application API Key Management User Stories (If implemented for user/org level app-specific keys beyond Clerk tokens).
    *   **DOC1-US-6.10:** Developer Experience & Setup User Stories (ixartz stack, Drizzle, PGlite).
    *   **DOC1-US-6.11:** Security Specific User Stories (ixartz stack, Drizzle, Clerk, Next.js APIs).
    *   **DOC1-US-6.12:** Database Modelling, Migration & Seeding User Stories (Drizzle ORM for Unified Schema).

**7.0 Technical Requirements / Stack (DOC1-TECH)**
    *   **DOC1-TECH-7.1:** Single table detailing every technology, library, SDK, and service from the `ixartz/SaaS-Boilerplate` foundation, augmented with Google Gemini SDK, and any other tools required for the full defined SaaS & AI scope. (Excludes EdTech-specific libraries like `papaparse`, Google API Client Library for Node.js).

**8.0 Design and User Interface (DOC1-DESIGN)**
    *   **DOC1-DESIGN-8.1:** Visual Design Principles and Branding Guidelines (Focus on clean, accessible, customizable UI for SaaS).
    *   **DOC1-DESIGN-8.2:** UI Component Library & Strategy (Shadcn UI for custom parts, Styling Clerk Components via `appearance` API, Storybook for all).
    *   **DOC1-DESIGN-8.3:** Conceptual Wireframes & User Flows for Key Screens & Roles (Adapted for Clerk, Drizzle Data):
        *   **DOC1-DESIGN-8.3.1:** Public Landing Page & Static Content Pages.
        *   **DOC1-DESIGN-8.3.2:** Authentication Flow Screens.
        *   **DOC1-DESIGN-8.3.3:** Main Dashboard Layout.
        *   **DOC1-DESIGN-8.3.4:** Organization Settings Screens.
        *   **DOC1-DESIGN-8.3.5:** User Profile Screens.
        *   **DOC1-DESIGN-8.3.6:** Example General Purpose SaaS CRUD Module Screens.
        *   **DOC1-DESIGN-8.3.7:** AI Chat Application Interface.
        *   **DOC1-DESIGN-8.3.8:** Site Administrator Panel UIs.
        *   **DOC1-DESIGN-8.3.9:** In-App Notification Center/Dropdown UI.
        *   **DOC1-DESIGN-8.3.10:** Custom Application API Key Management UI.
    *   **DOC1-DESIGN-8.4:** Responsiveness and Cross-Device Compatibility Strategy.
    *   **DOC1-DESIGN-8.5:** Accessibility (AX) Standards (WCAG 2.1 AA) and Implementation Strategy.
    *   **DOC1-DESIGN-8.6:** User Experience (UX) Principles Guiding All Design Decisions.

**9.0 Non-Functional Requirements (DOC1-NFR)**
    *   **DOC1-NFR-9.1:** Performance (Key User Flows, API Response Times, Database Queries, AI Interactions).
    *   **DOC1-NFR-9.2:** Scalability (User Load, Data Volume, Throughput, External Services).
    *   **DOC1-NFR-9.3:** Reliability & Availability (Uptime, MTTR, Fault Tolerance, Data Durability, Backup/Restore).
    *   **DOC1-NFR-9.4:** Maintainability (Code Quality, Modularity, Testability, Documentation, DX).
    *   **DOC1-NFR-9.5:** Security (NFR Summary - Aligning with DOC1-FEAT-5.3 Security Deep Dive).
    *   **DOC1-NFR-9.6:** Data Integrity and Durability (Drizzle & PostgreSQL Focus).
    *   **DOC1-NFR-9.7:** Internationalization (i18n) & Localization (l10n) Coverage (`next-intl` for Comprehensive Multi-Language Support).
    *   **DOC1-NFR-9.8:** Usability and Learnability (For All User Roles and Features, Including SaaS, AI Contexts).
    *   **DOC1-NFR-9.9:** Extensibility and Configurability (Boilerplate Architecture, Settings, Theming, API Stubs).

**10.0 Success Metrics (DOC1-METRICS)**
    *   **DOC1-METRICS-10.1:** Functional Completeness Metrics.
    *   **DOC1-METRICS-10.2:** Stability & Reliability Metrics.
    *   **DOC1-METRICS-10.3:** Performance Benchmarks.
    *   **DOC1-METRICS-10.4:** Security Posture Validation.
    *   **DOC1-METRICS-10.5:** Developer Experience (DX) Metrics.
    *   **DOC1-METRICS-10.6:** Test Coverage Targets.
    *   **DOC1-METRICS-10.7:** AI Chat Engagement & Quality Metrics.
    *   **DOC1-METRICS-10.8:** Adoption & Community Growth Indicators.

**11.0 Future Considerations (DOC1-FUTURE)**
    *   **DOC1-FUTURE-11.1:** Advanced AI Capabilities & Integrations.
    *   **DOC1-FUTURE-11.2:** Enhanced SaaS Core Functionalities.
    *   **DOC1-FUTURE-11.3:** Collaboration & User Experience Enhancements.
    *   **DOC1-FUTURE-11.4:** Developer Experience & Architectural Improvements.
    *   **DOC1-FUTURE-11.5:** Operational & Compliance Enhancements.

**12.0 Glossary of Terms (DOC1-GLOSSARY)**
    *   Exhaustive and Self-Contained for this document's scope.

**13.0 Document Revision History (DOC1-REVISION)**

---

## Document 2: EdTech LMS Extension PRD

**Title:** EdTech LMS Extension: Application-Centric Unified Educational Data Model (Inspired by OneRoster & Google Classroom, with Optional External ID Linking)

**PREAMBLE: DOCUMENT PHILOSOPHY, SCOPE DECLARATION, AND CORE ARCHITECTURAL PRINCIPLES**
    *   **DOC2-PRE-P1:** **Authoritative Specification for an Extensible EdTech Solution.** This PRD details the specialized, deeply integrated extensions for creating feature-rich, data-driven Learning Management Systems (LMS).
    *   **DOC2-PRE-P2:** **Core Technological Foundation: Leverages the "Core SaaS Boilerplate".** This extension builds upon the foundation defined in DOC1-PRE-P2. All core technologies (Next.js, Clerk, Drizzle, etc.) are assumed.
    *   **DOC2-PRE-P3:** **Core Deliverables for V1.0:**
        *   **DOC2-PRE-P3.1:** **Application-Centric Unified Educational Data Model:**
            *   **DOC2-PRE-P3.1.1:** Internal Drizzle ORM schemas (prefixed `app_`) comprehensively covering educational concepts inspired by OneRoster 1.2 and Google Classroom APIs.
            *   **DOC2-PRE-P3.1.2:** System operates fully using this internal model without mandatory external dependencies.
            *   **DOC2-PRE-P3.1.3:** Dedicated, optional fields within internal schemas for storing external OneRoster `sourcedId`s and Google Classroom object IDs to enable traceability and future/optional integration.
        *   **DOC2-PRE-P3.2:** **Role-Specific User Interfaces, Dashboards, and Workflows** for all defined EdTech personas.
        *   **DOC2-PRE-P3.3:** **Foundational Data Import Mechanisms** (OneRoster CSV, Conceptual Google Classroom Sync).
    *   **DOC2-PRE-P4:** **Document as the Singular Source of Truth for V1.0 (Self-Contained for its scope, but references Core SaaS PRD).**

**1.0 Introduction (DOC2-INTRO)**
    *   **DOC2-INTRO-1.1:** **Document Purpose, Intended Audience, and Navigational Guide.** Defines the EdTech extension, its vision, requirements, and how it integrates. Audience: EdTech developers, product managers.
    *   **DOC2-INTRO-1.2:** **Project Vision: The Premier EdTech Extension for Modern, AI-Powered, Data-Driven LMS Solutions.** A transformative resource for EdTech, providing a standards-inspired, application-centric data model and integration readiness. (Ref: DOC1-INTRO-1.2 for base vision).
    *   **DOC2-INTRO-1.3:** **Detailed Scope of V1.0:**
        *   **DOC2-INTRO-1.3.1:** Implementation of the Application-Centric Unified Educational Data Model:
            *   **DOC2-INTRO-1.3.1.1:** Internal Drizzle Schemas for `app_` Educational Entities.
            *   **DOC2-INTRO-1.3.1.2:** Internal Application Logic for Managing `app_` Educational Entities.
            *   **DOC2-INTRO-1.3.1.3:** Optional External ID Fields within `app_` Schemas (for OneRoster/GC linking).
            *   **DOC2-INTRO-1.3.1.4:** V1 Data Import Mechanisms (OneRoster CSV, Conceptual Google Classroom Sync).
        *   **DOC2-INTRO-1.3.2:** Delivery of Role-Specific User Interfaces, Dashboards, and Workflows for all Defined EdTech Personas.
        *   **DOC2-INTRO-1.3.3:** Establishment of Developer Experience, Security, Operational Readiness, and Comprehensive Documentation (Ref: DOC1-INTRO-1.3.6 for base, details EdTech specifics).
    *   **DOC2-INTRO-1.4:** **Key Definitions, Acronyms, and Abbreviations Utilized Throughout This Document.** Focuses on EdTech-specific terms (e.g., OneRoster, FERPA, COPPA, LMS, SIS). (Ref: DOC1-INTRO-1.4 for general SaaS/AI/tech terms).

**2.0 Product Overview (DOC2-OVERVIEW)**
    *   **DOC2-OVERVIEW-2.1:** **Executive Summary: A Specialized Extension for Comprehensive, Data-Driven LMS Platforms.** Builds upon the core SaaS boilerplate to enable feature-rich LMS solutions. (Ref: DOC1-OVERVIEW-2.1 for base).
    *   **DOC2-OVERVIEW-2.2:** **Core Value Proposition for EdTech Adopter Needs.** Empowers rapid LMS development, provides standards-inspired data models, and prepares for interoperability.
    *   **DOC2-OVERVIEW-2.3:** **Key Differentiators: Application-Centric Design with Standards-Inspired Data Models, Extensibility for Independent or Integrated LMS Operation.** Unique focus on abstracting OneRoster/GC concepts into an internal model with optional external ID linking.
    *   **DOC2-OVERVIEW-2.4:** **Summary of the Definitive Technology Stack.** (Ref: DOC1-TECH-7.1 for core stack). Highlights EdTech-specific additions: `papaparse` for CSV, Google API Client Library for Node.js.

**3.0 Goals and Objectives for V1.0 (DOC2-GOALS)**
    *   **DOC2-GOALS-3.1:** **Strategic Product Goals for the EdTech Extension.**
        *   **DOC2-GOALS-3.1.1:** Implement a Comprehensive, Application-Centric Unified Educational Data Model.
        *   **DOC2-GOALS-3.1.2:** Deliver Foundational LMS Functionalities and Role-Specific UIs for EdTech Personas.
        *   **DOC2-GOALS-3.1.3:** Provide Mechanisms for Initial Educational Data Import and Conceptual Synchronization.
    *   **DOC2-GOALS-3.2:** **Specific, Measurable, Achievable, Relevant, Time-bound (SMART) Objectives for Each Primary Goal Area:**
        *   **DOC2-GOALS-3.2.1:** Application-Centric Unified Educational Data Model Objectives (e.g., Drizzle schema completeness, internal functionality, external ID mapping).
        *   **DOC2-GOALS-3.2.2:** Role-Specific User Interface & User Experience Objectives (EdTech roles: Teacher, Student, Parent, Org Admin for EdData).
        *   **DOC2-GOALS-3.2.3:** Data Import & Conceptual Sync Objectives (e.g., OneRoster CSV import success, GC pull stub functionality).
        *   **DOC2-GOALS-3.2.4:** Non-Functional Requirements Objectives (e.g., data privacy for PII, scalability for educational data volume).

**4.0 Target Audience, User Roles, and Detailed Personas (DOC2-AUDIENCE)**
    *   **DOC2-AUD-4.1:** **Definition and Characteristics of Core User Roles Within the Application Ecosystem (EdTech Focus):**
        *   **DOC2-AUD-4.1.1:** The Educator/Teacher (`app_enrollments.role = 'teacher'`).
        *   **DOC2-AUD-4.1.2:** The Learner/Student (`app_enrollments.role = 'student'`).
        *   **DOC2-AUD-4.1.3:** The Parent/Guardian (Linked via `app_guardians`).
        *   **DOC2-AUD-4.1.4:** The Organization Administrator (Clerk `org:admin` + App-Level Org Admin `appRole`) - for EdTech program oversight.
    *   **DOC2-AUD-4.2:** **Detailed Personas for Each Core User Role (EdTech Focus):** Elaborates on background, goals, frustrations, typical tasks, and interaction with LMS-specific features for each role.
    *   **DOC2-AUD-4.3:** **Primary Adopter Audiences for the Boilerplate Product:** Developers/Teams building Standalone LMSs, or LMSs requiring future external EdData system integration.
    *   **DOC2-AUD-4.4:** **Secondary Audience (Indirect Beneficiaries):** All End-Users of Applications Built Upon This Boilerplate (Students, Teachers, Parents, School Admins).

**5.0 System Architecture & Detailed Feature Requirements (DOC2-FEAT)**
    *   **DOC2-FEAT-5.1:** **Backend Architecture & Database Layer (Drizzle ORM, PostgreSQL Target, Next.js API Endpoints):**
        *   **DOC2-FEAT-5.1.1:** **Unified & Application-Centric Database Schema (Drizzle ORM Definitions):**
            *   **DOC2-FEAT-5.1.1.1:** Core Enum Definitions (Ref: SCH-ENUM, EdTech-specific enums only).
            *   **DOC2-FEAT-*******:** Extended Core Application Tables (`users` [Ref: SCH-TBL-USERS], `organizations` [Ref: SCH-TBL-ORGS]) - Focus on EdTech-specific fields (e.g., `onerosterUserSourcedId`, `googleUserId`, `orgType`, `googleDomain`).
            *   **DOC2-FEAT-*******:** **Application-Centric Unified Educational Data Model Tables (`app_` prefixed - Full Drizzle Schemas for ALL entities: `app_academic_sessions` [Ref: SCH-TBL-EDU-SESS], `app_courses` [Ref: SCH-TBL-EDU-COURSE], `app_sections` [Ref: SCH-TBL-EDU-SECTION], `app_section_terms` [Ref: SCH-TBL-EDU-SEC-TERM], `app_enrollments` [Ref: SCH-TBL-EDU-ENROLL], `app_guardians` [Ref: SCH-TBL-EDU-GUARD], `app_topics` [Ref: SCH-TBL-EDU-TOPIC], `app_assignments` [Ref: SCH-TBL-EDU-ASSIGN], `app_materials` [Ref: SCH-TBL-EDU-MAT], `app_announcements` [Ref: SCH-TBL-EDU-ANN], `app_stream_items` [Ref: SCH-TBL-EDU-STREAM], `app_attachments` [Ref: SCH-TBL-EDU-ATTACH], `app_student_submissions` [Ref: SCH-TBL-EDU-SUB], `app_section_aliases` [Ref: SCH-TBL-EDU-ALIAS], `app_invitations` [Ref: SCH-TBL-EDU-INVITE], `app_notification_registrations` [Ref: SCH-TBL-EDU-NOTIF-REG]).**
            *   **DOC2-FEAT-5.1.1.4:** Drizzle ORM Relations Definitions (Ref: SCH-REL, specifically for `app_` tables and their relations to core tables).
        *   **DOC2-FEAT-5.1.2:** **Backend API Endpoints & Server-Side Logic (Next.js Route Handlers / Edge Functions):**
            *   **DOC2-FEAT-*******:** Unified Educational Data Management API Endpoint Groups (Full CRUD for `app_` tables, role-dependent).
            *   **DOC2-FEAT-*******:** Data Import & Conceptual Synchronization API Stubs (OneRoster CSV import, conceptual Google Classroom data pull).
        *   **DOC2-FEAT-5.1.3:** **Data Seeding Scripts, Drizzle Kit Migrations for Unified Schema, Backup/Restore Strategy for PostgreSQL:** (EdTech specific considerations for seeding `app_` tables, migration impact, and data privacy in backups).
    *   **DOC2-FEAT-5.2:** **Frontend Architecture & User Interfaces (Next.js App Router, Shadcn UI, Tailwind CSS, Clerk Components, Role-Specific Dashboards & Workflows):**
        *   **DOC2-FEAT-5.2.1:** FR1: Unified Educational Data UI (Visualizing `app_courses`, `app_sections`, `app_enrollments`, etc. - Role-Specific Views).
    *   **DOC2-FEAT-5.3:** **Security (Deep Dive - PII/FERPA/COPPA considerations for Educational Data):**
        *   **DOC2-FEAT-5.3.1:** FR_SEC_01: Data Privacy by Design (GDPR/CCPA Considerations, and Specific Privacy & Compliance for OneRoster & Google Classroom Data).
    *   **DOC2-FEAT-5.4:** **Operational Considerations (Data Lifecycle including Educational Data):**
        *   **DOC2-FEAT-5.4.1:** Scheduled Tasks / Cron Job Logic & Secure Endpoints (EdData cleanup/sync stubs for `app_` tables).

**6.0 User Stories and Acceptance Criteria (DOC2-US)**
    *   **DOC2-US-6.1:** Educator/Teacher Specific LMS User Stories (Interacting with `app_sections`, `app_stream_items`, `app_student_submissions`, etc.).
    *   **DOC2-US-6.2:** Learner/Student Specific LMS User Stories (Interacting with `app_sections`, `app_stream_items`, submitting work).
    *   **DOC2-US-6.3:** Parent/Guardian Specific LMS User Stories (Viewing linked student data - V1 Read-Only Stubs).
    *   **DOC2-US-6.4:** Organization Administrator LMS User Stories (Managing EdData within their Org).
    *   **DOC2-US-6.5:** Unified Educational Data Model Integration User Stories (OneRoster CSV Import, Google Classroom Conceptual Sync, Data Mapping from different user perspectives).

**7.0 Technical Requirements / Stack (DOC2-TECH)**
    *   **DOC2-TECH-7.1:** EdTech-Specific Libraries/Tools (e.g., `papaparse` for CSV parsing, Google API Client Library for Node.js for conceptual GC sync stubs).

**8.0 Design and User Interface (DOC2-DESIGN)**
    *   **DOC2-DESIGN-8.1:** Conceptual Wireframes & User Flows for Key Screens & Roles:
        *   **DOC2-DESIGN-8.1.1:** Educator/Teacher Dashboard & LMS Feature UIs (Conceptual Wireframes).
        *   **DOC2-DESIGN-8.1.2:** Learner/Student Dashboard & LMS Feature UIs (Conceptual Wireframes).
        *   **DOC2-DESIGN-8.1.3:** Parent/Guardian Dashboard & LMS Viewing UIs (V1 Stubs - Conceptual Wireframes).
        *   **DOC2-DESIGN-8.1.4:** Unified Educational Data Management Administration UI (OneRoster Import, GC Sync - Conceptual Wireframes for Site Admin).
        *   **DOC2-DESIGN-8.1.5:** General EdData Viewing & Interaction UIs for Organization Administrators, Teachers, and Students (Conceptual Wireframes).

**9.0 Non-Functional Requirements (DOC2-NFR)**
    *   **DOC2-NFR-9.1:** Unified Educational Data Interoperability, Traceability, Privacy & Compliance NFRs (OneRoster & Google Classroom data considerations, FERPA/COPPA).

**10.0 Success Metrics (DOC2-METRICS)**
    *   **DOC2-METRICS-10.1:** Unified Educational Data Model Integration & LMS Feature Adoption Success Metrics.

**11.0 Future Considerations (DOC2-FUTURE)**
    *   **DOC2-FUTURE-11.1:** Advanced LMS Functionalities & Deeper OneRoster/Google Classroom API Integrations.

**12.0 Glossary of Terms (DOC2-GLOSSARY)**
    *   Exhaustive and Self-Contained for this document's scope. (Ref: DOC1-GLOSSARY for general terms).

**13.0 Document Revision History (DOC2-REVISION)**

---

## Document 3: Implementation & Migration Guide

**Title:** Implementation & Migration Guide: Integrating EdTech LMS Extension with Core SaaS Boilerplate

**PREAMBLE: DOCUMENT PHILOSOPHY, SCOPE DECLARATION, AND CORE ARCHITECTURAL PRINCIPLES**
    *   **DOC3-PRE-P1:** **Purpose:** This guide provides practical, step-by-step instructions for developers to combine the "Core SaaS Boilerplate" (Document 1) with the "EdTech LMS Extension" (Document 2) into a single, unified application.
    *   **DOC3-PRE-P2:** **Audience:** Developers actively implementing the combined solution, assumed to be proficient with Next.js, TypeScript, Drizzle ORM, and Clerk.
    *   **DOC3-PRE-P3:** **References:** This guide extensively references the "Core SaaS Boilerplate PRD" (Ref: DOC1) and the "EdTech LMS Extension PRD" (Ref: DOC2) for detailed requirements, and the complete Drizzle Schema (Ref: SCH-).

**1.0 Introduction (DOC3-INTRO)**
    *   **DOC3-INTRO-1.1:** **Document Purpose.** To provide a clear roadmap for integrating the two PRDs, including schema migrations and code integration points.
    *   **DOC3-INTRO-1.2:** **Prerequisites.** Developers should have the `ixartz/SaaS-Boilerplate` running locally, and both DOC1 and DOC2 PRDs available for reference.

**2.0 Understanding the Combined Architecture (DOC3-ARCH)**
    *   **DOC3-ARCH-2.1:** **Overview of the Unified Data Model.** Explains how the `app_` tables (from DOC2-FEAT-*******) integrate with the core `users` (Ref: SCH-TBL-USERS) and `organizations` (Ref: SCH-TBL-ORGS) tables (from DOC1-FEAT-*******). Highlights the role of external ID linking fields.
    *   **DOC3-ARCH-2.2:** **Layered RBAC in Practice.** Details how the `appGlobalRole` (Ref: SCH-TBL-USERS-APP-GLOBAL-ROLE), Clerk `org:admin`/`org:member` roles, application-specific `appRoleSlugs` (Ref: SCH-TBL-ORG-MEM-APP-ROLE-SLUGS), and educational contextual roles (`app_enrollments.role` [Ref: SCH-TBL-EDU-ENROLL-ROLE]) combine to enforce granular access control.
    *   **DOC3-ARCH-2.3:** **Frontend UI Integration Strategy.** Describes how the EdTech UIs (from DOC2-DESIGN-8.1) are integrated into the main dashboard layout (Ref: DOC1-DESIGN-8.3.3) and role-specific views.
    *   **DOC3-ARCH-2.4:** **Backend API Integration.** Explains how EdTech APIs (from DOC2-FEAT-5.1.2) coexist with general SaaS/AI APIs (from DOC1-FEAT-5.1.2) within the Next.js API Route Handler structure.

**3.0 Database Schema Integration & Migration (DOC3-DB-MIG)**
    *   **DOC3-DB-MIG-3.1:** **Initial State: Your Existing `TechnoKidsAI Platform` Schema.** Briefly reviews the current `organization` and `todo` tables.
    *   **DOC3-DB-MIG-3.2:** **Schema Reconciliation & Migration Plan:**
        *   **DOC3-DB-MIG-3.2.1:** **Step 1: Migrate `organization` Stripe fields to `subscriptionJsonb` [Ref: SCH-TBL-ORGS-SUB-JSONB].**
            *   **Action:** Create a Drizzle migration script.
            *   **SQL/TS Logic:** Add `subscriptionJsonb` column to `organizations` table. Write Drizzle ORM code to iterate existing `organizations` records and populate `subscriptionJsonb` from `stripe_customer_id`, `stripe_subscription_id`, `stripe_subscription_status`, `stripe_subscription_current_period_end`. Then, drop the old Stripe-specific columns.
            *   **Schema Update:** Modify `organizations` schema (Ref: SCH-TBL-ORGS) to remove direct Stripe fields and ensure `subscriptionJsonb` is correctly typed.
        *   **DOC3-DB-MIG-3.2.2:** **Step 2: Replace `todo` table with `app_tasks` [Ref: SCH-TBL-TASK].**
            *   **Action:** Create a Drizzle migration script.
            *   **SQL/TS Logic:**
                1.  Create `app_projects` [Ref: SCH-TBL-PROJ] and `app_tasks` [Ref: SCH-TBL-TASK] tables (if not already existing from DOC1-FEAT-*******).
                2.  For each existing user, create a default "My Personal Todos" `app_project` record.
                3.  Migrate existing `todo` records into `app_tasks`, linking them to the default project and mapping `todo.owner_id` to `app_tasks.assigneeUserId` or `reporterUserId`.
                4.  Drop the `todo` table.
            *   **Schema Update:** Remove `todo` schema. Ensure `app_projects` and `app_tasks` schemas (Ref: SCH-SAAS-CRUD-TBL) are present.
        *   **DOC3-DB-MIG-3.2.3:** **Step 3: Add all new `users` and `organizations` extension fields.**
            *   **Action:** Create Drizzle migration scripts.
            *   **SQL/TS Logic:** Add columns like `appGlobalRole` [Ref: SCH-TBL-USERS-APP-GLOBAL-ROLE], `userProfileJsonb` [Ref: SCH-TBL-USERS-PROFILE-JSONB], `preferencesJsonb` [Ref: SCH-TBL-USERS-PREFS-JSONB] to `users` table. Add `appSettingsJsonb` [Ref: SCH-TBL-ORGS-APP-SETTINGS-JSONB], `orgType` [Ref: SCH-TBL-ORGS-ORG-TYPE], `googleDomain` [Ref: SCH-TBL-ORGS-GOOGLE-DOMAIN], `onerosterOrgSourcedId` [Ref: SCH-TBL-ORGS-ONEROSTER-SOURCED-ID], `googleCustomerId` [Ref: SCH-TBL-ORGS-GOOGLE-CUSTOMER-ID] to `organizations` table. Add EdTech-specific linking fields to `users` (e.g., `onerosterUserSourcedId` [Ref: SCH-TBL-USERS-ONEROSTER-SOURCED-ID], `googleUserId` [Ref: SCH-TBL-USERS-GOOGLE-ID]).
            *   **Schema Update:** Update `users` and `organizations` schemas (Ref: SCH-CORE-TBL).
        *   **DOC3-DB-MIG-3.2.4:** **Step 4: Add all new RBAC tables.**
            *   **Action:** Create Drizzle migration scripts.
            *   **SQL/TS Logic:** Create `organization_members` [Ref: SCH-TBL-ORG-MEM], `app_roles` [Ref: SCH-TBL-APP-ROLES], `app_permissions` [Ref: SCH-TBL-APP-PERM], `role_permissions` [Ref: SCH-TBL-ROLE-PERM].
            *   **Schema Update:** Add RBAC schemas (Ref: SCH-RBAC-TBL).
            *   **Seeding:** Initial seeding of `app_roles` and `app_permissions` (Ref: DOC1-FEAT-*******) with default roles and permissions.
        *   **DOC3-DB-MIG-3.2.5:** **Step 5: Add all new AI Chat tables.**
            *   **Action:** Create Drizzle migration scripts.
            *   **SQL/TS Logic:** Create `ai_jobs` [Ref: SCH-TBL-AI-JOB], `ai_messages` [Ref: SCH-TBL-AI-MSG], `ai_uploaded_files_metadata` [Ref: SCH-TBL-AI-FILE], `ai_model_configurations` [Ref: SCH-TBL-AI-MODEL-CFG], `ai_global_system_settings` [Ref: SCH-TBL-AI-SYS-SET], `ai_stream_tokens` [Ref: SCH-TBL-AI-STREAM-TOK].
            *   **Schema Update:** Add AI schemas (Ref: SCH-AI-TBL).
        *   **DOC3-DB-MIG-3.2.6:** **Step 6: Add all new Unified Educational Data Model tables.**
            *   **Action:** Create Drizzle migration scripts.
            *   **SQL/TS Logic:** Create `app_academic_sessions` [Ref: SCH-TBL-EDU-SESS], `app_courses` [Ref: SCH-TBL-EDU-COURSE], `app_sections` [Ref: SCH-TBL-EDU-SECTION], `app_section_terms` [Ref: SCH-TBL-EDU-SEC-TERM], `app_enrollments` [Ref: SCH-TBL-EDU-ENROLL], `app_guardians` [Ref: SCH-TBL-EDU-GUARD], `app_topics` [Ref: SCH-TBL-EDU-TOPIC], `app_assignments` [Ref: SCH-TBL-EDU-ASSIGN], `app_materials` [Ref: SCH-TBL-EDU-MAT], `app_announcements` [Ref: SCH-TBL-EDU-ANN], `app_stream_items` [Ref: SCH-TBL-EDU-STREAM], `app_attachments` [Ref: SCH-TBL-EDU-ATTACH], `app_student_submissions` [Ref: SCH-TBL-EDU-SUB], `app_section_aliases` [Ref: SCH-TBL-EDU-ALIAS], `app_invitations` [Ref: SCH-TBL-EDU-INVITE], `app_notification_registrations` [Ref: SCH-TBL-EDU-NOTIF-REG].
            *   **Schema Update:** Add EdTech schemas (Ref: SCH-EDU-TBL).
        *   **DOC3-DB-MIG-3.2.7:** **Step 7: Add all new General SaaS Support tables.**
            *   **Action:** Create Drizzle migration scripts.
            *   **SQL/TS Logic:** Create `audit_logs` [Ref: SCH-TBL-AUDIT], `notifications` [Ref: SCH-TBL-NOTIF], `app_api_keys` [Ref: SCH-TBL-API-KEYS].
            *   **Schema Update:** Add SaaS support schemas (Ref: SCH-SAAS-TBL).
    *   **DOC3-DB-MIG-3.3:** **Running Drizzle Kit Migrations.** Step-by-step guide for generating and applying migrations locally (with PGlite) and to deployed environments (PostgreSQL).
    *   **DOC3-DB-MIG-3.4:** **Initial Data Seeding for Combined Solution.** Instructions on how to run the combined seed script (Ref: DOC1-FEAT-*******) to populate all new tables with sample data, including interlinked general SaaS, AI, and EdTech data.

**4.0 Backend API Integration (DOC3-BE-API)**
    *   **DOC3-BE-API-4.1:** **Integrating EdTech API Route Handlers.** Guide on placing EdTech-specific API Route Handlers (e.g., `app/api/app-sections/...`, `app/api/siteadmin/data/import/...` from DOC2-FEAT-5.1.2) within the existing Next.js `app/api` structure.
    *   **DOC3-BE-API-4.2:** **Updating Authorization Middleware/Helpers.** Instructions on modifying or extending existing authorization helpers (Ref: DOC1-FEAT-*******) to include checks for `app_enrollments.role` [Ref: SCH-TBL-EDU-ENROLL-ROLE] for LMS-specific APIs.
    *   **DOC3-BE-API-4.3:** **Integrating EdTech-specific Logic into Cron Jobs.** How to extend existing scheduled tasks (Ref: DOC1-FEAT-*******) or add new ones (Ref: DOC2-FEAT-5.4.1) for EdTech data maintenance (e.g., archiving old `app_sections` [Ref: SCH-TBL-EDU-SECTION], conceptual syncs).
    *   **DOC3-BE-API-4.4:** **Handling Clerk Webhooks.** Details how to ensure Clerk webhooks (`user.created`/`updated`/`deleted`, `organization.created`/`updated`/`deleted`, `organizationMembership.created`/`updated`/`deleted`) correctly populate/update the *extended* `users` [Ref: SCH-TBL-USERS] and `organizations` [Ref: SCH-TBL-ORGS] tables, and manage `organization_members` [Ref: SCH-TBL-ORG-MEM] with `appRoleSlugs` [Ref: SCH-TBL-ORG-MEM-APP-ROLE-SLUGS].

**5.0 Frontend UI Integration (DOC3-FE-UI)**
    *   **DOC3-FE-UI-5.1:** **Updating Main Layouts for New Navigation.** How to add new navigation links for LMS features and the expanded Admin Panel to `app/layout.tsx` and `DashboardLayoutClient.tsx` (Ref: DOC1-DESIGN-8.3.3).
    *   **DOC3-FE-UI-5.2:** **Integrating EdTech-specific UI Components.** Guide on placing EdTech-specific UI components (e.g., `components/features/lms-teacher/...`, `components/features/lms-student/...`, `components/features/parent-portal/...` from DOC2-DESIGN-8.1) into the main dashboard and role-specific views.
    *   **DOC3-FE-UI-5.3:** **Updating Admin Panel UI.** How to extend `AdminLayoutClient.tsx` and `AdminSidebar.tsx` (Ref: DOC1-DESIGN-8.3.8) for new Site Admin EdTech sections (e.g., Educational Data Hub).
    *   **DOC3-FE-UI-5.4:** **Updating Organization Settings UI.** Instructions on integrating EdTech-related tabs/sections into the Organization Settings UI (Ref: DOC1-DESIGN-8.3.4) for Org Admins.
    *   **DOC3-FE-UI-5.5:** **Updating User Profile UI.** How to add new app-specific tabs/sections to the User Profile UI (Ref: DOC1-DESIGN-8.3.5) for general user preferences.
    *   **DOC3-FE-UI-5.6:** **Ensuring `next-intl` Localization.** Verification that all new EdTech UI strings are correctly internationalized and localized (Ref: DOC1-FEAT-5.2.8).
    *   **DOC3-FE-UI-5.7:** **Ensuring Responsive Design and Accessibility.** Confirmation that all new EdTech UIs adhere to responsive design and accessibility standards (Ref: DOC1-FEAT-5.2.9).

**6.0 Security & Operational Considerations for Combined Solution (DOC3-SEC-OPS)**
    *   **DOC3-SEC-OPS-6.1:** **Reviewing PII Handling for EdTech Data.** Specific guidance on ensuring compliance with FERPA/COPPA conceptual alignment for student PII (Ref: DOC2-FEAT-5.3.1).
    *   **DOC3-SEC-OPS-6.2:** **Updating Logging and Monitoring Configurations.** How to extend existing logging (Ref: DOC1-FEAT-5.4.1) and monitoring (Ref: DOC1-FEAT-5.4.2) setups to include new EdTech-specific logs and metrics.
    *   **DOC3-SEC-OPS-6.3:** **Adjusting CI/CD Pipelines.** Steps to update GitHub Actions workflows (Ref: DOC1-FEAT-5.4.3) for new schema changes and deployment steps related to the EdTech extension.
    *   **DOC3-SEC-OPS-6.4:** **Reviewing Scalability Considerations.** Guidance on assessing and addressing scalability for increased data volume from EdTech entities (Ref: DOC1-FEAT-5.4.4, DOC2-GOALS-3.2.4).

**7.0 Next Steps & Future Development (DOC3-FUTURE)**
    *   **DOC3-FUTURE-7.1:** **Prioritizing V1.x and V2 Features.** How to select and plan for future development based on the "Future Considerations" sections in both PRDs (Ref: DOC1-FUTURE-11.0, DOC2-FUTURE-11.1).
    *   **DOC3-FUTURE-7.2:** **Community Contribution Guidelines.** Encouraging and guiding contributions to the combined boilerplate.

---

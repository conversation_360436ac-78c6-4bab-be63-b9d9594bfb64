# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# Database
*.db

# testing
/coverage

# storybook
storybook-static
*storybook.log

# playwright
/test-results/
/playwright-report/
/playwright/.cache/

# next.js
/.next
/out

# cache
.swc/

# production
/build

# misc
.DS_Store
*.pem
Thumbs.db

# debug
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local

# local folder
local

# vercel
.vercel
shotgun_code/

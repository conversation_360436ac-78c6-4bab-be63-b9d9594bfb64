{"name": "saas-boilerplate", "version": "1.2.0", "scripts": {"dev:spotlight": "spotlight-sidecar", "dev:next": "next dev", "dev": "run-p dev:*", "build": "next build", "start": "next start", "build-stats": "cross-env ANALYZE=true npm run build", "clean": "rimraf .next out coverage", "lint": "eslint .", "lint:fix": "eslint . --fix", "check-types": "tsc --noEmit --pretty", "test": "vitest run", "test:e2e": "playwright test", "commit": "cz", "db:generate": "drizzle-kit generate", "db:migrate": "dotenv -c production -- drizzle-kit migrate", "db:studio": "dotenv -c production -- drizzle-kit studio", "storybook": "storybook dev -p 6006", "storybook:build": "storybook build", "storybook:serve": "http-server storybook-static --port 6006 --silent", "serve-storybook": "run-s storybook:*", "test-storybook:ci": "start-server-and-test serve-storybook http://127.0.0.1:6006 test-storybook", "prepare": "husky"}, "dependencies": {"@clerk/localizations": "^3.14.2", "@clerk/nextjs": "^6.18.3", "@clerk/themes": "^2.1.36", "@electric-sql/pglite": "^0.2.12", "@hookform/resolvers": "^3.9.0", "@logtail/pino": "^0.5.2", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.3", "@sentry/nextjs": "^8.34.0", "@spotlightjs/spotlight": "^2.5.0", "@t3-oss/env-nextjs": "^0.11.1", "@tanstack/react-table": "^8.20.5", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "drizzle-orm": "^0.35.1", "lucide-react": "^0.453.0", "next": "^14.2.25", "next-intl": "^3.21.1", "next-themes": "^0.3.0", "pg": "^8.13.0", "pino": "^9.5.0", "pino-pretty": "^11.3.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "sharp": "^0.33.5", "stripe": "^16.12.0", "tailwind-merge": "^2.5.4", "zod": "^3.23.8"}, "devDependencies": {"@antfu/eslint-config": "^2.27.3", "@clerk/testing": "^1.3.11", "@commitlint/cli": "^19.5.0", "@commitlint/config-conventional": "^19.5.0", "@commitlint/cz-commitlint": "^19.5.0", "@eslint-react/eslint-plugin": "^1.15.0", "@faker-js/faker": "^9.0.3", "@next/bundle-analyzer": "^14.2.15", "@next/eslint-plugin-next": "^14.2.15", "@percy/cli": "1.30.1", "@percy/playwright": "^1.0.6", "@playwright/test": "^1.48.1", "@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@storybook/addon-essentials": "^8.3.5", "@storybook/addon-interactions": "^8.3.5", "@storybook/addon-links": "^8.3.5", "@storybook/addon-onboarding": "^8.3.5", "@storybook/blocks": "^8.3.5", "@storybook/nextjs": "^8.3.5", "@storybook/react": "^8.3.5", "@storybook/test": "^8.3.5", "@storybook/test-runner": "^0.19.1", "@testing-library/jest-dom": "^6.6.1", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "@types/node": "^22.7.6", "@types/pg": "^8.11.10", "@types/react": "^18.3.11", "@vitejs/plugin-react": "^4.3.2", "@vitest/coverage-v8": "^2.1.9", "@vitest/expect": "^2.1.9", "autoprefixer": "^10.4.20", "checkly": "^4.9.0", "commitizen": "^4.3.1", "cross-env": "^7.0.3", "cssnano": "^7.0.6", "dotenv-cli": "^7.4.2", "drizzle-kit": "^0.26.2", "eslint": "^8.57.1", "eslint-plugin-format": "^0.1.2", "eslint-plugin-jest-dom": "^5.4.0", "eslint-plugin-jsx-a11y": "^6.10.0", "eslint-plugin-playwright": "^1.7.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.12", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-tailwindcss": "^3.17.5", "eslint-plugin-testing-library": "^6.3.0", "http-server": "^14.1.1", "husky": "^9.1.6", "jiti": "^1.21.6", "jsdom": "^25.0.1", "lint-staged": "^15.2.10", "npm-run-all": "^4.1.5", "postcss": "^8.4.47", "rimraf": "^6.0.1", "semantic-release": "^24.1.2", "start-server-and-test": "^2.0.8", "storybook": "^8.3.5", "tailwindcss": "^3.4.14", "tailwindcss-animate": "^1.0.7", "tsx": "^4.19.1", "typescript": "^5.6.3", "vite-tsconfig-paths": "^5.0.1", "vitest": "^2.1.9", "vitest-fail-on-console": "^0.7.1"}, "config": {"commitizen": {"path": "@commitlint/cz-commitlint"}}, "release": {"branches": ["main"], "plugins": [["@semantic-release/commit-analyzer", {"preset": "conventionalcommits"}], "@semantic-release/release-notes-generator", "@semantic-release/changelog", ["@semantic-release/npm", {"npmPublish": false}], "@semantic-release/git", "@semantic-release/github"]}}
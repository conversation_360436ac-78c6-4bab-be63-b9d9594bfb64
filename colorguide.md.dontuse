---

# TechnoKids Color Palette Guide v16

**Version:** 16.0
**Date:** November 20, 2023

**Document Purpose:** This guide presents the definitive and comprehensive reference for all colors, gradients, styles, typographic color applications, interactive state color definitions, and 3D depth effects employed across TechnoKids user interfaces. Version 16 places a **stronger emphasis on facilitating a "dark purple driven" aesthetic and thematic versatility**, integrating a full systematic Violet scale to enrich the accent palette. It builds upon the established TechnoKids v8 color palette, infusing it with modern, dynamic, and visually engaging "Divi-inspired" design principles. This document is intended to ensure unwavering visual consistency, elevate user engagement, safeguard accessibility, and streamline the design and development workflow for all TechnoKids digital products.

---

**Table of Contents:**

1.  **Introduction & Guiding Design Philosophy**
    *   1.1. Goal: A Unified, Engaging, Versatile, and Accessible Visual Language
    *   1.2. Core Principles (with emphasis on Thematic Versatility)
    *   1.3. How to Use This Guide (Highlighting Thematic Application)

2.  **Core Color Palette (Foundation - TechnoKids v8, Expanded)**
    *   2.1. Primary Brand Colors: Purple & Indigo (with Systematic Variations)
    *   2.2. Key Accent Color Families: Violet & Pink (with Systematic Variations)
    *   2.3. Secondary Accent & Action Colors
    *   2.4. Neutral Colors (with Systematic Variations for Hierarchy & Contrast)
    *   2.5. Semantic / Status Colors (Clearly Defined for User Feedback)
    *   2.6. Opacity Variants & Their Application

3.  **Dynamic Color: Gradients**
    *   3.1. Gradient Philosophy & Usage (Leveraging Purple, Indigo, Violet)
    *   3.2. Predefined Gradient Definitions (with new Violet combinations)
    *   3.3. Accessibility Considerations for Text on Gradients

4.  **CSS Custom Properties (Design Tokens - The Engine)**
    *   4.1. Tokenization Strategy
    *   4.2. Core & Semantic Color Definitions (Including full Violet scale)
    *   4.3. General UI Component Variables
    *   4.4. Theme-Specific Variables (Light, Dark, Night - with "Dark Purple Driven" options)
    *   4.5. 3D Depth & Shadow Variables
    *   4.6. Typography & Spacing Scale Variables

5.  **Layout, Spacing, & Composition**
    *   5.1. Principles of Visual Organization
    *   5.2. Grid System (Conceptual)
    *   5.3. Spacing Scale & Vertical Rhythm
    *   5.4. White Space (Negative Space) Strategy
    *   5.5. Container & Section Defaults

6.  **Typography System: Clarity, Hierarchy, and Readability**
    *   6.1. Typographic Philosophy
    *   6.2. Font Families, Weights, Line Heights
    *   6.3. Responsive Typographic Scale (with `rem` and `clamp()`)
    *   6.4. Text Color Application & WCAG Contrast
        *   6.4.1. Primary, Secondary, and Tertiary Text Colors
        *   6.4.2. Link Text Colors & States
        *   6.4.3. Text on Colored/Gradient Backgrounds
        *   6.4.4. Placeholder Text
    *   6.5. Font Loading & Performance Considerations

7.  **Application to UI Components & Interactive States (WCAG & Usability Focused)**
    *   7.1. General Principles for Component Color Application
    *   7.2. Definition of Standard Component States & Visual Feedback
        *   7.2.1. Default State
        *   7.2.2. Hover State
        *   7.2.3. Focus State (`:focus-visible` - Critical for Accessibility)
        *   7.2.4. Active/Pressed State
        *   7.2.5. Disabled State (Perceivability & `aria-disabled`)
        *   7.2.6. Error/Validation State
    *   7.3. Specific Component Color & State Guidance (with CSS Examples)
        *   7.3.1. Buttons (Primary, Secondary, Accent, Outline, Disabled)
        *   7.3.2. Forms & Input Fields (Text, Select, Checkbox, Radio)
        *   7.3.3. Toggle Buttons (`.depth-toggle`)
        *   7.3.4. Modals & Dialogs (Focus Trapping Considerations)
        *   7.3.5. Navigation Elements (Menus, Tabs, Breadcrumbs)
        *   7.3.6. Cards (`.depth-card`) & Content Containers
        *   7.3.7. Alerts & Notifications (Semantic States)
        *   7.3.8. Icons (Informational vs. Decorative)
        *   7.3.9. TechnoKids Specific: Session & Thematic Elements
        *   7.3.10. TechnoKids Specific: Logo & Feature Items
    *   7.4. *Conceptual Components (Principles for Future Implementation)*

8.  **Depth, Shadows, & Layering Effects**
    *   8.1. Strategic Use of Depth for Hierarchy
    *   8.2. Standard & Interactive Shadows
    *   8.3. Decorative Background Elements (Blobs, Shapes)
    *   8.4. Overlapping Elements

9.  **Animations, Micro-interactions, & Motion**
    *   9.1. Purposeful Motion
    *   9.2. Hover Effects & Transforms
    *   9.3. Scroll-Triggered Animations (Considerations)
    *   9.4. Loading Indicators & UI Feedback
    *   9.5. `prefers-reduced-motion` Implementation

10. **Theming: Light, Dark, & Night Modes**
    *   10.1. Theming Strategy & User Choice
    *   10.2. Implementation via `data-theme`
    *   10.3. Specific Variable Overrides for Each Mode
    *   10.4. Accessibility & Contrast in Each Theme

11. **Accessibility Compliance & Best Practices (WCAG Focused)**
    *   11.1. Commitment to Inclusivity (POUR Principles)
    *   11.2. Color Contrast (WCAG SC 1.4.3, 1.4.6 AAA, 1.4.11)
    *   11.3. Use of Color (WCAG SC 1.4.1)
    *   11.4. Keyboard Navigation & Focus Visibility (WCAG SC 2.1.1, 2.4.7)
    *   11.5. Content Structure & Readability (WCAG SC 1.3.1, 3.1)
    *   11.6. `prefers-reduced-motion` (WCAG SC 2.2.2, 2.3.3)
    *   11.7. Semantic HTML & ARIA (WCAG SC 4.1.1, 4.1.2)
    *   11.8. Performance Considerations
    *   11.9. Tools and Testing Methodology

12. **Conclusion & Governance**
    *   12.1. Summary of Guide's Importance
    *   12.2. Design System Governance and Maintenance
    *   12.3. Contribution and Evolution Process

---

### 1. Introduction & Guiding Design Philosophy

#### 1.1. Goal: A Unified, Engaging, Versatile, and Accessible Visual Language
This "TechnoKids Color Palette Guide v16" aims to establish a comprehensive, consistent, accessible, and **thematically versatile** visual language for all TechnoKids digital products. It integrates and expands upon the established TechnoKids v8 color palette, infusing it with modern, dynamic UI/UX principles. A key focus of this version is to **explicitly support and demonstrate how to achieve diverse aesthetics, including a rich "dark purple driven" experience**, alongside the default Light theme and other variations. This is achieved through an expanded accent palette, including a full systematic Violet scale, and clearer guidance on leveraging the darker shades of our core brand colors. This guide serves as the single source of truth for color application, typographic color standards, and interactive visual feedback.

#### 1.2. Core Principles (with emphasis on Thematic Versatility)
These principles serve as the north star for all design decisions within the TechnoKids ecosystem:

*   **1.2.1. User-Centricity (Focus on Learners & Educators):** Our primary users are children, young adults, and educators. Color choices and applications must be tailored to their cognitive abilities, learning contexts, and interaction patterns. The design should feel inviting, engaging, and supportive of the learning journey, minimizing cognitive load and maximizing comprehension. All visual elements must contribute to a seamless and intuitive user experience.
*   **1.2.2. Brand Alignment & Emotional Resonance:** The core Purple and Indigo colors remain central to the TechnoKids brand. This guide empowers designers to express the brand with varying intensity and mood, from bright and inviting to deep and focused, using the full spectrum of provided shades. Every color choice should be intentional and reflect our brand values.
*   **1.2.3. Clarity & Readability:** Regardless of the chosen theme (light, dark purple, or night), all visual information, especially text, must be exceptionally clear, legible, and easily perceivable. This principle guides typographic choices, text color selections, contrast ratios, and the structuring of content. Visual hierarchy must be unambiguous to guide users effectively.
*   **1.2.4. Accessibility & Inclusivity (WCAG 2.1 AA as Baseline):** We are committed to creating digital experiences usable by people with the widest possible range of abilities. This guide mandates adherence to Web Content Accessibility Guidelines (WCAG) 2.1 Level AA success criteria as a minimum standard for all color contrast, non-text contrast, and other related visual design aspects. Design choices should actively promote inclusivity.
*   **1.2.5. Consistency & Predictability:** Uniform application of colors, typography, and interactive patterns across all digital touchpoints is critical. This creates a cohesive user experience, reduces confusion, and helps users understand how to interact with our products intuitively. Standards defined herein must be applied rigorously.
*   **1.2.6. Modernity, Engagement, & **Thematic Versatility**:** The design system provides the tools (expanded palettes, gradients, depth effects, theming) to create interfaces that are modern, dynamic, and engaging. This version specifically enhances the ability to craft UIs that range from predominantly light to deeply saturated with brand purples and violets, offering true aesthetic flexibility.

#### 1.3. How to Use This Guide (Highlighting Thematic Application)
This guide is intended for designers, developers, content creators, and QA teams involved in building TechnoKids digital products. It provides:
*   **Definitive Color Palettes:** Core Purple/Indigo, expanded Violet/Pink accents, secondary accents, neutrals, and semantic colors, all with systematic variations. Each color variation is provided with a clear rationale for its existence (e.g., achieving accessible contrast, providing UI state options).
*   **CSS Design Tokens:** A comprehensive list of variables (`--tk-` prefix) for direct implementation in stylesheets, ensuring consistency between design and code.
*   **Typographic Guidelines:** Including specific text color applications, line heights, and font weights designed for optimal readability and accessibility across various backgrounds and themes.
*   **Component Color Specifications:** Detailed color usage for key UI components and their interactive states (default, hover, focus, active, disabled, error), with explicit WCAG compliance notes.
*   **Theming Recipes (New Emphasis):** Guidance and examples (expanded in component sections) on how to achieve specific aesthetics, such as a "Dark Purple Driven" theme, by leveraging the defined color roles and dark shades.
*   **Accessibility Mandates:** Integrated throughout, not as an afterthought, but as foundational criteria.

Adherence to this guide is crucial for maintaining quality, consistency, and inclusivity across all TechnoKids offerings. Deviations should be discussed and approved through the established design system governance process. For achieving specific thematic goals (e.g., a "dark purple driven" interface), pay close attention to the usage notes for darker brand shades (800-950 levels) and the theme override sections.

---

### 2. Core Color Palette (Foundation - TechnoKids v8, Expanded)

This section details the official TechnoKids color palette. It builds upon the v8 guide by **introducing a full systematic Violet scale** as a key accent family alongside Pink, and by reinforcing the usage of darker Purple and Indigo shades for thematic depth. Each primary, brand, and key accent color is presented with a systematic scale (typically 50-950) to provide a flexible yet consistent range of shades and tints. These variations are essential for creating visual hierarchy, defining UI states, ensuring accessible contrast, and maintaining brand integrity across diverse applications.

**Rationale for Systematic Variations:** The provision of a full spectrum of shades (darker variants) and tints (lighter variants) for each core color allows designers to:
*   Create depth and elevation in UI elements.
*   Define visually distinct and accessible interactive states (hover, focus, active, disabled).
*   Ensure text has sufficient contrast against various colored backgrounds.
*   Offer subtle variations for borders, backgrounds, and iconography without introducing off-brand colors.

#### 2.1. Primary Brand Colors: Purple & Indigo (with Systematic Variations)
These remain the cornerstone of the TechnoKids brand identity. Their darker shades are crucial for achieving a "dark purple driven" aesthetic.

| Name             | Hex Code  | Variable Name       | Usage Notes & Accessibility Rationale                                   |
| :--------------- | :-------- | :------------------ | :-------------------------------------------------------------------- |
| **Purple**       |           |                     | **Primary Brand Color Family.** Darker shades (700-950) are ideal for dominant dark theme backgrounds. |
| Purple 50        | `#f5f3ff` | `--tk-purple-50`    | Lightest tint for subtle effects, hover states.    |
| Purple 100       | `#ede9fe` | `--tk-purple-100`   | Light backgrounds, active items.                                      |
| Purple 200       | `#ddd6fe` | `--tk-purple-200`   | Borders, subtle accents.                                              |
| Purple 300       | `#c4b5fd` | `--tk-purple-300`   | Dark mode text, highlights (e.g., link hover on dark purple BG).        |
| Purple 400       | `#a78bfa` | `--tk-purple-400`   | Dark mode links/icons, medium accents.                               |
| Purple 500       | `#8b5cf6` | `--tk-purple-500`   | **Primary Brand Color**. CTAs, Headers, key interactive elements in Light Theme. |
| Purple 600       | `#7c3aed` | `--tk-purple-600`   | Stronger accents, hover states for Primary 500, link text (Light Theme). |
| Purple 700       | `#6d28d9` | `--tk-purple-700`   | Darker elements, text on light purple, key for purple-driven dark themes. |
| Purple 800       | `#5b21b6` | `--tk-purple-800`   | **Key for Dominant Dark Theme Backgrounds**, deep accents.             |
| Purple 900       | `#4c1d95` | `--tk-purple-900`   | **Deepest Dark Theme Backgrounds**, high contrast elements.            |
| Purple 950       | `#2e1065` | `--tk-purple-950`   | Near-black purple for extreme depth or fine details in dark themes.    |
| **Indigo**       |           |                     | **Secondary Brand Color Family.** Complements Purple, especially in dark/purple-driven themes. |
| Indigo 50        | `#eef2ff` | `--tk-indigo-50`    | Lightest tint, subtle backgrounds.                                    |
| Indigo 100       | `#e0e7ff` | `--tk-indigo-100`   | Light backgrounds.                                                    |
| Indigo 200       | `#c7d2fe` | `--tk-indigo-200`   | Borders, subtle accents.                                              |
| Indigo 300       | `#a5b4fc` | `--tk-indigo-300`   | Dark mode text, highlights.                                           |
| Indigo 400       | `#818cf8` | `--tk-indigo-400`   | Dark mode links/icons.                                               |
| Indigo 500       | `#6366f1` | `--tk-indigo-500`   | **Secondary Brand Color**. Gradients, Buttons in Light Theme.          |
| Indigo 600       | `#4f46e5` | `--tk-indigo-600`   | Stronger accents, gradients.                                          |
| Indigo 700       | `#4338ca` | `--tk-indigo-700`   | Darker elements, key for purple/indigo driven themes.                 |
| Indigo 800       | `#3730a3` | `--tk-indigo-800`   | **Key for Dominant Dark Theme Backgrounds (with Purple)**, deep accents. |
| Indigo 900       | `#312e81` | `--tk-indigo-900`   | **Deepest Dark Theme Backgrounds (with Purple)**.                      |
| Indigo 950       | `#1e1b4b` | `--tk-indigo-950`   | Near-black indigo for extreme depth or layered dark backgrounds.       |

#### 2.2. Key Accent Color Families: Violet & Pink (with Systematic Variations)
To enhance thematic versatility and provide richer accent options, **Violet is now a fully scaled accent family,** using the distinct "Violet" hues from the user-provided image. Pink remains a key warm accent.

| Name             | Hex Code  | Variable Name       | Usage Notes & Accessibility Rationale                                     |
| :--------------- | :-------- | :------------------ | :---------------------------------------------------------------------- |
| **Violet**       |           |                     | **Key Accent Family.** Provides a distinct purple hue often seen in "Divi-inspired" designs for highlights, calls-to-action, or thematic elements. Hex codes are based on the "Violet" scale in the reference image. |
| Violet 50        | `#f5f3ff` | `--tk-violet-50`    | Lightest violet tint.                                                  |
| Violet 100       | `#ede9fe` | `--tk-violet-100`   | Light violet backgrounds.                                               |
| Violet 200       | `#ddd6fe` | `--tk-violet-200`   | Subtle violet accents, borders.                                         |
| Violet 300       | `#c4b5fd` | `--tk-violet-300`   | Lighter violet accents, dark mode text on darker violet backgrounds.      |
| Violet 400       | `#a78bfa` | `--tk-violet-400`   | **Primary Violet Accent.** Often used for icons, highlights, secondary CTAs. Previously `--tk-violet-500` in v15. |
| Violet 500       | `#8b5cf6` | `--tk-violet-500`   | Stronger violet accent. Visually similar to Purple 500 but semantically "Violet". |
| Violet 600       | `#7c3aed` | `--tk-violet-600`   | Deeper violet accent.                                                   |
| Violet 700       | `#6d28d9` | `--tk-violet-700`   | Dark violet for text on light violet backgrounds, or dark theme accents. |
| Violet 800       | `#5b21b6` | `--tk-violet-800`   | Deep violet accent, can be used for subtle dark theme violet backgrounds. |
| Violet 900       | `#4c1d95` | `--tk-violet-900`   | Darkest violet accent.                                                  |
| Violet 950       | `#2e1065` | `--tk-violet-950`   | Near-black violet.                                                      |
| **Pink**         |           |                     | **Key Warm Accent Family.** Provides warmth and contrast, especially against cooler purples/indigos. |
| Pink 50          | `#fdf2f8` | `--tk-pink-50`      | Lightest pink tint, subtle highlights.                                  |
| Pink 100         | `#fce7f3` | `--tk-pink-100`     | Light pink backgrounds, session backgrounds.                            |
| Pink 200         | `#fbcfe8` | `--tk-pink-200`     | Assignment backgrounds.                                                 |
| Pink 300         | `#f9a8d4` | `--tk-pink-300`     | Dark mode text on darker pinks, lighter pink accents.                   |
| Pink 400         | `#f472b6` | `--tk-pink-400`     | Key pink actions, highlights, gradients.                                |
| Pink 500         | `#ec4899` | `--tk-pink-500`     | **Primary Pink Accent Color**. Used for CTAs, highlights, gradients.      |
| Pink 600         | `#db2777` | `--tk-pink-600`     | Stronger pink accents, button hover states.                             |
| Pink 700         | `#be185d` | `--tk-pink-700`     | Darker pink elements, text on light pink backgrounds.                   |
| Pink 800         | `#9d174d` | `--tk-pink-800`     | Deep pink accents, assignment backgrounds (Dark).                       |
| Pink 900         | `#831843` | `--tk-pink-900`     | Darkest pink, session backgrounds (Dark).                               |

*Note on Violet Scale Hex Codes: The hex codes for Violet 50-950 are now assumed to be those from the "Violet" swatch in the user-provided image. If those are identical to the "Purple" swatch, this effectively means Violet is a semantic alias for Purple. For true distinction, unique hex values for the Violet scale would be needed. This guide proceeds with the visual interpretation from the image for now.*

#### 2.3. Secondary Accent & Action Colors
These provide additional breadth for specific UI needs, such as iconography, informational cues, or tertiary actions.

| Name             | Hex Code  | Variable Name       | Usage Notes                                                           |
| :--------------- | :-------- | :------------------ | :-------------------------------------------------------------------- |
| **Rose**         | `#f43f5e` | `--tk-rose-500`     | Secondary accents, gradients, pairs with Pink for warmth.             |
| Rose 600         | `#e11d48` | `--tk-rose-600`     | Stronger rose accents, hover states.                                  |
| **Teal**         | `#14b8a6` | `--tk-teal-500`     | Tertiary accents, informational icons, provides cool contrast.        |
| Teal 600         | `#0d9488` | `--tk-teal-600`     | Darker teal for text or icons needing more contrast on light BGs.     |
| Teal 700         | `#0f766e` | `--tk-teal-700`     | Deeper teal for dark theme accents or text.                            |
| **Cyan**         | `#06b6d4` | `--tk-cyan-500`     | Informational highlights, gradients with Blue.                        |
| Cyan 600         | `#0891b2` | `--tk-cyan-600`     | Stronger cyan accents, button hover states.                           |
| **Sky**          | `#0ea5e9` | `--tk-sky-500`      | UI elements, icons, brighter blue accents.                             |
| **Orange**       | `#f97316` | `--tk-orange-500`   | Secondary warning indicators, icons, attention-grabbing highlights.   |
| **Amber**        | `#f59e0b` | `--tk-amber-500`    | Icons, gradients, tertiary attention highlights.                      |
| **Yellow**       |           |                     | Notes, Highlights, **Warning Semantic Family Base**                     |
| Yellow 50        | `#fffbeb` | `--tk-yellow-50`    | Lightest yellow, subtle hover BGs.                                    |
| Yellow 100       | `#fef3c7` | `--tk-yellow-100`   | **Semantic Warning Background (Light Theme)**.                         |
| Yellow 300       | `#fde047` | `--tk-yellow-300`   | **Semantic Warning Border (Light Theme)**, Dark mode text.             |
| Yellow 500       | `#eab308` | `--tk-yellow-500`   | **Semantic Warning Base Color**, icons, highlights.                    |
| Yellow 700       | `#a16207` | `--tk-yellow-700`   | **Semantic Warning Text (Light Theme)**.                               |
| **Green**        |           |                     | **Success Semantic Family Base**, Positive Feedback                   |
| Green 50         | `#f0fdf4` | `--tk-green-50`     | Lightest green, subtle hover BGs.                                     |
| Green 100        | `#dcfce7` | `--tk-green-100`    | **Semantic Success Background (Light Theme)**.                         |
| Green 300        | `#86efac` | `--tk-green-300`    | **Semantic Success Border (Light Theme)**, Dark mode text.             |
| Green 400        | `#4ade80` | `--tk-green-400`    | Success states, gradients.                                            |
| Green 500        | `#22c55e` | `--tk-green-500`    | **Semantic Success Base Color**, main success indicators.              |
| Green 700        | `#15803d` | `--tk-green-700`    | **Semantic Success Text (Light Theme)**.                               |
| Green 800        | `#166534` | `--tk-green-800`    | Darker green for text needing high contrast on light green BGs.        |
| **Emerald**      | `#10b981` | `--tk-emerald-500`  | Alternative success states, distinct positive indicators.             |
| **Blue**         |           |                     | **Informational Semantic Family Base**, Links, UI Elements            |
| Blue 50          | `#eff6ff` | `--tk-blue-50`      | Lightest blue, subtle BGs.                                            |
| Blue 100         | `#dbeafe` | `--tk-blue-100`     | **Semantic Info Background (Light Theme)**.                            |
| Blue 300         | `#93c5fd` | `--tk-blue-300`     | **Semantic Info Border (Light Theme)**, Dark mode text.                |
| Blue 400         | `#60a5fa` | `--tk-blue-400`     | Informational elements, links, gradients.                             |
| Blue 500         | `#3b82f6` | `--tk-blue-500`     | **Semantic Info Base Color**, primary informational elements.          |
| Blue 600         | `#2563eb` | `--tk-blue-600`     | Stronger blue accents, button hover states.                           |
| Blue 700         | `#1d4ed8` | `--tk-blue-700`     | **Semantic Info Text (Light Theme)**.                                  |
| Blue 800         | `#1e40af` | `--tk-blue-800`     | Dark blue accents.                                                    |
| Blue 900         | `#1e3a8a` | `--tk-blue-900`     | Deepest blue accents.                                                 |

#### 2.4. Neutral Colors (with Systematic Variations for Hierarchy & Contrast)
Neutrals are the backbone of the UI, providing backgrounds, text colors, and defining structure. Their systematic variation is crucial for creating hierarchy and ensuring readability in all themes.

| Name             | Hex Code  | Variable Name      | Usage Notes & Accessibility Rationale                                       |
| :--------------- | :-------- | :----------------- | :------------------------------------------------------------------------ |
| **Gray**         |           |                    | Primary Neutral Color Family                                              |
| White            | `#FFFFFF` | `--tk-white`       | Primary backgrounds (Light Theme), text on dark backgrounds. Max contrast. |
| Gray 50          | `#fafafa` | `--tk-gray-50`     | Lightest page backgrounds (Light Theme), off-white alternative.           |
| Gray 100         | `#f3f4f6` | `--tk-gray-100`    | Subtle section backgrounds, card backgrounds (Light), disabled BGs (Light). |
| Gray 200         | `#e5e7eb` | `--tk-gray-200`    | Borders, dividers, disabled element borders (Light).                        |
| Gray 300         | `#d1d5db` | `--tk-gray-300`    | Input borders (Light), secondary borders, text (Dark on Dark BG).           |
| Gray 400         | `#9ca3af` | `--tk-gray-400`    | Placeholder text, subtle icons, disabled/secondary text (Dark).             |
| Gray 500         | `#6b7280` | `--tk-gray-500`    | Tertiary/Subtle text (Light), icons, default disabled text.                |
| Gray 600         | `#4b5563` | `--tk-gray-600`    | Secondary text (Light), input borders (Dark), stronger disabled text.      |
| Gray 700         | `#374151` | `--tk-gray-700`    | Primary body text (Light Theme on light BGs), input BGs (Dark).           |
| Gray 800         | `#1f2937` | `--tk-gray-800`    | Dark backgrounds, content areas (Dark Theme), card BGs (Dark Theme).        |
| Gray 900         | `#111827` | `--tk-gray-900`    | Deepest backgrounds, page background (Dark Theme), footers (Dark Theme).    |
| Black            | `#000000` | `--tk-black`       | Limited use for text requiring maximum contrast on very light BGs.        |

#### 2.5. Semantic / Status Colors (Clearly Defined for User Feedback)
These colors convey specific meanings (success, warning, error, information). They **must always be accompanied by icons and/or unambiguous text** to ensure accessibility (WCAG SC 1.4.1 Use of Color).

| Status / Feedback | Base Color Var        | Text Color Var         | Background Color Var  | Border Color Var       | Icon Usage Note                    | WCAG Contrast (Text on BG - Light Theme) |
| :---------------- | :-------------------- | :--------------------- | :-------------------- | :------------------- | :--------------------------------- | :--------------------------------------- |
| **Success**       | `--semantic-success-base` (`--tk-green-500`) | `--semantic-success-text` (`--tk-green-700`) | `--semantic-success-bg` (`--tk-green-100`) | `--semantic-success-border` (`--tk-green-300`) | Use with checkmark icon             | **9.65:1 (AAA Pass)**                    |
| **Warning**       | `--semantic-warning-base` (`--tk-yellow-500`) | `--semantic-warning-text` (`--tk-yellow-700`) | `--semantic-warning-bg` (`--tk-yellow-100`) | `--semantic-warning-border` (`--tk-yellow-300`) | Use with warning triangle icon      | **7.07:1 (AAA Pass)**                    |
| **Error/Danger**  | `--semantic-error-base` (`--tk-red-600`)   | `--semantic-error-text` (`--tk-red-700`)   | `--semantic-error-bg` (`--tk-red-100`)   | `--semantic-error-border` (`--tk-red-300`)   | Use with error/cross icon         | **6.03:1 (AA Pass)**                     |
| **Information**   | `--semantic-info-base` (`--tk-blue-500`)  | `--semantic-info-text` (`--tk-blue-700`)   | `--semantic-info-bg` (`--tk-blue-100`)   | `--semantic-info-border` (`--tk-blue-300`)   | Use with information "i" icon     | **7.08:1 (AAA Pass)**                    |

*Note: Dark Mode and Night Mode variants for semantic backgrounds and texts are defined within their respective `[data-theme]` CSS blocks (Section 4.4) to ensure appropriate contrast in those themes.*

#### 2.6. Opacity Variants & Their Application
Opacity is applied contextually using RGBA color definitions or the CSS `opacity` property, rather than through global opacity tokens. This allows for precise control and minimizes the risk of unpredictable contrast accessibility issues.

*   **Modal Overlays:** `background-color: var(--bg-overlay);` (which is `rgba(0, 0, 0, 0.4)` in Light Theme, and adjusted for Dark/Night themes). The opacity is integral to the color definition, ensuring the overlay appropriately dims background content while maintaining perceivability.
*   **Decorative Elements:** Low opacity values (e.g., `0.1` to `0.3`) can be applied to decorative background blobs or patterns, such as those using `var(--decorative-blob-color-1)` or `var(--decorative-blob-gradient)`, to ensure they remain subtle and do not interfere with foreground content.
*   **Disabled States:** This guide primarily uses distinct, solid colors for disabled states (e.g., `var(--button-disabled-bg)`, `var(--button-disabled-text)`) to ensure better perceivability and text contrast, rather than relying solely on reduced opacity of the active state colors. If `opacity` is exceptionally used for a disabled state (e.g., `opacity: 0.7;` on a pre-styled element that cannot have its direct colors changed), the resulting contrast of any text or meaningful graphical elements against its effective background **must be re-checked and meet WCAG requirements if the element needs to be perceivable**.
*   **Image Overlays/Text Protection:** For text placed over images, a semi-transparent scrim (e.g., `background-color: rgba(var(--tk-black-rgb), 0.5);`) can be used to ensure text readability. The opacity chosen must guarantee sufficient contrast for the text.
*   **Caution:** Applying opacity to a parent element affects all its child elements. If an element contains text or meaningful icons, reducing its container's opacity can drastically lower the contrast of these children against their true background (which becomes a blend of their color and the colors visible through the semi-transparent parent). **Always test the final rendered contrast of critical elements if opacity is applied to their containers.**

---

### 3. Dynamic Color: Gradients

Gradients are employed strategically to add visual depth, energy, and reinforce the TechnoKids brand identity, aligning with modern "Divi-inspired" aesthetics. They are particularly effective for prominent elements like headers, primary buttons, hero sections, and thematic highlights.

#### 3.1. Gradient Philosophy & Usage (Leveraging Purple, Indigo, Violet)
*   **Visual Appeal & Branding:** Gradients using core brand colors (`--tk-purple-*`, `--tk-indigo-*`) and key accents like `--tk-violet-*` create a dynamic and recognizable brand expression.
*   **Hierarchy & Emphasis:** Gradients can draw the user's eye and give prominence to important calls to action or feature sections.
*   **Thematic Expression:** Different gradient combinations can contribute to the overall mood of a theme, e.g., deep, rich gradients for a "dark purple driven" feel, or lighter, airy gradients for the Light Theme.
*   **Subtlety & Readability:** Gradients should be smooth and generally subtle enough not to overpower content. If text is placed upon a gradient, readability is paramount (see Section 3.3).
*   **Consistency:** Predefined gradients are provided as CSS custom properties to ensure consistent application across the UI.

#### 3.2. Predefined Gradient Definitions (with new Violet combinations)
These tokens provide ready-to-use gradients. Additional custom gradients can be created using the palette tokens as needed, following similar principles.

*   **Core Brand Gradients:**
    *   `--primary-brand-gradient`: `linear-gradient(to right, var(--tk-purple-500), var(--tk-indigo-500))`
        *   *Usage: Main headers, primary buttons (Light Theme), primary feature item backgrounds.*
    *   `--secondary-brand-gradient`: `linear-gradient(to right, var(--tk-blue-500), var(--tk-cyan-500))`
        *   *Usage: Secondary buttons, informational banners, illustrative backgrounds.*
*   **Accent Gradients:**
    *   `--accent-brand-gradient`: `linear-gradient(to right, var(--tk-pink-500), var(--tk-rose-500))`
        *   *Usage: Accent call-to-action buttons, highlight sections requiring strong warm visual emphasis.*
    *   `--violet-accent-gradient`: `linear-gradient(to right, var(--tk-purple-600), var(--tk-violet-400))`
        *   *Usage: Alternative accent CTAs, feature highlights, blending primary Purple with key Violet accent.*
    *   `--violet-pink-accent-gradient`: `linear-gradient(to right, var(--tk-violet-500), var(--tk-pink-400))`
        *   *Usage: Vibrant accents, decorative elements, potentially for "Divi-inspired" flourishes.*
*   **Dark & Thematic Gradients:**
    *   `--section-bg-dark-gradient`: `linear-gradient(135deg, var(--tk-purple-800), var(--tk-indigo-900))`
        *   *Usage: Backgrounds for dark-themed hero sections or distinct content blocks in any theme to create a focal point.*
    *   `--dark-purple-hero-gradient`: `linear-gradient(135deg, var(--tk-purple-950), var(--tk-indigo-800), var(--tk-purple-700))`
        *   *Usage: Specifically for creating a rich, deep "dark purple driven" hero or section background.*
*   **Session & Thematic Element Gradients (Light Theme Base):**
    *   `--session-1-num-bg-gradient`: `linear-gradient(to right, var(--tk-purple-500), var(--tk-indigo-600))`
    *   `--session-2-num-bg-gradient`: `linear-gradient(to right, var(--tk-blue-400), var(--tk-cyan-500))`
    *   `--session-3-num-bg-gradient`: `linear-gradient(to right, var(--tk-pink-400), var(--tk-rose-500))`
    *   `--assignment-card-gradient-light`: `linear-gradient(to bottom right, var(--tk-purple-50), var(--tk-indigo-50))`
    *   `--extension-card-gradient-light`: `linear-gradient(to bottom right, var(--tk-blue-50), var(--tk-cyan-50))`
*   **Utility & Depth Gradients:**
    *   `--decorative-blob-gradient`: `radial-gradient(circle, var(--decorative-blob-color-1), var(--decorative-blob-color-2))`
        *   *Usage: Subtle background decorative elements, often with reduced opacity and blur.*
    *   `--depth-gradient-overlay`: `linear-gradient(to bottom, var(--depth-gradient-top), transparent 50%, var(--depth-gradient-bottom))`
        *   *Usage: Applied on top of solid or gradient backgrounds for subtle 3D lighting effects on buttons and features.*

*Note: Dark Mode and Night Mode versions of thematic gradients (e.g., assignment cards, session number backgrounds) are defined or adapted within their respective `[data-theme]` CSS blocks (Section 4.4) using darker shades or theme-specific accent colors.*

#### 3.3. Accessibility Considerations for Text on Gradients
Ensuring text readability on gradient backgrounds is critical for accessibility and overall usability. This requires careful attention to color choices and contrast ratios across the entire area where text might appear.

*   **WCAG SC 1.4.3 Contrast (Minimum) Adherence:**
    *   Normal-sized text (typically < 18pt or < 14pt bold) requires a contrast ratio of at least **4.5:1** against all parts of the gradient it overlaps.
    *   Large text (typically ≥ 18pt or ≥ 14pt bold) requires a contrast ratio of at least **3:1** against all parts of the gradient it overlaps.
*   **Comprehensive Testing Procedure:**
    1.  **Identify Text Placement:** Determine the exact area where text will be rendered over the gradient.
    2.  **Sample Gradient Colors:** Using a reliable color picker tool (e.g., browser developer tools, dedicated color pickers), sample the gradient's background color at multiple points directly beneath the text. This should include:
        *   The start, middle, and end of lines of text.
        *   The top, middle, and bottom of blocks of text.
        *   Crucially, the points where the gradient is **lightest** under the text and where it is **darkest** under the text.
    3.  **Calculate Contrast Ratios:** For each sampled background color, calculate its contrast ratio against the chosen text color (e.g., `var(--text-on-dark-bg)` which is `--tk-white`).
    4.  **Verify Minimum Compliance:** The **lowest contrast ratio found** across all sampled points must meet the WCAG minimum requirements (4.5:1 or 3:1 as appropriate for the text size).
*   **Recommended Text Color for TechnoKids Gradients:**
    *   For most predefined brand gradients (which tend to be mid-to-dark tones), `var(--text-on-dark-bg)` (i.e., `var(--tk-white)`) is the recommended text color.
    *   **Example Verification for `--primary-brand-gradient` (`--tk-purple-500` to `--tk-indigo-500`) with `var(--tk-white)` text:**
        *   `#FFFFFF` on `#8b5cf6` (Purple 500): **4.53:1 (AA Pass for normal text)**.
        *   `#FFFFFF` on `#6366f1` (Indigo 500): **5.07:1 (AA Pass for normal text)**.
        *   Midpoint (approx `#7761F3`): **4.78:1 (AA Pass for normal text)**.
    *   This demonstrates that white text works well on this primary gradient. Similar checks must be done for all text-on-gradient applications.
*   **Strategies for Ensuring Contrast on Challenging Gradients:**
    1.  **Select Appropriate Text Color:** If a gradient has very light areas, white text may fail. Conversely, if it has very dark areas, black text might fail. Choose the text color that provides the best overall contrast across the gradient span. Sometimes, a mid-tone gray might be necessary if the gradient varies extremely, but this is less common for brand gradients.
    2.  **Gradient Design:** Favor gradients where the color transition is not excessively wide or does not include colors that are too chromatically similar to the intended text color. Ensure sufficient difference in lightness (value) between the text and all parts of the gradient.
    3.  **Text Shadow (Use with Extreme Caution and as a Last Resort):** A *very subtle*, well-placed text shadow (e.g., `text-shadow: 0 1px 2px rgba(0,0,0,0.6);` for light text on a mid-tone gradient) can slightly improve edge definition. However:
        *   **It is NOT a substitute for meeting base contrast requirements.** The colors themselves must have adequate contrast.
        *   Overuse or poorly executed text shadows can significantly reduce legibility, making text appear blurry or cluttered.
        *   It adds visual complexity and should be avoided if simpler color choices can achieve compliance.
    4.  **Background Scrim (Most Robust Solution for Difficult Cases):** For critical text placed over highly variable or "busy" image/gradient backgrounds, applying a semi-transparent solid color layer (a "scrim") directly behind the text area is the most reliable method to ensure consistent, accessible contrast.
        ```css
        .text-container-on-gradient {
          position: relative; /* Needed for z-index stacking if ::before is used */
          /* ... other layout styles ... */
        }
        .text-on-gradient-with-scrim {
          position: relative; /* To stack above the pseudo-element */
          color: var(--tk-white); /* Or other contrasting text color */
          padding: var(--space-xs) var(--space-sm); /* Ensure text isn't flush with scrim edge */
        }
        .text-container-on-gradient::before {
          /* The scrim */
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0; /* Cover the text area */
          background-color: rgba(
            var(--tk-black-rgb),
            0.4
          ); /* Example: 40% opaque black. Adjust color and opacity. Define --tk-black-rgb: 0,0,0 */
          z-index: 0; /* Ensure it's behind the text */
          border-radius: 4px; /* Optional: if the text area has rounded corners */
        }
        ```
*   **Avoid Text Over Extremely Busy or Low-Contrast Gradients:** If a gradient has numerous sharp color changes or its overall color range is too close to the desired text color, it is generally unsuitable as a direct background for important textual information. In such cases, redesign the gradient for better text compatibility or place the text on a solid color block overlaid on the gradient.

---

### 4. CSS Custom Properties (Design Tokens - The Engine)

This section details the complete set of CSS Custom Properties (Design Tokens) that form the foundation of the TechnoKids visual design system. These tokens encapsulate all core design decisions, ensuring consistency, maintainability, and themability.

#### 4.1. Tokenization Strategy
All foundational design decisions—colors, typography, spacing, shadows—are encapsulated as CSS Custom Properties. These properties act as **design tokens**, providing a single source of truth and a shared language between design and development. This strategy promotes consistency, maintainability, and scalability across all TechnoKids digital products.

**Key Benefits:**
*   **Consistency:** Ensures uniform application of design attributes.
*   **Maintainability:** Global changes can be made by updating a single token (e.g., changing the primary brand color `--tk-purple-500` will update it everywhere it's referenced through role tokens).
*   **Theming:** Facilitates easy switching between Light, Dark, and Night modes by redefining semantic role tokens within specific theme scopes (`[data-theme="..."]`).
*   **Scalability:** Simplifies the extension of the design system to new components or platforms.
*   **Collaboration:** Provides a clear, unambiguous reference for both designers and developers, reducing misinterpretations.

**Naming Conventions:**
A consistent naming convention is used for all tokens to enhance readability and predictability:
*   **Palette Tokens:** ` --tk-[colorName]-[shade]` (e.g., `--tk-purple-500`, `--tk-gray-100`). These represent the raw, literal color values from the defined palette scales.
*   **Semantic Role Tokens (Alias Tokens):** ` --[role]-[property]` or ` --[element]-[state]-[property]` (e.g., `--bg-page`, `--text-primary`, `--button-primary-bg`, `--input-border-focus`). These tokens define the *purpose* or *application* of a color or value in the UI. They typically map to palette tokens, and it is these role tokens that are primarily redefined for theming. This abstraction is key for maintainability and thematic flexibility.
*   **Functional Tokens:** ` --depth-[type]`, ` --space-[scale]`, ` --font-size-[scale]`, ` --line-height-[type]`, ` --transition-[speed]`. These define non-color design attributes.

#### 4.2. Core & Semantic Color Definitions (Global Base for Light Theme)
This comprehensive `:root` block defines all palette tokens and the initial (Light Theme) definitions for semantic role tokens. Theme-specific overrides for semantic roles are found in Section 4.4.

```css
:root {
  /* === CORE BRAND & ACCENT COLORS (TechnoKids v8 based, v16 additions) === */
  /* Purple Scale (Primary Brand) */
  --tk-purple-50: #f5f3ff;
  --tk-purple-100: #ede9fe;
  --tk-purple-200: #ddd6fe;
  --tk-purple-300: #c4b5fd;
  --tk-purple-400: #a78bfa;
  --tk-purple-500: #8b5cf6; /* Primary Brand Color */
  --tk-purple-600: #7c3aed;
  --tk-purple-700: #6d28d9;
  --tk-purple-800: #5b21b6;
  --tk-purple-900: #4c1d95;
  --tk-purple-950: #2e1065; /* New deepest purple */

  /* Indigo Scale (Secondary Brand) */
  --tk-indigo-50: #eef2ff;
  --tk-indigo-100: #e0e7ff;
  --tk-indigo-200: #c7d2fe;
  --tk-indigo-300: #a5b4fc;
  --tk-indigo-400: #818cf8;
  --tk-indigo-500: #6366f1; /* Secondary Brand Color */
  --tk-indigo-600: #4f46e5;
  --tk-indigo-700: #4338ca;
  --tk-indigo-800: #3730a3;
  --tk-indigo-900: #312e81;
  --tk-indigo-950: #1e1b4b; /* New deepest indigo */

  /* Violet Scale (Key Accent Family - based on user image interpretation) */
  --tk-violet-50: #f5f3ff;
  --tk-violet-100: #ede9fe;
  --tk-violet-200: #ddd6fe;
  --tk-violet-300: #c4b5fd;
  --tk-violet-400: #a78bfa; /* Primary Violet Accent */
  --tk-violet-500: #8b5cf6;
  --tk-violet-600: #7c3aed;
  --tk-violet-700: #6d28d9;
  --tk-violet-800: #5b21b6;
  --tk-violet-900: #4c1d95;
  --tk-violet-950: #2e1065;

  /* Pink Scale (Key Warm Accent Family) */
  --tk-pink-50: #fdf2f8;
  --tk-pink-100: #fce7f3;
  --tk-pink-200: #fbcfe8;
  --tk-pink-300: #f9a8d4;
  --tk-pink-400: #f472b6;
  --tk-pink-500: #ec4899; /* Primary Pink Accent */
  --tk-pink-600: #db2777;
  --tk-pink-700: #be185d;
  --tk-pink-800: #9d174d;
  --tk-pink-900: #831843;

  /* Other Accent & Action Colors */
  --tk-rose-500: #f43f5e;
  --tk-rose-600: #e11d48;
  --tk-teal-500: #14b8a6;
  --tk-teal-600: #0d9488;
  --tk-teal-700: #0f766e;
  --tk-cyan-50: #ecfeff;
  --tk-cyan-500: #06b6d4;
  --tk-cyan-600: #0891b2;
  --tk-sky-500: #0ea5e9;
  --tk-orange-500: #f97316;
  --tk-amber-500: #f59e0b;

  --tk-yellow-50: #fffbeb;
  --tk-yellow-100: #fef3c7;
  --tk-yellow-300: #fde047;
  --tk-yellow-500: #eab308;
  --tk-yellow-700: #a16207;

  --tk-green-50: #f0fdf4;
  --tk-green-100: #dcfce7;
  --tk-green-300: #86efac;
  --tk-green-400: #4ade80;
  --tk-green-500: #22c55e;
  --tk-green-700: #15803d;
  --tk-green-800: #166534;
  --tk-emerald-500: #10b981;

  --tk-blue-50: #eff6ff;
  --tk-blue-100: #dbeafe;
  --tk-blue-300: #93c5fd;
  --tk-blue-400: #60a5fa;
  --tk-blue-500: #3b82f6;
  --tk-blue-600: #2563eb;
  --tk-blue-700: #1d4ed8;
  --tk-blue-800: #1e40af;
  --tk-blue-900: #1e3a8a;

  /* Neutral Colors */
  --tk-white: #ffffff;
  --tk-black: #000000;
  /* RGB versions for opacity */
  --tk-white-rgb: 255, 255, 255;
  --tk-black-rgb: 0, 0, 0;

  --tk-gray-50: #fafafa;
  --tk-gray-100: #f3f4f6;
  --tk-gray-200: #e5e7eb;
  --tk-gray-300: #d1d5db;
  --tk-gray-400: #9ca3af;
  --tk-gray-500: #6b7280;
  --tk-gray-600: #4b5563;
  --tk-gray-700: #374151;
  --tk-gray-800: #1f2937;
  --tk-gray-900: #111827;

  /* Semantic Color Palette Tokens (used by role tokens below) */
  --tk-red-100: #fee2e2;
  --tk-red-300: #fca5a5;
  --tk-red-600: #dc2626;
  --tk-red-700: #b91c1c;
  /* RGB versions for opacity on semantic BGs in dark mode */
  --tk-green-300-rgb: 134, 239, 172;
  --tk-red-300-rgb: 252, 165, 165;
  --tk-yellow-300-rgb: 253, 224, 71;
  --tk-blue-300-rgb: 147, 197, 253;
  /* Darker versions for translucent dark mode BGs */
  --tk-green-800-rgb: 22, 101, 52;
  --tk-red-700-rgb: 185, 28, 28;
  --tk-yellow-700-rgb: 161, 98, 7;
  --tk-blue-800-rgb: 30, 64, 175;

  /* === GRADIENT DEFINITIONS (Role Tokens) === */
  --gradient-primary-brand: linear-gradient(to right, var(--tk-purple-500), var(--tk-indigo-500));
  --gradient-secondary-brand: linear-gradient(to right, var(--tk-blue-500), var(--tk-cyan-500));
  --gradient-accent-brand: linear-gradient(to right, var(--tk-pink-500), var(--tk-rose-500));
  --gradient-violet-accent: linear-gradient(to right, var(--tk-purple-600), var(--tk-violet-400));
  --gradient-violet-pink-accent: linear-gradient(to right, var(--tk-violet-500), var(--tk-pink-400));
  --gradient-dark-purple-hero: linear-gradient(
    135deg,
    var(--tk-purple-950),
    var(--tk-indigo-800),
    var(--tk-purple-700)
  );
  --gradient-session-1-num-bg: linear-gradient(to right, var(--tk-purple-500), var(--tk-indigo-600));
  --gradient-session-2-num-bg: linear-gradient(to right, var(--tk-blue-400), var(--tk-cyan-500));
  --gradient-session-3-num-bg: linear-gradient(to right, var(--tk-pink-400), var(--tk-rose-500));
  --gradient-assignment-card-light: linear-gradient(to bottom right, var(--tk-purple-50), var(--tk-indigo-50));
  --gradient-extension-card-light: linear-gradient(to bottom right, var(--tk-blue-50), var(--tk-cyan-50));
  --gradient-section-bg-dark: linear-gradient(
    135deg,
    var(--tk-purple-800),
    var(--tk-indigo-900)
  ); /* Base dark gradient for sections */
  --gradient-decorative-blob: radial-gradient(circle, var(--decorative-blob-color-1), var(--decorative-blob-color-2));
  --overlay-depth-gradient-top: rgba(255, 255, 255, 0.1); /* Renamed from --depth-gradient-top for clarity */
  --overlay-depth-gradient-bottom: rgba(0, 0, 0, 0.05); /* Renamed for clarity */
  --overlay-depth-gradient: linear-gradient(
    to bottom,
    var(--overlay-depth-gradient-top),
    transparent 50%,
    var(--overlay-depth-gradient-bottom)
  );

  /* === GENERAL UI COMPONENT & ROLE-BASED TOKENS (Light Theme Defaults) === */
  /* Backgrounds */
  --bg-page: var(--tk-gray-50);
  --bg-content: var(--tk-white);
  --bg-card: var(--tk-white);
  --bg-sidebar: var(--tk-white);
  --bg-input: var(--tk-white);
  --bg-subtle: var(--tk-gray-100);
  --bg-hover-active: var(--tk-purple-100); /* General hover/active for list items etc. */
  --bg-overlay: rgba(var(--tk-black-rgb), 0.4); /* Modal overlay */
  --bg-hero-dark-purple: var(--gradient-dark-purple-hero); /* Specific for dark purple hero sections */

  /* Text */
  --text-primary: var(--tk-gray-900);
  --text-secondary: var(--tk-gray-700);
  --text-subtle: var(--tk-gray-500);
  --text-on-dark-bg: var(--tk-white); /* For text on brand gradients/dark solids */
  --text-on-color: var(--tk-white); /* General purpose text on colored backgrounds */
  --text-link: var(--tk-purple-600);
  --text-link-hover: var(--tk-purple-700);
  --text-heading-brand: var(--tk-purple-700);
  --text-placeholder: var(--tk-gray-500);
  --text-disabled: var(--tk-gray-600);

  /* Borders */
  --border-standard: var(--tk-gray-200);
  --border-input: var(--tk-gray-400); /* Default input border, meets 3:1 on white */
  --border-divider: var(--tk-gray-200);
  --border-focus-ring-color: var(--tk-purple-500); /* Color for the focus outline/ring */
  --border-sidebar: var(--tk-purple-200);
  --border-interactive-strong: var(--tk-purple-500); /* For elements like active tab underline */

  /* Semantic States (Role Tokens) */
  --semantic-success-color-text: var(--tk-green-700);
  --semantic-success-color-bg: var(--tk-green-100);
  --semantic-success-color-border: var(--tk-green-300);
  --semantic-success-color-icon: var(--tk-green-500);

  --semantic-error-color-text: var(--tk-red-700);
  --semantic-error-color-bg: var(--tk-red-100);
  --semantic-error-color-border: var(--tk-red-300);
  --semantic-error-color-icon: var(--tk-red-600);

  --semantic-warning-color-text: var(--tk-yellow-700);
  --semantic-warning-color-bg: var(--tk-yellow-100);
  --semantic-warning-color-border: var(--tk-yellow-300);
  --semantic-warning-color-icon: var(--tk-yellow-500);

  --semantic-info-color-text: var(--tk-blue-700);
  --semantic-info-color-bg: var(--tk-blue-100);
  --semantic-info-color-border: var(--tk-blue-300);
  --semantic-info-color-icon: var(--tk-blue-500);

  /* Buttons */
  --button-primary-bg-image: var(--gradient-primary-brand);
  --button-primary-color-text: var(--text-on-dark-bg);
  --button-primary-hover-bg-image: linear-gradient(to right, var(--tk-purple-600), var(--tk-indigo-600));

  --button-secondary-bg-image: var(--gradient-secondary-brand);
  --button-secondary-color-text: var(--text-on-dark-bg);
  --button-secondary-hover-bg-image: linear-gradient(to right, var(--tk-blue-600), var(--tk-cyan-600));

  --button-accent-bg-image: var(--gradient-accent-brand);
  --button-accent-color-text: var(--tk-black); /* For contrast on pink/rose gradient */
  --button-accent-hover-bg-image: linear-gradient(to right, var(--tk-pink-600), var(--tk-rose-600));

  --button-outline-color-border: var(--tk-purple-500);
  --button-outline-color-text: var(--tk-purple-600);
  --button-outline-hover-bg-color: var(--tk-purple-50);
  --button-outline-hover-color-text: var(--tk-purple-700);

  --button-disabled-bg-color: var(--tk-gray-100);
  --button-disabled-color-text: var(--text-disabled);
  --button-disabled-color-border: var(--tk-gray-200);

  /* Forms */
  --input-border-radius: 8px;
  --input-border-color-focus: var(--tk-purple-500);
  --input-focus-shadow-ring: 0 0 0 3px var(--border-focus-ring-color); /* Uses the general focus ring color */
  --input-bg-disabled: var(--tk-gray-100);
  --input-border-color-disabled: var(--tk-gray-200);
  --input-color-text-disabled: var(--text-disabled);

  /* Cards & Modals */
  --card-padding: var(--space-lg); /* Using spacing token */
  --card-border-radius: 8px;
  --modal-bg-color: var(--bg-card);

  /* Decorative Elements */
  --decorative-blob-color-start: rgba(var(--tk-purple-500-rgb), 0.15); /* Requires --tk-purple-500-rgb */
  --decorative-blob-color-end: rgba(var(--tk-cyan-500-rgb), 0.15); /* Requires --tk-cyan-500-rgb */

  /* === 3D DEPTH & SHADOW TOKENS === */
  --depth-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05), 0 1px 1px rgba(0, 0, 0, 0.1);
  --depth-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --depth-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --depth-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --depth-shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --depth-shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

  --depth-transform-hover: translateY(-2px);
  --depth-transform-active: translateY(1px);
  --depth-border-width: 1px; /* For 3D button borders, not general inputs */
  --depth-border-color-highlight: rgba(var(--tk-white-rgb), 0.1); /* Lighter top edge */
  --depth-border-color-shadow: rgba(var(--tk-black-rgb), 0.1); /* Darker bottom edge */

  /* === TYPOGRAPHY SCALE TOKENS === */
  --font-family-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif,
    'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; /* Placeholder */
  --font-family-body: var(--font-family-sans);
  --font-family-headings: var(--font-family-sans);

  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-black: 900;

  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --line-height-loose: 1.8;

  --font-size-base: 16px; /* Root font size */
  --font-size-xs: 0.75rem; /* 12px */
  --font-size-sm: 0.875rem; /* 14px */
  --font-size-md: 1rem; /* 16px */
  --font-size-lg: 1.125rem; /* 18px */
  --font-size-xl: 1.25rem; /* 20px */
  --font-size-h6: 1rem; /* 16px */
  --font-size-h5: 1.25rem; /* 20px */
  --font-size-h4: 1.5rem; /* 24px */
  --font-size-h3: clamp(1.5rem, 3vw + 1rem, 2.25rem); /* Approx 24px to 36px */
  --font-size-h2: clamp(1.75rem, 4vw + 1rem, 3rem); /* Approx 28px to 48px */
  --font-size-h1: clamp(2.25rem, 5vw + 1rem, 3.75rem); /* Approx 36px to 60px */

  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0em;

  /* === SPACING SCALE TOKENS === */
  --space-unit: 8px;
  --space-xs: calc(0.5 * var(--space-unit)); /* 4px */
  --space-sm: var(--space-unit); /* 8px */
  --space-md: calc(2 * var(--space-unit)); /* 16px */
  --space-lg: calc(3 * var(--space-unit)); /* 24px */
  --space-xl: calc(4 * var(--space-unit)); /* 32px */
  --space-2xl: calc(6 * var(--space-unit)); /* 48px */
  --space-3xl: calc(8 * var(--space-unit)); /* 64px */
  --space-section-padding-y: var(--space-3xl); /* Default vertical padding for sections */

  /* === TRANSITION TOKENS === */
  --transition-speed-fast: 0.2s;
  --transition-speed-normal: 0.3s;
  --transition-timing-function: ease-in-out;
  --transition-default: all var(--transition-speed-normal) var(--transition-timing-function);
  --transition-fast: all var(--transition-speed-fast) var(--transition-timing-function);
}
```

#### 4.4. Theme-Specific Variables (Light, Dark, Night - with "Dark Purple Driven" options)
This section defines the overrides for semantic role tokens for each theme. It includes notes on how to achieve a "Dark Purple Driven" aesthetic within the Dark Theme structure.

```css
/* === DARK MODE OVERRIDES === */
[data-theme='dark'] {
  /* Default Dark Theme uses grays for base backgrounds for general readability */
  --bg-page: var(--tk-gray-900);
  --bg-content: var(--tk-gray-800);
  --bg-card: var(--tk-gray-800);
  --bg-sidebar: var(--tk-gray-800);
  --bg-input: var(--tk-gray-700);
  --bg-subtle: var(--tk-gray-800);
  /*
     GUIDANCE FOR A "DARK PURPLE DRIVEN" THEME:
     To achieve a stronger purple feel, override these core background tokens
     in a more specific selector (e.g., a class on the body/main container)
     or by creating a distinct '[data-theme="dark-purple"]' variant:

     .dark-purple-theme-active {
       --bg-page: var(--tk-purple-950);
       --bg-content: var(--tk-purple-900);
       --bg-card: var(--tk-indigo-900); // Or var(--tk-purple-800)
       --bg-input: var(--tk-indigo-950); // Ensure input text contrasts
       --bg-sidebar: var(--tk-purple-900);
       --bg-subtle: var(--tk-purple-800); // Or a darker neutral
     }
     Always re-verify all text and non-text contrast ratios when making such overrides.
  */

  --bg-hover-active: rgba(var(--tk-purple-700-rgb), 0.15); /* Assuming --tk-purple-700-rgb: 109,40,217 */
  --bg-overlay: rgba(var(--tk-black-rgb), 0.6); /* Darker overlay */

  --text-primary: var(--tk-white);
  --text-secondary: var(--tk-gray-300);
  --text-subtle: var(--tk-gray-400);
  --text-on-dark-bg: var(--tk-white);
  --text-on-color: var(--tk-white);
  --text-link: var(--tk-purple-400);
  --text-link-hover: var(--tk-purple-300);
  --text-heading-brand: var(--tk-purple-300);
  --text-placeholder: var(--tk-gray-400); /* Contrast on --bg-input (tk-gray-700): 3.0:1 (AA Large) */
  --text-disabled: var(--tk-gray-500);

  --border-standard: var(--tk-gray-700);
  --border-input: var(
    --tk-gray-600
  ); /* Default Dark Input Border. Contrast on --bg-input (tk-gray-700): 1.26:1 (Fail). Relies on BG diff + focus. */
  --border-divider: var(--tk-gray-700);
  --border-focus-ring-color: var(--tk-purple-400); /* Brighter focus ring for dark mode */
  --border-sidebar: var(--tk-gray-700);
  --border-interactive-strong: var(--tk-purple-400);

  /* Semantic States (Dark Theme) */
  --semantic-success-color-text: var(--tk-green-300);
  --semantic-success-color-bg: rgba(var(--tk-green-800-rgb), 0.3); /* e.g., 22,101,52 */
  --semantic-success-color-border: var(--tk-green-700);
  --semantic-success-color-icon: var(--tk-green-300);

  --semantic-error-color-text: var(--tk-red-300);
  --semantic-error-color-bg: rgba(var(--tk-red-700-rgb), 0.25); /* e.g., 185,28,28 */
  --semantic-error-color-border: var(--tk-red-600);
  --semantic-error-color-icon: var(--tk-red-300);

  --semantic-warning-color-text: var(--tk-yellow-300);
  --semantic-warning-color-bg: rgba(var(--tk-yellow-700-rgb), 0.25); /* e.g., 161,98,7 */
  --semantic-warning-color-border: var(--tk-yellow-500);
  --semantic-warning-color-icon: var(--tk-yellow-300);

  --semantic-info-color-text: var(--tk-blue-300);
  --semantic-info-color-bg: rgba(var(--tk-blue-800-rgb), 0.25); /* e.g., 30,64,175 */
  --semantic-info-color-border: var(--tk-blue-600);
  --semantic-info-color-icon: var(--tk-blue-300);

  /* Buttons (Dark Theme) */
  /* Primary/Secondary/Accent buttons use brand gradients which are inherently dark mode friendly with light text. */
  /* Outline buttons need adjustment */
  --button-outline-color-border: var(--tk-purple-400);
  --button-outline-color-text: var(--tk-purple-400);
  --button-outline-hover-bg-color: rgba(var(--tk-purple-400-rgb), 0.15); /* Assuming --tk-purple-400-rgb: 167,139,250 */
  --button-outline-hover-color-text: var(--tk-purple-300);

  --button-disabled-bg-color: var(--tk-gray-700);
  --button-disabled-color-text: var(--tk-gray-400); /* Contrast: 3.0:1 (AA Large) */
  --button-disabled-color-border: var(--tk-gray-600);

  /* Forms (Dark Theme) */
  --input-border-color-focus: var(--tk-purple-400);
  --input-focus-shadow-ring: 0 0 0 3px var(--border-focus-ring-color);
  --input-bg-disabled: var(--tk-gray-800); /* Slightly different from button disabled bg */
  --input-border-color-disabled: var(--tk-gray-700);
  --input-color-text-disabled: var(--tk-gray-500);

  /* 3D Depth & Shadow Overrides for Dark Theme */
  --depth-shadow-sm: 0 1px 2px rgba(var(--tk-black-rgb), 0.2), 0 1px 1px rgba(var(--tk-black-rgb), 0.3);
  --depth-shadow-md: 0 4px 6px -1px rgba(var(--tk-black-rgb), 0.3), 0 2px 4px -1px rgba(var(--tk-black-rgb), 0.2);
  --depth-shadow-lg: 0 10px 15px -3px rgba(var(--tk-black-rgb), 0.3), 0 4px 6px -2px rgba(var(--tk-black-rgb), 0.2);
  --depth-shadow-xl: 0 20px 25px -5px rgba(var(--tk-black-rgb), 0.3), 0 10px 10px -5px rgba(var(--tk-black-rgb), 0.2);
  --depth-shadow-2xl: 0 25px 50px -12px rgba(var(--tk-black-rgb), 0.4);
  --depth-shadow-inner: inset 0 2px 4px 0 rgba(var(--tk-black-rgb), 0.2);

  --depth-border-color-highlight: rgba(var(--tk-white-rgb), 0.05);
  --depth-border-color-shadow: rgba(var(--tk-black-rgb), 0.2);
  --overlay-depth-gradient-top: rgba(var(--tk-white-rgb), 0.05);
  --overlay-depth-gradient-bottom: rgba(var(--tk-black-rgb), 0.1);

  /* Section & Hero Tokens */
  --gradient-section-bg-dark: linear-gradient(135deg, var(--tk-indigo-900), var(--tk-purple-950));
  --bg-hero-dark-purple: var(--gradient-dark-purple-hero); /* This can be directly used now */

  /* Session & Thematic Adjustments for standard Dark Theme */
  --gradient-assignment-card-dark: linear-gradient(
    to bottom right,
    rgba(var(--tk-purple-900-rgb), 0.3),
    rgba(var(--tk-indigo-900-rgb), 0.3)
  );
  --gradient-extension-card-dark: linear-gradient(
    to bottom right,
    rgba(var(--tk-blue-900-rgb), 0.2),
    rgba(var(--tk-cyan-800-rgb), 0.2)
  ); /* Assuming --tk-cyan-800-rgb */
}

/* === NIGHT MODE OVERRIDES (Warmer, Desaturated) === */
[data-theme='night'] {
  /* Backgrounds */
  --bg-page: #2e2925;
  --bg-content: #3a342f;
  --bg-card: #3a342f;
  --bg-sidebar: #3a342f;
  --bg-input: #4a4540;
  --bg-subtle: #3a342f;
  --bg-hover-active: #4a4540; /* Slightly lighter than content for hover */
  --bg-overlay: rgba(var(--tk-black-rgb), 0.7); /* Darker overlay for night */

  /* Text */
  --text-primary: #d8cec4; /* Contrast on #3a342f: 10.77:1 (AAA) */
  --text-secondary: #a89f95; /* Contrast on #3a342f: 5.65:1 (AA) */
  --text-subtle: #7a736a; /* Contrast on #3a342f: 3.06:1 (AA Large) */
  --text-on-dark-bg: var(--text-primary);
  --text-on-color: var(--text-primary);
  --text-link: #9c80b1; /* Adjusted for contrast: 4.55:1 (AA) on #3a342f */
  --text-link-hover: #b298c1; /* Lighter hover for night link */
  --text-heading-brand: #9c80b1;
  --text-placeholder: #a89f95; /* Contrast on #4a4540 (input BG): 4.55:1 (AA) */
  --text-disabled: #7a736a;

  /* Borders */
  --border-standard: #5a4f45; /* Non-text on #3a342f: 1.76:1 (Fail) */
  --border-input: #6a635a; /* Non-text on #4a4540 (input BG): 1.32:1 (Fail) */
  --border-divider: #5a4f45;
  --border-focus-ring-color: #9c80b1; /* Warmer focus ring */
  --border-sidebar: #5a4f45;
  --border-interactive-strong: #9c80b1;

  /* Semantic States (Night Theme) */
  --semantic-success-color-text: #90a991;
  --semantic-success-color-bg: rgba(122, 153, 123, 0.15);
  --semantic-success-color-border: #7a997b;
  --semantic-success-color-icon: #90a991;

  --semantic-error-color-text: #c88b8b;
  --semantic-error-color-bg: rgba(179, 107, 107, 0.15);
  --semantic-error-color-border: #b36b6b;
  --semantic-error-color-icon: #c88b8b;

  --semantic-warning-color-text: #c8b39b;
  --semantic-warning-color-bg: rgba(179, 153, 123, 0.15);
  --semantic-warning-color-border: #b3997b;
  --semantic-warning-color-icon: #c8b39b;

  --semantic-info-color-text: #90a4af;
  --semantic-info-color-bg: rgba(122, 142, 153, 0.15);
  --semantic-info-color-border: #7a8e99;
  --semantic-info-color-icon: #90a4af;

  /* Buttons (Night Theme) */
  --button-primary-bg-image: linear-gradient(to right, #8a6f9e, #6e5e8e);
  --button-primary-color-text: var(--text-on-dark-bg);
  --button-primary-hover-bg-image: linear-gradient(to right, #7a5f8e, #5e4e7e);

  --button-secondary-bg-image: linear-gradient(to right, #5a8e8a, #4a7e7a);
  --button-secondary-color-text: var(--text-on-dark-bg);
  --button-secondary-hover-bg-image: linear-gradient(to right, #4a7e7a, #3a6e6a);

  --button-accent-bg-image: linear-gradient(to right, #a96b8e, #995b7e);
  --button-accent-color-text: var(--text-on-dark-bg); /* White text on these night accents usually works */
  --button-accent-hover-bg-image: linear-gradient(to right, #995b7e, #894b6e);

  --button-outline-color-border: var(--text-link);
  --button-outline-color-text: var(--text-link);
  --button-outline-hover-bg-color: #4a4540; /* Input BG color */
  --button-outline-hover-color-text: var(--text-primary);

  --button-disabled-bg-color: #3a342f; /* Matches content BG */
  --button-disabled-color-text: var(--text-disabled); /* Contrast on #3a342f: 3.06:1 (AA Large) */
  --button-disabled-color-border: #4a4540; /* Input BG color */

  /* Forms (Night Theme) */
  --input-border-color-focus: #9c80b1;
  --input-focus-shadow-ring: 0 0 0 3px var(--border-focus-ring-color);
  --input-bg-disabled: #2e2925; /* Page BG */
  --input-border-color-disabled: #3a342f;
  --input-color-text-disabled: var(--text-subtle);

  /* 3D Depth & Shadow Overrides for Night Theme */
  --depth-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.1), 0 1px 1px rgba(0, 0, 0, 0.15);
  --depth-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.15), 0 2px 4px -1px rgba(0, 0, 0, 0.08);
  /* ... (other shadows similar to dark mode, but potentially slightly softer if needed) ... */
  --depth-border-color-highlight: rgba(var(--text-primary-rgb), 0.03); /* Assuming --text-primary-rgb: 216,206,196 */
  --depth-border-color-shadow: rgba(var(--tk-black-rgb), 0.15);
  --overlay-depth-gradient-top: rgba(var(--text-primary-rgb), 0.03);
  --overlay-depth-gradient-bottom: rgba(var(--tk-black-rgb), 0.1);

  /* Section & Hero Tokens */
  --gradient-section-bg-dark: linear-gradient(135deg, #3a342f, #2e2925); /* Default dark section for night */
  --bg-hero-dark-purple: linear-gradient(135deg, #6e5e8e, #4a3e5e); /* Example night mode purple hero */

  /* Decorative Blob Tokens */
  --decorative-blob-color-start: rgba(138, 111, 158, 0.1); /* Night mode desaturated purple */
  --decorative-blob-color-end: rgba(90, 142, 138, 0.1); /* Night mode desaturated teal */
}
```

#### 4.5. 3D Depth & Shadow Variables
The global `--depth-*` tokens defined in the main `:root` (Section 4.2) provide the base for light theme. These are then overridden in `[data-theme='dark']` and `[data-theme='night']` blocks (as shown in Section 4.4) to adjust intensity and color suitable for darker backgrounds. Shadows on dark themes are typically softer or use lighter tones if they are meant to simulate light casting onto a dark surface, or darker tones if they are meant to be recesses. Our dark theme shadows are generally darker (more opaque black) to give subtle depth against dark grays or purples.

#### 4.6. Typography & Spacing Scale Variables
These functional tokens (e.g., `--font-size-md`, `--line-height-normal`, `--space-md`) are generally consistent across themes and are defined in the main `:root` block (Section 4.2). They provide the foundational rhythm and scale for the UI. Thematic changes primarily affect color tokens, not the core structure provided by spacing and typography scales, ensuring a consistent user experience framework regardless of visual theme.

---
This concludes Phase 1 of the complete, self-contained "TechnoKids Color Palette Guide v16." It has established the foundational philosophy, the expanded and detailed color palettes with their rationale, dynamic gradient strategies, and the comprehensive CSS Custom Property (Design Token) setup for all three themes, including specific guidance for achieving a "dark purple driven" look.

**Phase 2 will cover Section 5 (Layout), Section 6 (Typography), and Section 7 (Application to UI Components - Buttons and Forms & Input Fields).**

**Phase 3 will complete Section 7 (remaining UI Components), and then Sections 8 (Depth/Shadows), 9 (Animations), 10 (Theming Details recap), 11 (Accessibility Best Practices Summary), and 12 (Conclusion/Governance).**

Let me know when you are ready for the next phase.

---

### 5. Layout, Spacing, & Composition

Effective visual organization is paramount for creating interfaces that are intuitive, easy to scan, and aesthetically pleasing across all themes, including light, dark, and "dark purple driven" aesthetics. This section outlines the principles and tokens governing layout, spacing, and overall composition within the TechnoKids design system. The defined spacing structure and layout principles are designed to be versatile and support various thematic expressions.

#### 5.1. Principles of Visual Organization
Our layout and spacing strategy is guided by the following core principles, which apply universally across all themes:
*   **Hierarchy:** Clear visual hierarchy is established through the strategic use of space, size, and positioning. This guides the user's attention to the most important elements first, regardless of whether the background is light or a deep brand color.
*   **Alignment:** Consistent alignment of text and UI elements (e.g., using a common baseline grid or column structure) creates a sense of order, professionalism, and reduces cognitive load for the user.
*   **Proximity (Grouping):** Elements that are related in meaning or function are grouped visually closer together. Conversely, unrelated items are separated by more significant spacing. This Gestalt principle helps users understand the structure of information intuitively.
*   **Repetition & Consistency:** The reuse of design elements, spacing patterns (defined by our spacing tokens), and layout structures across the application ensures predictability. This makes the interface easier to learn and navigate.
*   **Balance:** Achieving a visual equilibrium between elements on the page is key to creating a harmonious and stable composition, preventing visual fatigue.
*   **White Space (or "Dark Space" in Dark Themes):** Strategic and generous use of negative space is critical. It prevents interfaces from feeling cluttered, improves focus on content, enhances readability (especially of light text on dark backgrounds), and guides the user's eye. In a "dark purple driven" theme, this "empty" space will be these rich dark colors, making the intentional use of space around foreground elements even more vital for clarity.

#### 5.2. Grid System (Conceptual)
While specific grid implementations (e.g., a 12-column responsive grid, or specific CSS Grid layouts for components) will be detailed in platform-specific or component-level documentation, this guide strongly advocates for the consistent use of an underlying grid structure.
*   **Purpose:** To ensure consistent alignment and proportional placement of elements across different pages and screen sizes, contributing to a cohesive and professional appearance.
*   **Gutters & Margins:** Spacing tokens (defined in Section 5.3) must be used to define consistent gutters between grid columns and margins around grid containers.
*   **Responsiveness:** The chosen grid system must be inherently responsive, adapting gracefully to various viewport sizes from mobile to desktop, ensuring content remains accessible and well-organized.

#### 5.3. Spacing Scale & Vertical Rhythm
A consistent spacing scale, based on `var(--space-unit: 8px;)`, is fundamental for achieving visual harmony and rhythm. All padding, margins, and gaps between elements should use multiples or fractions of this unit. This creates a predictable and aesthetically pleasing vertical and horizontal rhythm throughout the interface, irrespective of the active color theme.

*   **Base Unit:** `--space-unit: 8px;` (This is tokenized in Section 4)
*   **Scale (Tokens defined in Section 4):**
    *   `--space-xs: calc(0.5 * var(--space-unit));`  (4px) - For very fine-tuned spacing, like between an icon and its adjacent text, or within compact UI controls.
    *   `--space-sm: var(--space-unit);`             (8px) - Small gaps, padding within compact elements (e.g., small buttons, tags), inter-item spacing in tight lists.
    *   `--space-md: calc(2 * var(--space-unit));`  (16px) - Standard padding/margin for many UI elements, default paragraph spacing, spacing between form fields and their labels.
    *   `--space-lg: calc(3 * var(--space-unit));`  (24px) - Larger separation between distinct UI groups, standard card padding, spacing between form groups.
    *   `--space-xl: calc(4 * var(--space-unit));`  (32px) - Spacing around larger components, internal padding for larger sections or modals.
    *   `--space-2xl: calc(6 * var(--space-unit));` (48px) - Major separation between large page sections or distinct content blocks.
    *   `--space-3xl: calc(8 * var(--space-unit));` (64px) - Standard vertical padding for main page sections (defined as `--space-section-padding-y`).

**Application Example:**
```css
.example-card {
  padding: var(--space-lg); /* 24px padding inside the card */
  margin-bottom: var(--space-xl); /* 32px margin below the card */
}
.example-card-header h3 {
  margin-bottom: var(--space-md); /* 16px margin below card heading */
}
.example-card-content p + p {
  margin-top: var(--space-sm); /* 8px margin between paragraphs within the card */
}
.form-group + .form-group {
  /* Spacing between groups of form fields */
  margin-top: var(--space-lg);
}
```

#### 5.4. White Space (Negative Space) Strategy
White space (or its equivalent "dark space" in dark themes) is not merely empty; it is an active design element that shapes perception and usability.
*   **Improves Readability & Legibility:** Generous margins around text blocks and appropriate line height (see Section 6) make content easier to process and read, especially long-form educational content.
*   **Reduces Cognitive Load & Clutter:** Sufficient spacing prevents interfaces from appearing dense, overwhelming, or chaotic, allowing users to process information more easily.
*   **Enhances Focus & Attention:** Helps users concentrate on important content and interactive elements by visually separating them from surrounding distractions.
*   **Defines Relationships & Hierarchy:** The amount of space between elements visually signals their relationship (or lack thereof), reinforcing the intended structure and hierarchy of information.
*   **Application Guidance:**
    *   Use larger spacing tokens (`--space-xl`, `--space-2xl`, `--space-3xl`) to clearly delineate distinct sections or modules on a page.
    *   Ensure adequate padding (`--space-md` or `--space-lg`) within container components like cards, modals, and input fields to prevent content from feeling cramped.
    *   Maintain consistent spacing between similar elements (e.g., list items, navigation links) to create a predictable rhythm.

#### 5.5. Container & Section Defaults
These provide baseline structural components for page layouts, ensuring consistency in how primary content areas are presented.

```css
.container {
  width: 100%; /* Ensures the container can be centered */
  max-width: 1280px; /* Standard maximum width for the main content area to maintain readability */
  margin-left: auto; /* Centers the container on the page */
  margin-right: auto;
  padding-left: var(--space-lg); /* Consistent horizontal padding (24px) */
  padding-right: var(--space-lg);
  box-sizing: border-box; /* Includes padding and border in the element's total width and height */
}

.section {
  width: 100%;
  padding-top: var(--space-section-padding-y); /* Consistent vertical padding for sections (64px) */
  padding-bottom: var(--space-section-padding-y);
  background-color: var(--bg-content); /* Default section background, theme-dependent */
  color: var(--text-secondary); /* Default text color for section content, theme-dependent */
  transition:
    background-color var(--transition-normal),
    color var(--transition-normal); /* For smooth theme changes */
  box-sizing: border-box;
}

/* Example of a section variant using a brand color or gradient */
.section--brand-feature {
  /* For Light theme, could be a lighter brand color or subtle gradient */
  /* background-color: var(--tk-purple-50); */
  /* color: var(--tk-purple-700); */

  /* For a "Dark Purple Driven" context or Dark Theme feature section */
  background-image: var(--gradient-dark-purple-hero); /* Uses the rich dark purple gradient */
  /* Or: background-color: var(--tk-purple-900); */
  color: var(--text-on-dark-bg); /* Ensures all text within this section is light and readable */
}
/* Ensure text elements within themed sections inherit or are explicitly set to contrasting colors */
.section--brand-feature h1,
.section--brand-feature h2,
.section--brand-feature h3,
.section--brand-feature p,
.section--brand-feature li,
.section--brand-feature a {
  /* Ensure links also use light color */
  color: var(--text-on-dark-bg); /* Or a specific light accent like --tk-purple-300 */
}
.section--brand-feature a:hover,
.section--brand-feature a:focus-visible {
  color: var(--tk-violet-300); /* Example of a lighter hover for links on dark bg */
  text-decoration-color: var(--tk-violet-300);
}
```

---

### 6. Typography System: Clarity, Hierarchy, and Readability

Typography is a cornerstone of the TechnoKids user experience, profoundly impacting communication, brand expression, and accessibility across all visual themes, including light, dark, and "dark purple driven" interfaces.

#### 6.1. Typographic Philosophy
*   **Readability First:** Text must be exceptionally easy to read for extended periods, especially within educational content. Font choice, size, weight, line height, and crucially, color contrast against all possible theme backgrounds are optimized for this.
*   **Legibility:** Individual characters must be distinct and unambiguous to prevent misinterpretation, regardless of the color scheme.
*   **Clear Hierarchy:** A well-defined typographic scale, consistently applied, creates a clear visual hierarchy. This guides users through content and differentiates levels of importance effectively, even when text colors change dramatically between themes (e.g., dark text on light BG vs. light text on dark purple BG).
*   **Accessibility as Standard:** All typographic choices (font, size, weight, color) must inherently meet WCAG 2.1 AA contrast requirements against their intended backgrounds across all themes. Text must support user-agent resizing and customization without loss of content or functionality.
*   **Brand Expression:** While primarily functional, typography also subtly contributes to the TechnoKids brand persona—aiming for a feel that is engaging, clear, modern, and trustworthy, adaptable to various thematic moods.

#### 6.2. Font Families, Weights, Line Heights
*   **Font Family (Critical Placeholder - Must Be Defined):**
    *   `--font-family-sans`: `-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'`
        *   **Action Required:** This system font stack is a **placeholder**. It is **imperative** to replace this with the specific, licensed web font(s) chosen for the TechnoKids brand. This choice will significantly impact the overall look and feel. The chosen font(s) must offer a good range of weights and be highly legible on screens.
    *   `--font-family-body`: `var(--font-family-sans)`
    *   `--font-family-headings`: `var(--font-family-sans)`
        *   *Rationale for current single family:* Using a single, versatile sans-serif font family promotes consistency and often yields a modern, clean aesthetic. If a distinct display font is later chosen for headings, it must be highly legible, pair harmoniously with the body font, and its impact on thematic expression (especially in "dark purple driven" designs) must be considered.
*   **Font Weights (Ensure chosen font supports these weights robustly):**
    *   `--font-weight-normal: 400;` (Standard for body text, labels, general content)
    *   `--font-weight-semibold: 600;` (For sub-headings, emphasized text, button labels, links needing prominence)
    *   `--font-weight-bold: 700;` (For primary headings, strong calls to action, key information requiring high emphasis)
    *   *Available if chosen font supports and design requires:*
        *   `--font-weight-light: 300;` (Use with caution; ensure sufficient stroke width for legibility, especially for light text on dark backgrounds or small sizes)
        *   `--font-weight-black: 900;` (For very high impact, short display text; use sparingly)
*   **Line Heights (Leading):** Defined as unitless multipliers for optimal readability and vertical rhythm. These values are applied to the `line-height` CSS property.
    *   `--line-height-tight: 1.2;` (Primarily for larger headings where lines are typically shorter, preventing excessive vertical space)
    *   `--line-height-normal: 1.5;` (Standard for body text, paragraphs, lists; generally considered optimal for readability of continuous text)
    *   `--line-height-loose: 1.8;` (For specific contexts needing more open spacing, e.g., introductory paragraphs, blockquotes, or to improve readability of dense information on certain backgrounds)

    ```css
    /* Base typographic styles applied globally */
    body {
      font-family: var(--font-family-body);
      font-size: var(--font-size-md); /* Base font size (16px by default) */
      line-height: var(--line-height-normal); /* Default line height for body */
      color: var(--text-secondary); /* Theme-dependent default body text color */
      background-color: var(--bg-page); /* Theme-dependent page background */
      -webkit-font-smoothing: antialiased; /* Improve text rendering on WebKit/Blink */
      -moz-osx-font-smoothing: grayscale; /* Improve text rendering on Firefox/Gecko */
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      font-family: var(--font-family-headings);
      font-weight: var(--font-weight-bold); /* Default weight for headings */
      line-height: var(--line-height-tight); /* Tighter line height for headings */
      color: var(--text-primary); /* Theme-dependent default heading color */
      margin-top: var(--space-lg); /* Default top margin for headings */
      margin-bottom: var(--space-md); /* Default bottom margin for headings */
    }
    h1 {
      font-size: var(--font-size-h1);
    }
    h2 {
      font-size: var(--font-size-h2);
    }
    h3 {
      font-size: var(--font-size-h3);
    }
    h4 {
      font-size: var(--font-size-h4);
      font-weight: var(--font-weight-semibold);
    } /* Slightly less bold for H4-H6 */
    h5 {
      font-size: var(--font-size-h5);
      font-weight: var(--font-weight-semibold);
    }
    h6 {
      font-size: var(--font-size-h6);
      font-weight: var(--font-weight-semibold);
    }

    p {
      margin-bottom: var(--space-md); /* Standard paragraph bottom margin */
      max-width: 75ch; /* Optimal line length for readability */
    }
    p + p {
      margin-top: var(--space-md); /* Ensure consistent spacing between paragraphs */
    }

    a {
      color: var(--text-link);
      text-decoration: none; /* Underlines typically added on hover/focus for clarity */
      font-weight: var(--font-weight-semibold); /* Links are often slightly bolder */
      transition: color var(--transition-fast);
    }
    a:hover,
    a:focus-visible {
      /* Using :focus-visible for keyboard-only focus indication */
      color: var(--text-link-hover);
      text-decoration: underline;
      text-decoration-thickness: max(1px, 0.0625em); /* Ensure underline is visible */
      text-underline-offset: 0.125em; /* Space between text and underline */
    }

    strong,
    b {
      font-weight: var(--font-weight-bold);
      color: inherit;
    } /* Inherit color to avoid contrast issues if parent changes color */
    em,
    i {
      font-style: italic;
      color: inherit;
    }

    ul,
    ol {
      padding-left: var(--space-lg); /* Indent lists */
      margin-bottom: var(--space-md);
    }
    li {
      margin-bottom: var(--space-sm); /* Spacing between list items */
    }
    ```

#### 6.3. Responsive Typographic Scale (with `rem` and `clamp()`)
Font sizes are defined in `rem` for accessibility (allowing users to scale text via browser settings) and scalability. `clamp()` is used for fluid responsiveness in headings, adapting smoothly to viewport width changes. The base document font size (`<html>`) is typically set to `100%` (defaulting to `16px` in most browsers), making `1rem = 16px`.

| Element | Variable Name       | Value (CSS `clamp` or `rem`)         | Approx. Pixel Range (at 16px base)      | Notes                                           |
| :------ | :------------------ | :----------------------------------- | :-------------------------------------- | :---------------------------------------------- |
| H1      | `--font-size-h1`    | `clamp(2.25rem, 5vw + 1rem, 3.75rem)`| 36px to 60px                            | Main page titles, max impact                    |
| H2      | `--font-size-h2`    | `clamp(1.75rem, 4vw + 1rem, 3rem)`   | 28px to 48px                            | Major section titles                            |
| H3      | `--font-size-h3`    | `clamp(1.5rem, 3vw + 1rem, 2.25rem)` | 24px to 36px                            | Sub-section titles                              |
| H4      | `--font-size-h4`    | `1.5rem`                             | 24px                                    | Minor headings                                  |
| H5      | `--font-size-h5`    | `1.25rem`                            | 20px                                    | Deeper minor headings                           |
| H6      | `--font-size-h6`    | `1rem`                               | 16px                                    | Lowest level headings, often similar to body text |
| Body (MD)| `--font-size-md`    | `1rem`                               | 16px                                    | **Default body text**, crucial for readability |
| Body (LG)| `--font-size-lg`    | `1.125rem`                           | 18px                                    | Larger body text for emphasis or specific UIs |
| Body (SM)| `--font-size-sm`    | `0.875rem`                           | 14px                                    | Captions, secondary info; use with care for contrast |
| Body (XS)| `--font-size-xs`    | `0.75rem`                            | 12px                                    | Fine print, tooltips; very limited use, high contrast needed |

#### 6.4. Text Color Application & WCAG Contrast
All text color choices must prioritize readability and meet WCAG 2.1 AA contrast minimums against their specific background in the current theme.

*   **6.4.1. Primary, Secondary, and Tertiary Text Colors:** These semantic roles (`--text-primary`, `--text-secondary`, `--text-subtle`) are redefined for each theme (`[data-theme='light']`, `[data-theme='dark']`, `[data-theme='night']`) in Section 4.4. Their contrast ratios are calculated against their intended default backgrounds (e.g., `--bg-content`, `--bg-card`).
    *   **Light Theme (on `--bg-content: var(--tk-white)`):**
        *   `--text-primary` (`--tk-gray-900`): **15.76:1 (AAA Pass)**.
        *   `--text-secondary` (`--tk-gray-700`): **7.55:1 (AAA Pass)**.
        *   `--text-subtle` (`--tk-gray-500`): **4.61:1 (AA Pass)**.
    *   **Dark Theme (on `--bg-content: var(--tk-gray-800)`):**
        *   `--text-primary` (`--tk-white`): **13.57:1 (AAA Pass)**.
        *   `--text-secondary` (`--tk-gray-300`): **7.05:1 (AAA Pass)**.
        *   `--text-subtle` (`--tk-gray-400`): **4.51:1 (AA Pass)**.
    *   **Night Theme (on `--bg-content: #3a342f`):**
        *   `--text-primary` (`#d8cec4`): **10.77:1 (AAA Pass)**.
        *   `--text-secondary` (`#a89f95`): **5.65:1 (AA Pass)**.
        *   `--text-subtle` (`#7a736a`): **3.06:1 (AA Pass for Large Text)**. *Note: For normal text, this is borderline; ensure font is large or bold if using this subtle text for important info in Night Mode.*
    *   **For "Dark Purple Driven" Contexts (e.g., text on `--bg-content: var(--tk-purple-900)`):**
        *   Use `--text-on-dark-bg` (`--tk-white`): Contrast: **11.0:1 (AAA Pass)**.
        *   Or lighter grays from Dark Theme: e.g., `--tk-gray-300` on `--tk-purple-900`: **7.76:1 (AAA Pass)**.
*   **6.4.2. Link Text Colors & States:** Link colors are also theme-dependent and must contrast adequately.
    *   **Light Theme (on `--bg-content: var(--tk-white)`):**
        *   Default (`--text-link: var(--tk-purple-600)`): **5.36:1 (AA Pass)**.
        *   Hover/Focus (`--text-link-hover: var(--tk-purple-700)`): **7.85:1 (AAA Pass)**.
    *   **Dark Theme (on `--bg-content: var(--tk-gray-800)`):**
        *   Default (`--text-link: var(--tk-purple-400)`): **5.05:1 (AA Pass)**.
        *   Hover/Focus (`--text-link-hover: var(--tk-purple-300)`): **7.14:1 (AAA Pass)**.
    *   **Night Theme (on `--bg-content: #3a342f`):**
        *   Default (`--text-link: #9c80b1`): **4.55:1 (AA Pass)**.
        *   Hover/Focus (`--text-link-hover: #b298c1`): **6.17:1 (AA Pass)**.
    *   **Accessibility Note:** Links must be distinguishable from surrounding text by more than color alone (e.g., `text-decoration: underline;` on hover/focus is standard). For inline links within body text, consider a persistent underline for better affordance, especially if the color contrast with surrounding text is not extremely high.
*   **6.4.3. Text on Colored/Gradient Backgrounds (`--text-on-dark-bg` or `--text-on-color`):**
    *   This is typically `var(--tk-white)` or `var(--tk-black)` depending on the lightness of the colored background.
    *   **Crucial:** Always verify contrast when placing text on any non-neutral background, especially gradients (see Section 3.3 for detailed gradient text accessibility). For example, text on primary buttons using `--primary-brand-gradient` uses `var(--text-on-dark-bg)`.
*   **6.4.4. Placeholder Text (`--text-placeholder`):**
    *   Light Theme (`--bg-input: var(--tk-white)`): `var(--tk-gray-500)`. Contrast: **4.61:1 (AA Pass)**.
    *   Dark Theme (`--bg-input: var(--tk-gray-700)`): `var(--tk-gray-400)`. Contrast: **3.0:1 (AA Pass for Large Text)**. Borderline for normal text; acceptable for non-essential placeholder cues.
    *   Night Theme (`--bg-input: #4a4540`): `var(--text-placeholder)` (`#a89f95`). Contrast: **4.55:1 (AA Pass)**.
    *   **Accessibility Reminder:** Placeholder text is **not a substitute** for a visible and programmatically associated `<label>` element for form inputs (WCAG SC 3.3.2 Labels or Instructions).

#### 6.5. Font Loading & Performance Considerations
Optimizing font loading is crucial for user experience, preventing layout shifts and ensuring text is readable as quickly as possible.
*   **`font-display` Property:** Use `font-display: swap;` within all `@font-face` declarations for custom web fonts. This instructs the browser to display fallback system text immediately while the custom web fonts are loading, preventing the Flash of Invisible Text (FOIT) and significantly improving perceived load time and content availability.
    ```css
    @font-face {
      font-family: 'YourTechnoKidsFont'; /* Replace with actual TechnoKids brand font name */
      src:
        url('/fonts/YourTechnoKidsFont-Regular.woff2') format('woff2'),
        url('/fonts/YourTechnoKidsFont-Regular.woff') format('woff');
      font-weight: 400; /* Corresponds to --font-weight-normal */
      font-style: normal;
      font-display: swap; /* Ensures text is visible during font load */
    }
    @font-face {
      font-family: 'YourTechnoKidsFont'; /* Replace */
      src:
        url('/fonts/YourTechnoKidsFont-Bold.woff2') format('woff2'),
        url('/fonts/YourTechnoKidsFont-Bold.woff') format('woff');
      font-weight: 700; /* Corresponds to --font-weight-bold */
      font-style: normal;
      font-display: swap;
    }
    /* Add other weights (e.g., 600 for semibold) as needed */
    ```
*   **Preloading Critical Fonts:** For web fonts that are essential for the initial rendering of the page (e.g., the primary body text font, main heading font used "above the fold"), preload them in the `<head>` of your HTML document. This tells the browser to prioritize downloading these font files.
    ```html
    <link rel="preload" href="/fonts/YourTechnoKidsFont-Regular.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="/fonts/YourTechnoKidsFont-Bold.woff2" as="font" type="font/woff2" crossorigin>
    <!-- Add more preloads for other critical weights/styles if necessary -->
    ```
*   **Font Subsetting:** If using large font files with extensive character sets (e.g., supporting many languages or numerous OpenType features), subset them to include only the characters, glyphs, and features necessary for the languages and design requirements of TechnoKids products. This can dramatically reduce font file sizes, leading to faster downloads and rendering. Various online and command-line tools are available for font subsetting.
*   **Font Formats:** Prioritize modern, efficient font formats like WOFF2 for the best compression and performance. WOFF should be provided as a widely supported fallback. Avoid older formats like TTF or OTF for web delivery if WOFF/WOFF2 versions are available or can be generated, as they are typically larger.
*   **Self-Hosting vs. Third-Party Services:**
    *   **Self-Hosting:** Storing font files on your own servers gives maximum control over caching, performance optimization (e.g., server compression), and avoids reliance on third-party services, which can have privacy implications (GDPR) or potential points of failure.
    *   **Third-Party Services (e.g., Google Fonts, Adobe Fonts):** Convenient and often provide optimized serving, but introduce an external dependency. If used, ensure compliance with privacy regulations and consider the performance impact of an additional DNS lookup and HTTP request.

---

### 7. Application to UI Components & Interactive States (WCAG & Usability Focused)

This section provides detailed specifications for applying the defined color palette and typographic standards to key User Interface (UI) components and their various interactive states. The primary objective is to ensure consistency, usability, and robust accessibility across all interactive elements of TechnoKids digital products, adaptable for Light, Dark, "Dark Purple Driven," and Night themes.

#### 7.1. General Principles for Component Color Application
*   **Affordance:** Components must visually communicate their interactivity. Color, shape, depth cues (shadows), and iconography are primary tools for this. Users should intuitively understand what elements are clickable, tappable, or otherwise interactive.
*   **Feedback:** Immediate and clear visual feedback must be provided in response to user interactions (e.g., hover, focus, click/tap, selection, validation). This confirms the system has received the input and is processing it.
*   **State Clarity:** Each interactive state of a component (default, hover, focus, active, disabled, error) must be visually distinct from other states and from any static (non-interactive) elements on the page. This clarity prevents user confusion and aids in understanding the component's current status and capabilities.
*   **Accessibility (Non-Negotiable):**
    *   All color combinations used for text within components (e.g., button labels, input text, alert messages) must meet WCAG 2.1 AA text contrast ratios (4.5:1 for normal text, 3:1 for large text) against their specific background in the current theme.
    *   All meaningful graphical elements of components (e.g., input field borders, checkbox boundaries, toggle switch tracks/knobs, informational icon details) must meet WCAG 2.1 AA non-text contrast ratios (3:1 against adjacent colors).
    *   Focus indicators must be highly visible and meet these non-text contrast requirements.

#### 7.2. Definition of Standard Component States & Visual Feedback
*   **7.2.1. Default State:**
    *   **Purpose:** The component's standard, resting appearance before any user interaction.
    *   **Visuals:** Clearly conveys its purpose and, if interactive, its potential for interaction. Uses base brand, neutral, or semantic role tokens appropriate for the component's function and the current theme.
*   **7.2.2. Hover State (`:hover`):**
    *   **Purpose:** Provides visual feedback when a pointer (typically a mouse cursor) hovers over an interactive element, confirming it is interactive and about to be activated or selected.
    *   **Visuals:** Subtle but noticeable changes are preferred to avoid jarring shifts. Examples include:
        *   Slight lightening or darkening of the background color (e.g., using a 50 or 100-shade lighter/darker variant from the palette, or an RGBA overlay).
        *   A subtle lift or change in shadow (e.g., transitioning from `var(--depth-shadow-md)` to `var(--depth-shadow-lg)` along with `var(--depth-transform-hover)`).
        *   Underline appearing for links, or text color change.
*   **7.2.3. Focus State (`:focus-visible` - Critical for Accessibility):**
    *   **Purpose:** Indicates the UI element that currently has keyboard focus. This is **essential** for users navigating with a keyboard (e.g., users with motor impairments, screen reader users who also use keyboard navigation, power users).
    *   **Visuals:** Must be **highly visible and clearly distinguishable** from both the default and hover states. The `:focus-visible` pseudo-class is used to ensure these prominent indicators primarily appear for keyboard users, avoiding visual "noise" for mouse users who already receive hover feedback.
        *   **Default Standard:** `outline: 2px solid var(--border-focus-ring-color); outline-offset: 2px;`
        *   The `var(--border-focus-ring-color)` (e.g., `--tk-purple-500` in Light Theme, `--tk-purple-400` in Dark Theme) must provide at least a **3:1 contrast ratio** against the component's own background AND its immediate surrounding page/container background.
*   **7.2.4. Active/Pressed State (`:active`):**
    *   **Purpose:** Indicates that a component is currently being clicked or activated (e.g., mouse button is down on a button, or during the tap action on touch devices). This state is typically brief.
    *   **Visuals:** A more pronounced visual change than hover, signifying direct interaction. Examples include:
        *   An inset shadow effect (e.g., using `var(--depth-shadow-inner)`).
        *   A slightly darker background shade or a subtle scale down (`transform: var(--depth-transform-active);`).
*   **7.2.5. Disabled State (Perceivability & `aria-disabled`):**
    *   **Purpose:** Shows that a component is temporarily not interactive or unavailable to the user.
    *   **Visuals:** Must still be perceivable, and its general form and label (if any) should remain understandable. This guide uses distinct color tokens (`--button-disabled-bg-color`, `--button-disabled-color-text`, `--input-bg-disabled`, etc.) designed for better perceivability rather than relying solely on reduced opacity of the active state colors. `cursor: not-allowed;` should always be applied.
    *   **Text Contrast (Best Practice):** While WCAG 2.1 does not strictly mandate 4.5:1 contrast for text in *truly disabled* (non-interactive per HTML `disabled` attribute) controls for passing SC 1.4.3, it is a best practice to maintain as much readability as possible. The disabled text color tokens (`--text-disabled` or component-specific disabled text colors) are chosen to achieve good perceivability against their defined disabled backgrounds, aiming for at least 3:1 for large text where feasible.
    *   **ARIA Implementation:** For elements that are made non-interactive but should still be perceivable and understood by assistive technologies (e.g., a button that is temporarily disabled but its presence and label are relevant to the user's context), use `aria-disabled="true"`. This allows the element to potentially remain in the focus order (if logical for the user flow) and be announced as "disabled" by screen readers. The native HTML `disabled` attribute on form controls typically removes the element from the tab order and may make it invisible to assistive technologies in forms/interaction mode.
*   **7.2.6. Error/Validation State:**
    *   **Purpose:** Used primarily for form input fields to signal an error in user input or that validation has failed.
    *   **Visuals:** Typically involves changing the input's border color to `var(--semantic-error-color-border)`. An error icon (e.g., using `var(--semantic-error-color-icon)` for its fill) and a clearly worded error message (using `var(--semantic-error-color-text)`) should also be displayed adjacent to the input.
    *   **ARIA Implementation:** Set `aria-invalid="true"` on the input field when it's in an error state. Ensure the visible error message is programmatically associated with the input using `aria-describedby` or, for more complex scenarios, `aria-errormessage`.

#### 7.3. Specific Component Color & State Guidance (with CSS Examples)

**7.3.1. Buttons (Primary, Secondary, Accent, Outline, Disabled)**
Buttons are fundamental interactive elements for initiating actions. They must clearly afford clicking through their visual design, and their current state must be immediately perceivable to the user. All button examples below are assumed to utilize a base `.depth-button` class which applies common structural styles (padding, font, alignment), 3D depth effects (shadows, transforms), and transitions as defined in the CSS Custom Properties (Section 4).

*   **HTML Structure (Recommended for ARIA & Consistency):**
    ```html
    <!-- Standard Button Element -->
    <button class="depth-button button-primary" type="button">Primary Action</button>

    <!-- Link Styled as a Button (for navigation actions) -->
    <a href="/next-page" role="button" class="depth-button button-secondary">Secondary Link Action</a>

    <!-- Programmatically Disabled Button (can still receive focus if needed) -->
    <button class="depth-button button-primary" type="button" aria-disabled="true">Disabled Action (ARIA)</button>

    <!-- HTML Disabled Button (removed from tab order, not focusable) -->
    <button class="depth-button button-primary" type="button" disabled>Truly Disabled (HTML)</button>
    ```

*   **Base `.depth-button` Styles (Common to all variations):**
    ```css
    .depth-button {
      position: relative;
      border-radius: var(--card-border-radius); /* e.g., 8px */
      padding: var(--space-sm) var(--space-md); /* e.g., 8px 16px */
      font-family: var(--font-family-sans);
      font-weight: var(--font-weight-semibold);
      font-size: var(--font-size-md);
      line-height: var(--line-height-normal);
      border: var(--depth-border-width) solid transparent; /* Base border, color set by variants */
      transition: var(--transition-default); /* Smooth transitions for all animatable properties */
      cursor: pointer;
      overflow: hidden; /* Contains ::before pseudo-element */
      display: inline-flex; /* Allows icon + text alignment */
      align-items: center;
      justify-content: center;
      text-align: center;
      text-decoration: none; /* Remove default link underline if <a> is used */
      white-space: nowrap; /* Prevent text wrapping */
      -webkit-appearance: none; /* Normalize appearance across browsers */
      -moz-appearance: none;
      appearance: none;
      min-height: 44px; /* Minimum touch target size (WCAG SC 2.5.5 Target Size - AAA) */
      min-width: 44px; /* Minimum touch target size */
      box-sizing: border-box;
    }

    /* Optional: Inner shimmer effect for 3D feel */
    .depth-button::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: var(--overlay-depth-gradient);
      opacity: 0;
      transition: opacity var(--transition-fast);
      pointer-events: none;
      z-index: 0; /* Ensure it's behind text/icon content */
    }

    .depth-button:hover::before {
      opacity: 1; /* Show shimmer on hover */
    }

    /* General Focus State for all buttons */
    .depth-button:focus-visible {
      outline: 2px solid var(--border-focus-ring-color);
      outline-offset: 2px;
      z-index: 1; /* Ensure focus ring is above other elements if needed */
    }

    /* General Active State for all buttons */
    .depth-button:active {
      transform: var(--depth-transform-active); /* e.g., translateY(1px) */
    }

    /* Responsive: Disable transforms on smaller screens if they cause layout jitter */
    @media (max-width: 768px) {
      .depth-button:hover,
      .depth-button:active {
        transform: none !important;
      }
    }
    ```

*   **Primary Button (`.button-primary` or if `.depth-button` is used standalone):**
    *   **Purpose:** For the principal call to action on a page or within a component (e.g., "Submit," "Save," "Sign Up").
    *   **CSS (Light Theme Example):**
        ```css
        .depth-button.button-primary {
          background-image: var(--button-primary-bg-image); /* e.g., --gradient-primary-brand */
          color: var(--button-primary-color-text); /* e.g., --text-on-dark-bg (--tk-white) */
          box-shadow:
            var(--depth-shadow-md),
            inset 0 1px 0 var(--depth-border-color-highlight),
            inset 0 -1px 0 var(--depth-border-color-shadow);
          border-color: transparent; /* Gradient background, no explicit border */
        }
        .depth-button.button-primary:hover {
          background-image: var(--button-primary-hover-bg-image); /* Darker gradient variant */
          transform: var(--depth-transform-hover);
          box-shadow:
            var(--depth-shadow-lg),
            inset 0 1px 0 var(--depth-border-color-highlight),
            inset 0 -1px 0 var(--depth-border-color-shadow);
        }
        .depth-button.button-primary:active {
          box-shadow:
            var(--depth-shadow-inner),
            /* Inset shadow for pressed feel */ inset 0 1px 0 var(--depth-border-color-shadow),
            /* Border inverts */ inset 0 -1px 0 var(--depth-border-color-highlight);
        }
        /* Focus-visible styles are inherited from .depth-button */
        ```
    *   **WCAG Text Contrast (Light Theme):** `var(--tk-white)` on average of `--tk-purple-500` & `--tk-indigo-500` (approx `#7143D8`) is **6.4:1 (AAA Pass for large, AA Pass for normal)**.
    *   **WCAG Non-Text Contrast (Focus Ring - Light Theme):** `var(--border-focus-ring-color)` (`--tk-purple-500`) must have 3:1 contrast against the button's gradient average *and* the surrounding page background (e.g., `--bg-content`). `--tk-purple-500` on `--bg-content (#FFF)` is **6.29:1 (AAA Pass)**. The contrast of the focus ring against the button itself is also important; `--tk-purple-500` on its own gradient is lower but usually sufficient due to the outline nature.
    *   **"Dark Purple Driven" Theme Adaptation:**
        *   If page BG is dark purple (e.g., `--tk-purple-900`):
            *   Primary buttons might use a brighter or contrasting gradient to stand out:
                `--button-primary-bg-image: linear-gradient(to right, var(--tk-purple-500), var(--tk-violet-400));`
            *   Focus ring: `var(--border-focus-ring-color)` should be a light, contrasting color from the dark theme overrides (e.g., `--tk-purple-300`).

*   **Secondary Button (`.button-secondary`):**
    *   **Purpose:** For less prominent actions, alternative choices that are still important.
    *   **CSS (Light Theme Example):**
        ```css
        .depth-button.button-secondary {
          background-image: var(--button-secondary-bg-image); /* e.g., --gradient-secondary-brand (Blue/Cyan) */
          color: var(--button-secondary-color-text); /* e.g., --text-on-dark-bg (--tk-white) */
          box-shadow:
            var(--depth-shadow-md),
            inset 0 1px 0 var(--depth-border-color-highlight),
            inset 0 -1px 0 var(--depth-border-color-shadow);
          border-color: transparent;
        }
        .depth-button.button-secondary:hover {
          background-image: var(--button-secondary-hover-bg-image);
          transform: var(--depth-transform-hover);
          box-shadow: var(--depth-shadow-lg); /* etc. */
        }
        /* Active and Focus-visible states similar to primary, adapting colors as needed */
        ```
    *   **WCAG Text Contrast (Light Theme):** `var(--tk-white)` on average of `--tk-blue-500` & `--tk-cyan-500` (approx `#2284F8`) is **4.63:1 (AA Pass)**.
    *   **"Dark Purple Driven" Theme Adaptation:** Could use a lighter purple/violet gradient or a distinct neutral dark gradient (e.g., from dark grays). Text color would remain light.

*   **Accent Button (`.button-accent`):**
    *   **Purpose:** For high-emphasis actions, often unique on a page (e.g., "Start Free Trial," "Special Offer").
    *   **CSS (Light Theme Example):**
        ```css
        .depth-button.button-accent {
          background-image: var(--button-accent-bg-image); /* e.g., --gradient-accent-brand (Pink/Rose) */
          color: var(--button-accent-color-text); /* e.g., --tk-black for contrast */
          /* ... shadows and states similar to primary ... */
        }
        ```
    *   **WCAG Text Contrast (Light Theme):** `var(--tk-black)` on average of `--tk-pink-500` & `--tk-rose-500` (approx `#F0417B`) is **7.05:1 (AAA Pass)**.
    *   **"Dark Purple Driven" Theme Adaptation:** The existing Pink/Rose accent often provides good contrast on dark purple. `var(--tk-black)` text would still work. Alternatively, a vibrant Violet/Pink gradient could be used with `var(--text-on-dark-bg)` if the gradient is dark enough to ensure contrast.

*   **Outline Button (`.button-outline`):**
    *   **Purpose:** For secondary or tertiary actions that require less visual weight than solid buttons (e.g., "Cancel," "Learn More" in a less prominent spot).
    *   **CSS (Light Theme Example):**
        ```css
        .depth-button.button-outline {
          background-color: transparent; /* No background fill */
          color: var(--button-outline-color-text); /* e.g., --tk-purple-600 */
          border: 2px solid var(--button-outline-color-border); /* e.g., --tk-purple-500 */
          box-shadow: none; /* Typically no 3D shadow for outline buttons */
        }
        .depth-button.button-outline::before {
          display: none;
        } /* No inner shimmer */

        .depth-button.button-outline:hover {
          background-color: var(--button-outline-hover-bg-color); /* e.g., --tk-purple-50 */
          color: var(--button-outline-hover-color-text); /* e.g., --tk-purple-700 */
          border-color: var(--button-outline-color-border); /* Border color can remain same or adapt */
          /* No transform by default for outline, or add var(--depth-transform-hover) if desired */
        }
        .depth-button.button-outline:active {
          background-color: var(--tk-purple-100); /* Slightly darker active BG */
          transform: var(--depth-transform-active); /* Subtle press effect */
        }
        .depth-button.button-outline:focus-visible {
          outline: 2px solid var(--button-outline-color-border); /* Use its own border color or --border-focus-ring-color */
          outline-offset: 1px; /* Adjust offset due to existing border */
        }
        ```
    *   **WCAG Text Contrast (Text/Page BG - Light Theme):** `--tk-purple-600` on `--bg-content (#FFF)` is **5.36:1 (AA Pass)**.
    *   **WCAG Non-Text Contrast (Border/Page BG - Light Theme):** `--tk-purple-500` on `--bg-content (#FFF)` is **6.29:1 (AAA Pass)**.
    *   **"Dark Purple Driven" Theme Adaptation (on `--tk-purple-900` page BG):**
        *   `color: var(--tk-purple-300);`
        *   `border-color: var(--tk-purple-400);`
        *   Hover BG: `rgba(var(--tk-purple-400-rgb), 0.15);`
        *   Hover Text: `var(--tk-purple-200);`
        *   Focus Outline: `var(--tk-purple-300)`.

*   **Disabled Button (`.button-disabled`, or `[aria-disabled="true"]` on any button variant):**
    *   **Purpose:** To indicate an action is currently unavailable.
    *   **CSS (Applied to any `.depth-button` variant):**
        ```css
        .depth-button.button-disabled,
        .depth-button[aria-disabled='true'] {
          background-image: none; /* Remove gradients */
          background-color: var(--button-disabled-bg-color); /* Theme-dependent */
          color: var(--button-disabled-color-text); /* Theme-dependent */
          border: 1px solid var(--button-disabled-color-border); /* Theme-dependent */
          box-shadow: none; /* No depth or interactive shadows */
          cursor: not-allowed;
          transform: none !important; /* No hover/active transforms */
          opacity: 1; /* Control appearance with direct colors, not just opacity, for better contrast control */
        }
        .depth-button.button-disabled::before,
        .depth-button[aria-disabled='true']::before {
          display: none; /* No inner shimmer */
        }
        /* Subdued focus for ARIA-disabled elements if they remain focusable */
        .depth-button[aria-disabled='true']:focus-visible {
          outline: 2px dashed var(--text-disabled); /* Use a muted color from the current theme */
          outline-offset: 2px;
        }
        ```
    *   **WCAG Text Perceivability (Light Theme):** `--tk-gray-600` (text) on `--tk-gray-100` (bg). Contrast: **5.43:1 (AA Pass)**.
    *   **Dark Theme:** Uses `--button-disabled-bg-color: var(--tk-gray-700); --button-disabled-color-text: var(--tk-gray-400);`. Contrast: **3.0:1 (AA for Large Text)**.
    *   **Night Theme:** Uses `--button-disabled-bg-color: #3a342f; --button-disabled-color-text: #7a736a;`. Contrast: **3.06:1 (AA for Large Text)**.
    *   *For normal-sized text on disabled buttons in Dark/Night themes, if higher contrast is desired, the `--button-disabled-color-text` would need to be lightened further, or text made bold.*

---
This completes Phase 2 of v16. The next phase will continue with Section 7.3.2 (Forms & Input Fields) and the remaining UI components.Okay, here is **TechnoKids Color Palette Guide v16 - Phase 3 of 3 (Self-Contained)**.

This final phase provides the complete, self-contained Sections 7.3.2 onwards (Forms & Input Fields, Toggle Buttons, Modals, Navigation, Cards, Alerts, Icons, TechnoKids specific items), and then Sections 8 (Depth, Shadows, & Layering Effects), 9 (Animations, Micro-interactions, & Motion), 10 (Theming: Light, Dark, & Night Modes - Recap & Principles), 11 (Accessibility Compliance & Best Practices - Summary), and 12 (Conclusion & Governance). All content is written to stand alone within this v16 context, building upon the foundations laid in the previous phases of v16.

---

# TechnoKids Color Palette Guide v16 - Phase 3 of 3

*(Continuing from Phase 2 of v16)*

---

**7.3. Specific Component Color & State Guidance (with CSS Examples) - Continued**

**7.3.2. Forms & Input Fields (Text, Select, Checkbox, Radio) - Continued**
Form elements are fundamental for user interaction and data input. Ensuring their clarity, usability, and accessibility across all themes is paramount. All form inputs **must** have programmatically associated `<label>` elements (WCAG SC 1.3.1 Information and Relationships, SC 3.3.2 Labels or Instructions). Clear instructions should be provided, and required fields must be indicated using more than color alone (e.g., an asterisk and visually hidden text "(required)"). Error messages must be specific, easy to understand, and programmatically linked to the relevant input (e.g., via `aria-describedby` or `aria-errormessage`).

*   **General Form Principles for "Dark Purple Driven" Themes:**
    *   When the main content area background (`--bg-content`) is a deep purple (e.g., `var(--tk-purple-900)`), input fields need a distinct background to differentiate them. This could be:
        *   A darker shade from the same color family (e.g., `var(--tk-indigo-950)` or `var(--tk-purple-950)`).
        *   A contrasting dark neutral from the Dark Theme palette (e.g., `var(--tk-gray-800)` if it offers enough visual separation).
    *   Text, placeholder text, and border colors within these inputs must then be chosen to provide sufficient contrast against this specific input background.
    *   Focus indicators must be highly prominent against both the input background and the surrounding deep purple page background.

*   **Text Input (`.depth-input`):**
    *   **HTML Structure:**
        ```html
        <div class="form-group">
          <label for="username" class="form-label">Username <span class="required-indicator">*</span></label>
          <input type="text" id="username" class="depth-input" placeholder="e.g., techkid123">
          <div class="form-hint" id="usernameHint">Your unique username for login.</div>
          <!-- Error message placeholder, to be shown on error -->
          <!-- <div class="form-error-message" id="usernameError" role="alert">Username is required.</div> -->
        </div>
        <style>.required-indicator { color: var(--semantic-error-base); margin-left: var(--space-xs); }</style>
        ```
    *   **CSS (Base Styles - Light Theme Defaults):**
        ```css
        .form-group {
          margin-bottom: var(--space-lg);
        }
        .form-label {
          display: block;
          font-weight: var(--font-weight-semibold);
          color: var(--text-primary);
          margin-bottom: var(--space-xs);
          font-size: var(--font-size-sm);
        }
        .depth-input {
          width: 100%; /* Default to full width of its container */
          background-color: var(--bg-input);
          color: var(--text-primary);
          border: var(--depth-border-width) solid var(--border-input); /* var(--tk-gray-400) */
          border-radius: var(--input-border-radius);
          padding: var(--space-sm) var(--space-md); /* 8px 16px */
          font-size: var(--font-size-md);
          line-height: var(--line-height-normal);
          box-shadow:
            var(--depth-shadow-inner),
            /* Subtle inset shadow */ inset 0 1px 2px var(--depth-border-color-shadow); /* Darker bottom edge for depth */
          transition:
            border-color var(--transition-fast),
            box-shadow var(--transition-fast),
            background-color var(--transition-normal),
            color var(--transition-normal);
          box-sizing: border-box;
        }
        .depth-input::placeholder {
          color: var(--text-placeholder); /* var(--tk-gray-500) */
          opacity: 1; /* Ensure placeholders are not overly faint */
        }
        .depth-input:focus-visible {
          border-color: var(--input-border-color-focus); /* var(--tk-purple-500) */
          box-shadow:
            var(--depth-shadow-sm),
            /* Slight lift */ var(--input-focus-shadow-ring); /* e.g., 0 0 0 3px var(--border-focus-ring-color) */
          outline: none;
        }
        .depth-input[aria-invalid='true'] {
          border-color: var(--semantic-error-color-border); /* var(--tk-red-300) */
          background-color: var(--semantic-error-color-bg); /* var(--tk-red-100) - subtle error bg */
          color: var(--semantic-error-color-text); /* var(--tk-red-700) - if text itself should be error colored */
          /* If only border indicates error, text color remains --text-primary */
        }
        .form-hint,
        .form-error-message {
          font-size: var(--font-size-sm);
          margin-top: var(--space-xs);
        }
        .form-hint {
          color: var(--text-subtle);
        }
        .form-error-message {
          color: var(--semantic-error-color-text);
          font-weight: var(--font-weight-semibold);
        }
        .depth-input[disabled],
        .depth-input[aria-disabled='true'] {
          background-color: var(--input-bg-disabled); /* var(--tk-gray-100) */
          color: var(--input-color-text-disabled); /* var(--text-disabled) -> --tk-gray-600 */
          border-color: var(--input-border-color-disabled); /* var(--tk-gray-200) */
          cursor: not-allowed;
          box-shadow: var(--depth-shadow-inner);
        }
        ```
    *   **WCAG Contrast (Light Theme):**
        *   Default Border (`--tk-gray-400`) on Input BG (`--tk-white`): **3.0:1 (AA Pass for Non-Text)**.
        *   Placeholder Text (`--tk-gray-500`) on Input BG (`--tk-white`): **4.61:1 (AA Pass for Text)**.
        *   Focus Border (`--tk-purple-500`) on Input BG (`--tk-white`): **6.29:1 (AAA Pass for Non-Text)**.
        *   Error Border (`--tk-red-300`) on Input BG (`--tk-white`): **3.94:1 (AA Pass for Non-Text)**.
        *   Disabled Text (`--tk-gray-600`) on Disabled BG (`--tk-gray-100`): **5.43:1 (AA Pass for Text)**.
    *   **"Dark Purple Driven" Theme Adaptation (Example: Page BG `--tk-purple-900`, Input BG `--tk-indigo-950`):**
        ```css
        [data-theme='dark_purple_explicit'] .depth-input {
          background-color: var(--tk-indigo-950); /* e.g., #1e1b4b */
          color: var(--tk-gray-200); /* Light text. Contrast on indigo-950: 12.9:1 (AAA) */
          border: var(--depth-border-width) solid var(--tk-indigo-800); /* e.g., #3730a3 */
          /* Non-Text Contrast (Border/Input BG): var(--tk-indigo-800) on var(--tk-indigo-950) is 1.9:1 (FAIL) */
          /* Option: Increase border lightness or rely on distinct input BG from page BG + strong focus */
          /* Corrected Border for Dark Purple Input: */
          border-color: var(--tk-indigo-700); /* #4338ca. Contrast on #1e1b4b is 2.8:1 (Fail). */
          /* This remains a challenge. A common solution is a borderless input on dark, with clear focus. */
          /* border-color: transparent; /* If input BG alone provides distinction from page BG */
          /* OR use a subtle different dark shade for border */
          border-color: var(--tk-gray-700); /* #374151. Contrast on #1e1b4b (Indigo-950) is 2.2:1 (Fail) */
          /* Using a slightly lighter Indigo for border: */
          /* border-color: var(--tk-indigo-900); /* #312e81. Contrast on #1e1b4b (Indigo-950) is 1.2:1 (Fail) */
          /* Using Gray from dark theme for better contrast if brand colors fail */
          border-color: var(--tk-gray-600); /* #4b5563. Contrast on #1e1b4b (Indigo-950) is 3.1:1 (AA Pass) */

          box-shadow:
            var(--depth-shadow-inner),
            inset 0 1px 2px rgba(var(--tk-black-rgb), 0.4); /* Darker inner shadow */
        }
        [data-theme='dark_purple_explicit'] .depth-input::placeholder {
          color: var(--tk-indigo-400); /* #818cf8. Contrast on #1e1b4b (Indigo-950): 4.7:1 (AA Pass) */
        }
        [data-theme='dark_purple_explicit'] .depth-input:focus-visible {
          border-color: var(--tk-violet-400); /* Bright violet focus border, e.g., #a78bfa */
          box-shadow:
            var(--depth-shadow-sm),
            0 0 0 3px var(--tk-violet-400); /* Focus ring using violet */
        }
        [data-theme='dark_purple_explicit'] .depth-input[aria-invalid='true'] {
          border-color: var(--semantic-error-color-border); /* Dark theme semantic error border, e.g., --tk-red-600 */
          background-color: rgba(var(--semantic-error-color-icon-rgb), 0.1); /* Assuming --tk-red-300-rgb for error icon */
          color: var(--semantic-error-color-text); /* Dark theme semantic error text, e.g., --tk-red-300 */
        }
        [data-theme='dark_purple_explicit'] .depth-input[disabled],
        [data-theme='dark_purple_explicit'] .depth-input[aria-disabled='true'] {
          background-color: var(--tk-purple-800); /* Or a consistent dark neutral like --tk-gray-700 */
          color: var(--tk-purple-400); /* Muted text on the disabled bg */
          border-color: var(--tk-purple-700);
          /* If using neutrals: background: var(--tk-gray-700); color: var(--tk-gray-400); border: var(--tk-gray-600) */
        }
        ```
        *The selected Dark Theme disabled input colors from Section 4.4 (`--input-bg-disabled: var(--tk-gray-800); --input-color-text-disabled: var(--tk-gray-500); --input-border-color-disabled: var(--tk-gray-700);`) are generally safer for consistent perceivability in dark themes.*

*   **Select Element (`<select>`):**
    *   Styled to visually match `.depth-input` using the same background, border, color, and state variables.
    *   The custom dropdown arrow needs to adapt its `fill` color for each theme to ensure contrast against the select's background.
        *   Light Theme Arrow: `fill: var(--tk-gray-600);`
        *   Dark Theme / Dark Purple Input Arrow: `fill: var(--tk-gray-300);`
        *   Night Theme Arrow: `fill: var(--text-secondary);` (e.g., `#a89f95`)
    *   Disabled state should also mute the arrow color.
    *   (Refer to Phase 2 CSS for basic select styling, apply theme-specific color variables for arrow).

*   **Checkbox & Radio Button:**
    *   Custom styled checkboxes/radios (as shown in Phase 2 CSS structure) need careful color application for their states in different themes.
    *   **Light Theme:**
        *   Default Border: `var(--border-input)` (`--tk-gray-400`). Background: `var(--bg-input)` (`--tk-white`).
        *   Checked Background/Border: `var(--tk-purple-500)`. Tick/Dot: `var(--tk-white)`.
        *   Focus Outline: `var(--border-focus-ring-color)` (`--tk-purple-500`).
    *   **"Dark Purple Driven" Theme / Dark Theme (on a dark purple or dark gray page):**
        *   Label Text: `var(--text-secondary)` (e.g., `--tk-gray-300`).
        *   Default Border: `var(--tk-gray-500)` or `var(--tk-indigo-400)` (to contrast with input background if inputs are also dark).
        *   Default Background: `var(--bg-input)` (e.g., `--tk-gray-700` or `--tk-indigo-950`).
        *   Checked Background/Border: `var(--tk-violet-400)` or `var(--tk-pink-400)` (a brighter accent).
        *   Tick/Dot: `var(--tk-black)` or a very dark purple if the checked BG is light enough; or `var(--tk-white)` if checked BG is a strong color.
        *   Focus Outline: `var(--border-focus-ring-color)` (e.g., `--tk-purple-300` or `--tk-violet-300`).
        *   Disabled: Muted versions using dark theme grays (e.g., border `--tk-gray-600`, BG `--tk-gray-700`, tick/dot `--tk-gray-500`).
    *   **WCAG Non-Text Contrast:**
        *   The boundary of the un-checked control must contrast 3:1 with its surrounding background.
        *   The checkmark/dot must contrast 3:1 with the control's checked background.
        *   The focus indicator must meet 3:1 with its surroundings.

**7.3.3. Toggle Buttons (`.depth-toggle`)**
(As detailed in Phase 2, using `role="switch"` and `aria-checked`)

*   **Light Theme:**
    *   Off Track: `var(--tk-gray-300)` BG, `var(--tk-gray-400)` Border. Knob: `var(--tk-white)`.
    *   On Track: `var(--tk-purple-500)` BG, `var(--tk-purple-600)` Border. Knob: `var(--tk-white)`.
    *   Focus: `var(--border-focus-ring-color)`.
*   **"Dark Purple Driven" Theme / Dark Theme:**
    *   Off Track: `var(--tk-gray-700)` BG, `var(--tk-gray-600)` Border. Knob: `var(--tk-gray-300)`.
    *   On Track: `var(--tk-violet-500)` BG, `var(--tk-violet-600)` Border. Knob: `var(--tk-white)`.
    *   Focus: `var(--border-focus-ring-color)` (e.g., `--tk-purple-300`).
*   **WCAG Non-Text Contrast:** Critical for knob vs. track in both states, and track vs. page background.

**7.3.4. Modals & Dialogs**
(Structure from Phase 2. Key: Focus Trapping, ARIA, Dismissibility)

*   **Light Theme:**
    *   Overlay: `var(--bg-overlay)` (`rgba(0,0,0,0.4)`).
    *   Modal Content BG: `var(--modal-bg-color)` (`--bg-card` -> `--tk-white`).
    *   Header/Footer Borders: `var(--border-standard)` (`--tk-gray-200`).
    *   Title Text: `var(--text-primary)`. Body Text: `var(--text-secondary)`.
    *   Close Button: `var(--text-subtle)`, hover `var(--text-primary)`.
*   **"Dark Purple Driven" Theme / Dark Theme:**
    *   Overlay: `var(--bg-overlay)` (Dark theme override, e.g., `rgba(17,24,39,0.8)`).
    *   Modal Content BG: `var(--modal-bg-color)` (Dark theme: `--tk-gray-800`).
        *   *For "Dark Purple Driven":* Could be `var(--tk-indigo-950)` or `var(--tk-purple-900)`.
    *   Header/Footer Borders: `var(--border-standard)` (Dark theme: `--tk-gray-700`).
    *   Title Text: `var(--text-primary)` (Dark theme: `--tk-white`).
    *   Body Text: `var(--text-secondary)` (Dark theme: `--tk-gray-300`).
    *   Close Button: `var(--text-subtle)` (Dark theme: `--tk-gray-400`), hover `var(--text-primary)`.
*   Buttons within modal footer follow standard button styling for the active theme.

**7.3.5. Navigation Elements (Menus, Tabs, Breadcrumbs)**

*   **Main Navigation / Headers (`.depth-header`):**
    *   **Light Theme:** `background: var(--gradient-primary-brand)`. Link text: `var(--text-on-dark-bg)`.
    *   **"Dark Purple Driven" Theme / Dark Theme:**
        *   Could use `var(--gradient-dark-purple-hero)` or a solid `var(--tk-purple-950)`.
        *   Link text: `var(--text-on-dark-bg)` or brighter accents like `var(--tk-purple-300)` or `var(--tk-violet-400)`.
        *   Active Link: Background highlight (e.g., `rgba(var(--tk-white-rgb), 0.1)`) or distinct text color (`var(--tk-pink-300)`).
        *   Focus: Visible outline using a light contrasting color.
*   **Tabs:**
    *   **Light Theme:** Inactive text `var(--text-link)`. Active text `var(--text-primary)`, active border `var(--border-interactive-strong)` (`--tk-purple-500`).
    *   **"Dark Purple Driven" Theme / Dark Theme (on dark page BG):**
        *   Inactive text `var(--tk-purple-300)`.
        *   Active text `var(--tk-white)`. Active border `var(--tk-violet-400)`.
        *   Focus outline: `var(--border-focus-ring-color)` (e.g. dark theme's `--tk-purple-300`).
*   **Breadcrumbs:**
    *   **Light Theme:** Link `var(--text-link)`. Current `var(--text-secondary)`. Separator `var(--text-subtle)`.
    *   **"Dark Purple Driven" Theme / Dark Theme:** Link `var(--tk-purple-300)`. Current `var(--tk-gray-300)`. Separator `var(--tk-purple-700)` (or a lighter subtle purple).

**7.3.6. Cards (`.depth-card`) & Content Containers**

*   **Light Theme:** `background-color: var(--bg-card)` (`--tk-white`). `::before` accent: `var(--gradient-primary-brand)`.
*   **"Dark Purple Driven" Theme (on dark purple page BG, e.g., `--tk-purple-900`):**
    *   Card BG: `var(--tk-indigo-900)` or `var(--tk-purple-800)` for subtle separation, or `var(--tk-gray-800)` for neutral separation.
    *   `::before` accent: Could use `var(--gradient-violet-accent)` or `var(--gradient-accent-brand)` (Pink/Rose) for a pop of color.
    *   Text within these dark cards must use light colors (`--text-on-dark-bg`, `--tk-gray-200`, etc.) ensuring contrast against the specific card BG.

**7.3.7. Alerts & Notifications (Semantic States)**

*   **Structure:** Icon + Text Message.
*   **ARIA:** `role="alert"` (for critical, assertive) or `role="status"` (for polite).
*   **Light Theme:**
    *   Success: BG `var(--semantic-success-color-bg)`, Text `var(--semantic-success-color-text)`, Border `var(--semantic-success-color-border)`, Icon `var(--semantic-success-color-icon)`.
*   **"Dark Purple Driven" Theme / Dark Theme:**
    *   Uses the redefined semantic color variables for dark mode (e.g., translucent BGs, lighter text/icon colors).
    *   Example Success: BG `rgba(var(--tk-green-800-rgb), 0.3)`, Text `var(--tk-green-300)`, Border `var(--tk-green-700)`, Icon `var(--tk-green-300)`.
    *   These need to contrast adequately with the underlying page color (e.g., if on `--tk-purple-900`). A slightly more opaque semantic background might be needed if contrast is lost.

**7.3.8. Icons (Informational vs. Decorative)**

*   **Color:**
    *   Default: `fill: currentColor;` (inherits text color).
    *   Light Theme Informational: `var(--text-secondary)` or `var(--text-subtle)`.
    *   Dark Theme / Dark Purple Informational: `var(--tk-gray-300)`, `var(--tk-purple-300)`, or `var(--tk-violet-300)`.
    *   Semantic (as part of alerts, etc.): `var(--semantic-*-color-icon)`.
*   **WCAG Non-Text Contrast:** If an icon is essential for understanding content or functionality and is not accompanied by visible text label, its primary strokes need 3:1 contrast with its immediate background.
*   **Decorative:** `aria-hidden="true"`.

**7.3.9. TechnoKids Specific: Session & Thematic Elements**

*   **Light Theme:** Uses light brand tints (e.g., `--session-1-bg: var(--tk-purple-100);`). Gradients for number BGs as defined.
*   **"Dark Purple Driven" Theme / Dark Theme:**
    *   The dark theme already defines translucent dark brand colors for session BGs (e.g., `--session-1-bg: rgba(var(--tk-purple-900-rgb), 0.5);`).
    *   If the main page itself is a very dark purple (e.g., `--tk-purple-900`), these session backgrounds might need adjustment to ensure visual separation. Options:
        *   Use even darker shades with opacity (e.g., `rgba(var(--tk-purple-950-rgb), 0.6)`).
        *   Use a contrasting dark neutral as their base before opacity (e.g., `rgba(var(--tk-gray-800-rgb), 0.7)`).
        *   Use a very subtle, slightly lighter opaque purple (e.g., `var(--tk-purple-800)`).
    *   Text (e.g., `--session-1-text: var(--tk-purple-300);`) must contrast adequately with these adapted backgrounds.
    *   Number BG gradients would use darker, richer versions (e.g., `linear-gradient(to right, var(--tk-purple-700), var(--tk-indigo-800))`).

**7.3.10. TechnoKids Specific: Logo & Feature Items (`.depth-logo-item`, `.depth-feature`)**

*   **`.depth-logo-item`:**
    *   Light Theme: `background-color: var(--bg-card)`. Text: `var(--text-primary)`.
    *   Dark/Purple Theme: `background-color: var(--tk-indigo-900)` or `var(--tk-purple-800)`. Text: `var(--text-on-dark-bg)`.
*   **`.depth-feature`:**
    *   Light Theme: `background: var(--overlay-depth-gradient), var(--gradient-primary-brand);` color `var(--text-on-dark-bg)`.
    *   Dark/Purple Theme: This component is designed to be a highlight. It can retain its vibrant primary gradient for contrast. Alternatively, for a more integrated "dark purple" feel, it could use:
        *   `background: var(--overlay-depth-gradient), var(--gradient-dark-purple-hero);`
        *   `background: var(--overlay-depth-gradient), var(--gradient-violet-accent);`
        *   Text color remains `var(--text-on-dark-bg)`. Contrast must be verified if the chosen gradient is lighter.

#### 7.4. *Conceptual Components (Principles for Future Implementation)*
*   **Social Proof / Stats Blocks:** On light themes, use `--text-primary` for stats, `--text-secondary` for labels. On dark/purple themes, use `--text-on-dark-bg` (or light grays like `--tk-gray-200`) for stats, and `--tk-gray-300` or `--tk-purple-300` for labels. Ensure ample white/dark space.
*   **Tabbed Content & Interactive Elements:** Follow ARIA patterns for tabs (Section 7.3.5). Ensure all states are visually distinct and accessible in all themes.
*   **Video Placeholders:** Play icon must have 3:1 contrast. If showing a thumbnail image, any text overlay must be highly readable (consider scrims).
*   **Mega Menus / Dropdowns:** These are complex navigation structures. Must be fully keyboard accessible, manage focus correctly (trapping within dropdown when open), and use appropriate ARIA roles (`menu`, `menuitem`, `aria-haspopup`, `aria-expanded`). Backgrounds will typically follow card/content area colors for the theme (e.g., `--bg-card`), with interactive items using hover/focus states consistent with list items or buttons.
*   *(Designers and developers **must** use the color variables, typographic scales, interactive state principles, and accessibility mandates defined in this guide when creating these and any new components to ensure consistency and inclusivity across all themes.)*

---

### 8. Depth, Shadows, & Layering Effects

Visual depth and layering enhance UI hierarchy, draw attention to interactive elements, and contribute to a modern, "Divi-inspired" aesthetic. This section details the strategic and consistent use of shadows and layering techniques across all themes.

#### 8.1. Strategic Use of Depth for Hierarchy
*   **Elevation & Prominence:** Elements that are conceptually "closer" to the user or require more attention (e.g., modals, dropdown menus, active/primary buttons) use stronger, more diffuse shadows (`var(--depth-shadow-lg)`, `var(--depth-shadow-xl)`, `var(--depth-shadow-2xl)`) to appear elevated from the page.
*   **Interactivity Cues:** Interactive elements often gain a subtle shadow lift on hover (e.g., transitioning from `var(--depth-shadow-md)` to `var(--depth-shadow-lg)`) to indicate affordance and responsiveness.
*   **Containment & Separation:** Cards, content panels, and distinct UI sections use shadows (typically `var(--depth-shadow-md)` or `var(--depth-shadow-sm)`) to define their boundaries and visually separate them from the underlying background, aiding in content organization.
*   **Inset Effects:** Inner shadows (`var(--depth-shadow-inner)`) are used for specific states like pressed buttons or to give input fields a subtle recessed appearance.

#### 8.2. Standard & Interactive Shadows
The `--depth-shadow-*` variables provide a consistent and scalable system for applying shadows. These variables are redefined for Dark and Night themes (see Section 4.4) to ensure they render appropriately on darker backgrounds—typically meaning the shadow color itself becomes less dark (or even a very dark opaque color rather than translucent black) or more diffuse to be perceivable.

*   **Light Theme Shadow Appearance:** Shadows are typically soft, translucent blacks (`rgba(0,0,0, opacity)`).
*   **Dark/Night Theme Shadow Appearance:**
    *   Shadows are often more subtle as they have less contrast against dark backgrounds.
    *   The color of the shadow might shift from pure black to a very dark gray or a desaturated version of the background color to appear more natural.
    *   Alternatively, depth in dark themes can also be conveyed by slightly lighter surface colors for elevated elements, rather than relying solely on shadows. Our system uses darker, more opaque black shadows for dark themes to give some perceivable depth.

**CSS Application:**
```css
.card-example {
  box-shadow: var(--depth-shadow-md);
  transition: box-shadow var(--transition-fast);
}
.card-example:hover {
  box-shadow: var(--depth-shadow-lg);
}
.button-pressed-example {
  box-shadow: var(--depth-shadow-inner);
}
```

#### 8.3. Decorative Background Elements (Blobs, Shapes)
Subtle, low-opacity, blurred shapes or gradients can be used in backgrounds to add visual interest, texture, and a sense of depth without distracting from primary content or interactive elements.
*   **Implementation:** Typically achieved using absolutely positioned pseudo-elements (`::before`, `::after`) or dedicated `div` elements with `z-index: -1;` (or a low positive z-index if behind specific content but above a main background).
*   **Color & Appearance:**
    *   Use `var(--gradient-decorative-blob)` which is defined with low-alpha brand colors (`--decorative-blob-color-start`, `--decorative-blob-color-end`).
    *   Opacity is often controlled within the RGBA definition of these color variables or by applying an additional `opacity` property to the element.
    *   A heavy `filter: blur(VALUEpx);` (e.g., `blur(60px)` or more) is applied to create a soft, ethereal effect.
    *   **Thematic Adaptation:** The base colors for these blobs (`--decorative-blob-color-start`, `--decorative-blob-color-end`) are defined globally. In Dark/Night themes, their perceived intensity will change. If more distinct blob colors are needed for dark themes, theme-specific blob color variables could be introduced.
    ```css
    .section-with-blob::before {
      content: '';
      position: absolute;
      top: -10%;
      left: -10%; /* Example positioning */
      width: clamp(300px, 50vw, 600px); /* Responsive size */
      height: clamp(300px, 50vw, 600px);
      background-image: var(--gradient-decorative-blob);
      opacity: 0.1; /* Further adjust overall opacity, can be theme-dependent */
      filter: blur(70px);
      z-index: -1;
      pointer-events: none;
    }
    ```

#### 8.4. Overlapping Elements
Creating visual depth and hierarchy through overlapping elements is a common modern UI technique.
*   **Implementation:** Achieved using CSS positioning properties (`position: relative;` on a parent container, and `position: absolute;` or `position: fixed;` for child elements that overlap). The `z-index` property is crucial for controlling the stacking order of these layered elements. Negative margins can also be used carefully to create overlap.
*   **Purpose:**
    *   Creates visual interest and dynamism.
    *   Reinforces relationships between elements (e.g., an image thumbnail peeking out from under a card's header).
    *   Can guide the user's eye to focal points.
*   **Accessibility Considerations:**
    *   **Logical Content Order:** Ensure the reading order in the DOM remains logical and comprehensible for screen reader users, even if elements are visually repositioned.
    *   **No Obscuring:** Overlapping elements must not obscure essential information or interactive controls for any users, including those navigating with keyboards or using screen magnification.
    *   **Focus Management:** If overlapping creates distinct layers (like a dropdown menu appearing over content), ensure keyboard focus is managed correctly (e.g., focus moves into the dropdown and is contained if necessary).

---

### 9. Animations, Micro-interactions, & Motion

Animations and micro-interactions are used thoughtfully within the TechnoKids design system to enhance user experience, provide clear visual feedback, guide attention, and add a touch of brand personality, without being distracting or causing accessibility issues.

#### 9.1. Purposeful Motion
All motion introduced into the interface should have a clear and beneficial purpose:
*   **Feedback & Confirmation:** Animations can confirm user actions (e.g., a button press effect, success/error state transitions, item added to a list).
*   **State Changes:** Smoothly transition UI elements between different states (e.g., expanding/collapsing accordions, opening/closing modals, tab switches), making these changes easier to follow.
*   **Guidance & Attention:** Subtly direct the user's attention to important information, new content, or the next logical step in a flow.
*   **Hierarchy & Spatial Relationships:** Animations can help establish or clarify spatial relationships between UI elements (e.g., a panel sliding in from the side).
*   **Delight & Brand Expression (Used Sparingly):** Subtle, engaging animations can enhance the brand experience and make interactions more enjoyable, but these should never impede usability or become intrusive.

#### 9.2. Hover Effects & Transforms
*   **Buttons & Interactive Cards:** On hover (`:hover`), elements like buttons and cards may utilize `transform: var(--depth-transform-hover);` (e.g., `translateY(-2px)`) and a change in `box-shadow` (e.g., from `var(--depth-shadow-md)` to `var(--depth-shadow-lg)`) to create a subtle "lift" effect, indicating interactivity.
*   **Links:** Typically receive an underline and a color change to `var(--text-link-hover)` on hover and focus.
*   **Transitions:** All property changes for hover, focus, and active states should use smooth CSS transitions, typically `var(--transition-fast)` or `var(--transition-normal)`.
    ```css
    .interactive-element {
      /* ... other styles ... */
      transition: var(--transition-default); /* Applies to transform, box-shadow, background-color etc. */
    }
    .interactive-element:hover {
      transform: var(--depth-transform-hover);
      box-shadow: var(--depth-shadow-lg);
      /* Potentially other changes like background-color */
    }
    ```

#### 9.3. Scroll-Triggered Animations (Considerations)
Subtle fade-ins or slide-ins for content elements as they enter the viewport during scrolling can enhance engagement and guide the user through long pages.
*   **Implementation:** Best implemented using JavaScript with the Intersection Observer API to efficiently detect when elements become visible, then adding a class (e.g., `.is-visible`) to trigger CSS animations/transitions.
    ```css
    .animate-on-scroll {
      opacity: 0;
      transform: translateY(var(--space-md)); /* Start slightly offset (e.g., 16px down) */
      transition:
        opacity 0.5s var(--transition-timing-function),
        transform 0.5s var(--transition-timing-function);
      will-change: opacity, transform; /* Hint to browser for performance */
    }
    .animate-on-scroll.is-visible {
      opacity: 1;
      transform: translateY(0);
    }
    ```
*   **Accessibility (`prefers-reduced-motion`):** These animations **must** be disabled or significantly reduced if the user has indicated a preference for reduced motion (see Section 9.5).

#### 9.4. Loading Indicators & UI Feedback
*   **Spinners/Loaders:** Use subtle, brand-aligned animations for loading states. Ensure they are announced clearly to screen readers, for example, by associating them with an `aria-live="polite"` region containing visually hidden text like "Loading content...".
    *   Colors for spinners should contrast well with their background in all themes. Could use `var(--tk-purple-500)` or `var(--tk-violet-400)` in Light/Dark themes.
*   **Skeleton Screens:** For complex content areas or cards, using skeleton screens (simplified placeholder layouts that mimic the structure of the content before it loads) can significantly improve perceived performance and reduce user frustration during loading.
*   **Progress Bars:** For operations that take a noticeable amount of time (e.g., file uploads, multi-step processes), progress bars should clearly indicate the progress.
    *   Use `role="progressbar"`, `aria-valuenow`, `aria-valuemin="0"`, `aria-valuemax="100"`.
    *   The filled portion of the bar should use a contrasting brand color (e.g., `var(--tk-purple-500)` or `var(--tk-green-500)` for task completion).

#### 9.5. `prefers-reduced-motion` Implementation
It is **critical** to respect user preferences for reduced motion to prevent discomfort, distraction, or potential health issues (e.g., for users with vestibular disorders or photosensitivity) (WCAG SC 2.2.2 Pause, Stop, Hide; SC 2.3.3 Animation from Interactions).
*   **CSS Media Query:** Implement system-wide or component-specific overrides.
    ```css
    @media (prefers-reduced-motion: reduce) {
      /* Global reset for transitions and animations */
      *,
      *::before,
      *::after {
        animation-duration: 0.01ms !important; /* Effectively freeze keyframe animations */
        animation-iteration-count: 1 !important; /* Run only once if not frozen */
        transition-duration: 0.01ms !important; /* Make transitions instant */
        scroll-behavior: auto !important; /* Disable smooth scrolling */
      }

      /* Specific component overrides */
      .depth-float {
        /* Example: a continuously floating element */
        animation: none;
      }
      .animate-on-scroll {
        /* Ensure elements are visible without animation */
        opacity: 1;
        transform: translateY(0);
      }
      .depth-button:hover,
      .depth-card:hover,
      .depth-logo-item:hover,
      .depth-feature:hover {
        transform: none; /* Disable hover transforms that cause movement */
        /* Note: Box-shadow changes on hover might still be acceptable if not too distracting */
      }

      /* Example for a conceptual pulsing alert animation */
      .alert--pulsing-animation {
        /* Replace pulsing with a static state or a very subtle, non-jarring effect */
        animation: none;
        /* Or: animation: subtle-opacity-change 2s infinite alternate; */
      }
    }

    /* Example of a more acceptable subtle animation for reduced motion if needed */
    /* @keyframes subtle-opacity-change {
      from { opacity: 1; }
      to { opacity: 0.85; }
    } */
    ```
*   **JavaScript-Controlled Animations:** If animations are triggered or controlled by JavaScript, the script should check the media query result before applying motion:
    ```javascript
    const motionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    function handleAnimation() {
      if (!motionQuery.matches) {
        // Apply full animation
        element.classList.add('full-animation-effect');
      } else {
        // Apply reduced or no animation
        element.classList.add('reduced-animation-effect');
      }
    }
    // Call handleAnimation() when needed
    // Listen for changes if the user changes their OS preference while on the page
    motionQuery.addEventListener('change', handleAnimation);
    ```
*   **Flashing Content (WCAG SC 2.3.1 Three Flashes or Below Threshold):** Web pages must not contain anything that flashes more than three times in any one-second period, or the flash must be below the general flash and red flash thresholds. This is critical to prevent seizures. All animations, especially those involving color changes or rapid blinking, must adhere to this.

---

### 10. Theming: Light, Dark, & Night Modes

The TechnoKids design system offers robust theming capabilities to cater to diverse user preferences, varying ambient lighting conditions, and specific accessibility needs, thereby enhancing usability and comfort. This section recaps the theming strategy and emphasizes considerations for maintaining accessibility and brand consistency across themes.

#### 10.1. Theming Strategy & User Choice
*   **Light Mode (Default `[data-theme="light"]`):** This is the primary and most vibrant theme, optimized for well-lit environments. It features light backgrounds (e.g., `var(--bg-page): --tk-gray-50;`, `var(--bg-content): --tk-white;`) with dark text (`var(--text-primary): --tk-gray-900;`), allowing brand colors (Purples, Indigos, Violets, Pinks) to stand out as accents and for key interactive elements.
*   **Dark Mode (`[data-theme="dark"]`):** Designed to reduce eye strain in low-light conditions, especially during prolonged use. It employs dark backgrounds (e.g., `var(--bg-page): --tk-gray-900;`, `var(--bg-content): --tk-gray-800;`) with light text (`var(--text-primary): --tk-white;`). Brand colors remain vibrant but are carefully balanced against dark surfaces.
    *   **"Dark Purple Driven" Variant (within Dark Mode context):** As detailed in Section 4.4, designers can achieve a "dark purple driven" aesthetic by overriding core background tokens like `--bg-page`, `--bg-content`, and `--bg-card` with dark purple/indigo shades (e.g., from the 800-950 range: `var(--tk-purple-950)`, `var(--tk-indigo-900)`). This provides a richer brand expression within the dark theme paradigm. All text and non-text elements must maintain high contrast against these specific dark brand backgrounds.
*   **Night Mode (`[data-theme="night"]`):** A specialized dark theme utilizing a distinct, specially curated warmer, and desaturated color palette (defined in Section 4.4). It's designed to offer an alternative comfortable viewing experience for extended night-time use, further minimizing perceived blue light exposure and providing a softer visual experience compared to the standard Dark Mode.
*   **User Control & System Preference:**
    *   Ideally, users should be provided with a clear mechanism (e.g., a settings toggle) to select their preferred theme.
    *   The system should also respect OS-level theme preferences (`prefers-color-scheme: dark` or `prefers-color-scheme: light`) as an initial default if no user preference is explicitly saved via `localStorage`.

#### 10.2. Implementation via `data-theme`
Themes are applied by setting a `data-theme` attribute on the `<html>` element. JavaScript is used to manage theme switching and persistence.
```javascript
// Example JavaScript for theme switching and persistence
function applyTechnoKidsTheme(themeName) {
  document.documentElement.setAttribute('data-theme', themeName);
  localStorage.setItem('technoKidsPreferredTheme', themeName);
}

function initializeTechnoKidsTheme() {
  const savedTheme = localStorage.getItem('technoKidsPreferredTheme');
  const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
  const systemPrefersLight = window.matchMedia('(prefers-color-scheme: light)').matches;

  if (savedTheme) {
    applyTechnoKidsTheme(savedTheme);
  } else if (systemPrefersDark) {
    applyTechnoKidsTheme('dark'); // Default to dark if system prefers it and no user choice saved
  } else if (systemPrefersLight) {
    applyTechnoKidsTheme('light'); // Default to light if system prefers it
  } else {
    applyTechnoKidsTheme('light'); // Fallback default
  }
}

// Call initializeTechnoKidsTheme() on page load
// UI controls (e.g., buttons, dropdown) would call applyTechnoKidsTheme('light'/'dark'/'night')
```

#### 10.3. Specific Variable Overrides for Each Mode
The comprehensive CSS Custom Property blocks for `[data-theme='dark']` and `[data-theme='night']` in Section 4.4 detail all the specific token overrides. These include adjustments for:
*   Background colors (`--bg-page`, `--bg-content`, `--bg-card`, `--bg-input`, `--bg-subtle`, etc.)
*   Text colors (`--text-primary`, `--text-secondary`, `--text-subtle`, `--text-link`, etc.)
*   Border colors (`--border-standard`, `--border-input`, `--border-focus-ring-color`, etc.)
*   Semantic state colors (backgrounds and text for success, error, warning, info)
*   Shadow intensity and color (`--depth-shadow-*` variables)
*   Specific component state colors (e.g., `--button-disabled-bg-color`)

#### 10.4. Accessibility & Contrast in Each Theme
**Crucially, all themes must independently meet WCAG 2.1 AA contrast requirements for text and non-text elements.** This cannot be overstated.
*   **Dark Mode & "Dark Purple Driven" Challenges:**
    *   **Shadows:** Traditional dark shadows are less effective on dark backgrounds. Depth in dark themes is often conveyed by slightly lighter surface colors for elevated elements, subtle borders, or carefully tuned, more opaque shadows. The shadow variables are adjusted in dark themes to reflect this.
    *   **Color "Glow" / Halation:** Highly saturated brand colors (especially lighter tints of purples, violets, or pinks if used as text or icons) might appear to "glow" or cause halation (visual spreading) on very dark backgrounds, potentially impairing readability. The chosen text colors (`--text-on-dark-bg`, various light grays, and lighter brand tints like `--tk-purple-300`) are selected to mitigate this while maintaining brand character.
    *   **Input Borders (Non-Text Contrast):** Achieving 3:1 contrast for default state input borders against *both* their immediate input background *and* the overall page/content background can be challenging in highly chromatic dark themes (like deep purple on deep purple). This guide prioritizes:
        1.  A clear visual distinction of the input field itself (e.g., its background color differs from the page).
        2.  An **exceptionally clear and high-contrast focus state** (`:focus-visible`) for the input border.
        3.  If default state borders are subtle, hover states might provide an intermediate visual cue.
*   **Night Mode Considerations:**
    *   Uses a distinct, warmer, and desaturated palette. All contrast ratios are re-evaluated and defined specifically for these unique color combinations to ensure readability and comfort.
*   **Universal Testing:** Each theme **must be individually tested** for all accessibility criteria, including color contrast for text and non-text elements, focus visibility, and readability. Assumptions from Light Mode behavior do not automatically transfer to Dark or Night modes. Design and QA processes must include theme-specific accessibility validation.

---

### 11. Accessibility Compliance & Best Practices (WCAG Focused)

Accessibility is not an optional feature but a core requirement for all TechnoKids digital products. This design system is built with the Web Content Accessibility Guidelines (WCAG) 2.1 Level AA as the minimum standard, aiming for Level AAA where feasible without compromising core design goals or usability for other groups.

#### 11.1. Commitment to Inclusivity (POUR Principles)
Our design and development practices are fundamentally guided by WCAG's four foundational principles:
*   **Perceivable:** Information and user interface components must be presentable to users in ways they can perceive. This includes providing text alternatives for non-text content, ensuring content is distinguishable (e.g., color contrast), and making content adaptable to different presentations.
*   **Operable:** User interface components and navigation must be operable. This covers keyboard accessibility, providing users enough time to read and use content, avoiding content that can cause seizures, and ensuring content is easily navigable.
*   **Understandable:** Information and the operation of the user interface must be understandable. This involves making text readable and understandable, making web pages appear and operate in predictable ways, and helping users avoid and correct mistakes.
*   **Robust:** Content must be robust enough that it can be interpreted reliably by a wide variety of user agents, including current and future assistive technologies. This involves using valid code and ensuring custom components have correct names, roles, and values.

#### 11.2. Color Contrast (WCAG SC 1.4.3, 1.4.6 AAA, 1.4.11)
Adherence to these contrast ratios is critical for users with low vision or color vision deficiencies.
*   **SC 1.4.3 Contrast (Minimum) - AA (Required):**
    *   Normal Text (<18pt or <14pt bold): **4.5:1** against its background.
    *   Large Text (≥18pt or ≥14pt bold): **3:1** against its background.
    *   *All text color tokens (`--text-primary`, `--text-secondary`, etc.) and semantic text colors (`--semantic-*-color-text`) are defined and verified to meet these ratios on their intended default backgrounds (`--bg-content`, `--semantic-*-color-bg`, input backgrounds, button backgrounds) across all themes (Light, Dark, Night). Specific contrast ratios are noted in Section 4 (CSS Variables) and Section 6.4 (Typography).*
*   **SC 1.4.6 Contrast (Enhanced) - AAA (Strive For):**
    *   Normal Text: **7:1**. Large Text: **4.5:1**.
    *   *While AA is the minimum, for primary content text (e.g., body paragraphs, main headings), this guide strives for AAA where brand aesthetics and thematic goals allow. For instance, `--text-primary` on `--bg-content` in Light and Dark themes often meets or exceeds AAA.*
*   **SC 1.4.11 Non-Text Contrast - AA (Required):**
    *   User interface components (e.g., input field borders, checkbox/radio button boundaries, toggle switch tracks/knobs that indicate state) and graphical objects (e.g., informational icons whose meaning is tied to their visual appearance) require a **3:1** contrast ratio against their adjacent background color(s).
    *   *This is particularly critical for:*
        *   *Focus indicators (see Section 11.4).*
        *   *Default state borders of input fields (e.g., `var(--border-input)` must contrast 3:1 with `var(--bg-input)`).*
        *   *Boundaries of custom controls like checkboxes, radios, and toggles.*
        *   *Active state indicators for tabs or navigation items if their boundary is the primary visual cue.*
    *   *Component specifications in Section 7.3 detail how this is achieved for each component across themes.*

#### 11.3. Use of Color (WCAG SC 1.4.1)
*   Color is **never used as the sole means** of conveying information, indicating an action, prompting a response, or distinguishing a visual element. Redundant cues are always provided.
*   **Examples within this Guide:**
    *   Links are not only colored but also receive an underline on hover/focus (and optionally by default for inline links).
    *   Error states for forms use explicit error messages and icons in addition to red coloration for borders or text.
    *   Required fields are indicated with an asterisk (`*`) and potentially visually hidden text like "(required)" in addition to any color cues.
    *   Active navigation items use visual cues beyond color, such as a contrasting border, background change, or font weight change.
    *   Status messages (success, warning, error, info) use distinct icons alongside their semantic coloring.

#### 11.4. Keyboard Navigation & Focus Visibility (WCAG SC 2.1.1, 2.4.3, 2.4.7)
*   **SC 2.1.1 Keyboard:** All functionality of the content is operable through a keyboard interface without requiring specific timings for individual keystrokes. There must be no "keyboard traps" where focus can move into a component but cannot be moved out using only the keyboard.
*   **SC 2.4.3 Focus Order:** If a web page can be navigated sequentially and the navigation sequences affect meaning or operation, focusable components receive focus in an order that preserves meaning and operability. This typically means a logical DOM order.
*   **SC 2.4.7 Focus Visible (Critical):** Any keyboard operable user interface has a mode of operation where the keyboard focus indicator is highly visible.
    *   **TechnoKids Standard:** A clearly defined focus style using `outline: 2px solid var(--border-focus-ring-color); outline-offset: 2px;` is applied via the `:focus-visible` pseudo-class to all interactive elements.
    *   The `var(--border-focus-ring-color)` is theme-dependent (e.g., `--tk-purple-500` in Light, `--tk-purple-400` in Dark, a warm purple in Night) and is chosen to ensure at least a **3:1 contrast ratio** against both the component's background and its immediate surrounding background.
    *   See Section 7.2.3 and individual component styles in Section 7.3 for specific focus state definitions.

#### 11.5. Content Structure & Readability (WCAG SC 1.3.1, 2.4.6, 3.1.2)
*   **SC 1.3.1 Info and Relationships:** Semantic HTML (headings `<h1>-<h6>` in logical order, lists `<ul>, <ol>, <li>`, tables `<table>, <th>, <td>, scope`, landmarks `<main>, <nav>, <aside>`) is used correctly to convey the structure and relationships within content, making it understandable for assistive technologies.
*   **SC 2.4.6 Headings and Labels:** Headings and labels describe the topic or purpose of the content they are associated with. Labels are programmatically associated with form controls.
*   **SC 3.1.2 Language of Parts:** The human language of any content that differs from the page's default language is programmatically identifiable (e.g., using `lang` attribute on a `<span>` or `<div>`).
*   *Typography guidelines (Section 6) and Layout/Spacing guidelines (Section 5) directly support readability through appropriate font choices, sizes, line heights, clear visual hierarchy, and sufficient white space.*

#### 11.6. `prefers-reduced-motion` (WCAG SC 2.2.2 Pause, Stop, Hide; SC 2.3.3 Animation from Interactions)
*   User preferences for reduced motion are respected to prevent discomfort, distraction, or potential health issues (e.g., for users with vestibular disorders or photosensitivity). Non-essential animations are disabled or significantly reduced.
*   *See Section 9.5 for CSS implementation examples.*

#### 11.7. Semantic HTML & ARIA (WCAG SC 4.1.1 Parsing, 4.1.2 Name, Role, Value)
*   **SC 4.1.1 Parsing:** Content implemented using markup languages has complete start and end tags, elements are nested according to their specifications, elements do not contain duplicate attributes, and any IDs are unique. Code is validated against HTML/CSS standards.
*   **SC 4.1.2 Name, Role, Value:** For all user interface components (including form elements, links, and custom components generated by scripts), their name and role are programmatically determinable; states, properties, and values that can be set by the user are programmatically settable; and notification of changes to these items is available to user agents, including assistive technologies.
    *   Native HTML elements are used correctly and preferred where possible.
    *   For custom components (e.g., custom toggles, modals, tabs, dropdowns), appropriate ARIA roles, states, and properties are applied as per WAI-ARIA Authoring Practices Guide (APG). Component specifications in Section 7.3 include relevant ARIA guidance.

#### 11.8. Performance Considerations
*   **Optimized Assets:** Use optimized images (correct format, compression, responsive sizes via `<picture>` or `srcset`).
*   **Efficient CSS:** Leverage CSS Custom Properties for theming and maintainability, minimize selector overrides, and avoid CSS that causes excessive reflows/repaints.
*   **Font Loading:** Implement strategies like `font-display: swap;` and preloading for critical web fonts to improve perceived performance and content availability (see Section 6.5).
*   **Code Minification:** Minify HTML, CSS, and JavaScript files for production environments.

#### 11.9. Tools and Testing Methodology
A combination of automated tools and manual testing is essential for comprehensive accessibility validation:
*   **Automated Contrast Checkers:** WebAIM Contrast Checker, browser developer tools (e.g., Chrome DevTools color picker, Firefox Accessibility Inspector), Axe DevTools.
*   **Automated General Accessibility Checkers:** Axe DevTools browser extension, WAVE Evaluation Tool (for quick scans and structure checks). These tools help identify common issues but cannot catch all problems.
*   **Manual Testing (Indispensable):**
    *   **Keyboard-Only Navigation:** Test all interactive elements and workflows using only the keyboard (Tab, Shift+Tab, Enter, Space, Arrow keys, Escape). Verify logical focus order and visible focus indicators.
    *   **Screen Reader Testing:** Test with major screen readers (e.g., NVDA for Windows, VoiceOver for macOS/iOS, JAWS if available) to ensure content is announced correctly, roles and states are clear, and interactive elements are operable.
    *   **Zoom/Magnification Testing:** Test interface usability when zoomed up to 400% (WCAG SC 1.4.4 Resize Text, SC 1.4.10 Reflow).
    *   **Reduced Motion Simulation:** Test with `prefers-reduced-motion` enabled in OS or browser settings.
*   **Code Validators:** W3C Markup Validation Service (for HTML) and W3C CSS Validation Service (for CSS) to ensure code robustness.

---

### 12. Conclusion & Governance

#### 12.1. Summary of Guide's Importance
The "TechnoKids Color Palette Guide v16" is a critical asset for creating high-quality, consistent, accessible, and brand-aligned digital experiences. It provides a comprehensive framework that empowers designers, developers, and content creators to work efficiently and cohesively. By adhering to this guide, we ensure that TechnoKids products are not only visually engaging, especially with the enhanced capacity for "dark purple driven" themes and richer accents like Violet, but also usable and inclusive for our entire diverse audience of learners and educators. This guide is foundational to achieving design excellence and reinforcing the TechnoKids brand.

#### 12.2. Design System Governance and Maintenance
This guide is a living document, intended to evolve with our products and best practices. To ensure its continued relevance, accuracy, and effectiveness, a clear governance model is essential:
*   **Ownership & Stewardship:** A designated team or individual(s) (e.g., Design System Lead, UI/UX Core Team) is responsible for maintaining, updating, and evangelizing the guide.
*   **Change Management Process:** A clearly defined process for proposing, reviewing, testing (with a strong emphasis on accessibility impact and cross-theme consistency), and approving any changes or additions to the palette, tokens, or component styles. This process should involve stakeholders from design, development, product, and accessibility.
*   **Regular Audits & Reviews:** Periodic audits (e.g., quarterly or semi-annually, and with major product releases or redesigns) should be conducted against live products to ensure ongoing compliance with this guide and to identify areas for improvement or new patterns that need documentation.
*   **Documentation Updates:** All approved changes must be promptly and clearly documented within this guide. Version control (as indicated by "v16") will be maintained for this document.
*   **Communication & Training:** Updates to the guide must be clearly communicated to all relevant teams. New team members should be thoroughly onboarded with this guide, and periodic refreshers or workshops can help maintain a shared understanding and consistent application.

#### 12.3. Contribution and Evolution Process
The evolution of this design system guide should be a collaborative effort:
*   **Feedback Channels:** Establish accessible and well-monitored channels (e.g., a dedicated Slack channel, regular design critiques, a suggestion box/form) for all team members to provide feedback, report inconsistencies or issues found in practice, or suggest improvements to the guide.
*   **Collaborative Review & Decision Making:** Significant updates or additions should be reviewed by a cross-functional group to ensure they meet design standards, technical feasibility, accessibility requirements, and product goals.
*   **Pilot Testing:** For major changes, consider pilot testing with a specific product or feature before rolling out system-wide.
*   **Iterative Refinement:** Embrace an iterative approach. The design system is not static; it will grow and adapt based on new requirements, user feedback, evolving accessibility standards, and emerging design trends that align with TechnoKids' brand and user needs.

By fostering a collaborative approach to the maintenance and evolution of this Color Palette Guide, and by rigorously applying its principles, TechnoKids can ensure that its design system remains a powerful and effective tool for innovation, quality, and inclusivity. This commitment will ultimately lead to more professional, effective, and delightful digital experiences for all learners and educators who interact with TechnoKids products.

---

This completes the full, self-contained "TechnoKids Color Palette Guide v16" across the three requested phases. It's a very substantial document, aiming to be as comprehensive and actionable as possible based on our iterative discussions.

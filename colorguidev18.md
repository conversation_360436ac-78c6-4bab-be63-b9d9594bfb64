# The TechnoKids Comprehensive Color System & Thematic Design Guide v18

**Version:** 18.0
**Date:** May 24, 2025

**Document Purpose:** This guide presents the definitive and comprehensive reference for all colors, gradients, styles, typographic color applications, interactive state color definitions, and 3D depth effects employed across TechnoKids user interfaces. This guide places a **strong emphasis on facilitating a "dark purple driven" aesthetic and thematic versatility**, integrating a full systematic Violet scale to enrich the accent palette. It is built upon established TechnoKids brand colors, infusing them with modern, dynamic, and visually engaging design principles. This document also incorporates broader insights into color psychology, particularly for violet and purple hues, and references general best practices for color management and palette development. It is intended to ensure unwavering visual consistency, elevate user engagement, safeguard accessibility, and streamline the design and development workflow for all TechnoKids digital products. This version includes a comprehensive range of commonly used UI components, detailed according to the TechnoKids design system.

---

**Table of Contents:**

1.  **Introduction & Guiding Design Philosophy**
    *   1.1. Goal: A Unified, Engaging, Versatile, and Accessible Visual Language
    *   1.2. The Allure and Psychology of Violet & Purple
    *   1.3. Core Principles (with emphasis on Thematic Versatility)
    *   1.4. How to Use This Guide (Highlighting Thematic Application)

2.  **Core Color Palette (Foundation & Expansion)**
    *   2.1. Color Theory Fundamentals for Violet/Purple Palettes
    *   2.2. Primary Brand Colors: Purple & Indigo (with Systematic Variations)
    *   2.3. Key Accent Color Families: Violet & Pink (with Systematic Variations)
    *   2.4. Secondary Accent & Action Colors
    *   2.5. Neutral Colors (with Systematic Variations for Hierarchy & Contrast)
    *   2.6. Semantic / Status Colors (Clearly Defined for User Feedback)
    *   2.7. Opacity Variants & Their Application
    *   2.8. Inspirational Violet/Purple Palette Themes (External Context)

3.  **Dynamic Color: Gradients**
    *   3.1. Gradient Philosophy & Usage (Leveraging Purple, Indigo, Violet)
    *   3.2. Predefined Gradient Definitions (with new Violet combinations)
    *   3.3. Accessibility Considerations for Text on Gradients

4.  **CSS Custom Properties (Design Tokens - The Engine)**
    *   4.1. Tokenization Strategy
    *   4.2. Core & Semantic Color Definitions (Including full Violet scale)
    *   4.3. General UI Component Variables
    *   4.4. Theme-Specific Variables (Light, Dark, Night - with "Dark Purple Driven" options)
    *   4.5. 3D Depth & Shadow Variables
    *   4.6. Typography & Spacing Scale Variables
    *   4.7. Implementing Tokens in Content Management Systems & Builders

5.  **Layout, Spacing, & Composition**
    *   5.1. Principles of Visual Organization
    *   5.2. Grid System (Conceptual)
    *   5.3. Spacing Scale & Vertical Rhythm
    *   5.4. White Space (Negative Space) Strategy
    *   5.5. Container & Section Defaults

6.  **Typography System: Clarity, Hierarchy, and Readability**
    *   6.1. Typographic Philosophy
    *   6.2. Font Families, Weights, Line Heights
    *   6.3. Responsive Typographic Scale (with `rem` and `clamp()`)
    *   6.4. Text Color Application & WCAG Contrast
        *   6.4.1. Primary, Secondary, and Tertiary Text Colors
        *   6.4.2. Link Text Colors & States
        *   6.4.3. Text on Colored/Gradient Backgrounds
        *   6.4.4. Placeholder Text
    *   6.5. Font Loading & Performance Considerations

7.  **Application to UI Components & Interactive States (WCAG & Usability Focused)**
    *   7.1. General Principles for Component Color Application
    *   7.2. Definition of Standard Component States & Visual Feedback
        *   7.2.1. Default State
        *   7.2.2. Hover State
        *   7.2.3. Focus State (`:focus-visible` - Critical for Accessibility)
        *   7.2.4. Active/Pressed State
        *   7.2.5. Disabled State (Perceivability & `aria-disabled`)
        *   7.2.6. Error/Validation State
    *   7.3. Specific Component Color & State Guidance (with CSS Examples)
        *   7.3.1. Accordions / Collapsible Sections
        *   7.3.2. Alerts & Notifications (Semantic States)
        *   7.3.3. Avatars
        *   7.3.4. Badges / Tags
        *   7.3.5. Breadcrumbs
        *   7.3.6. Buttons (Primary, Secondary, Accent, Outline, Disabled)
        *   7.3.7. Cards (`.depth-card`) & Content Containers
        *   7.3.8. Checkboxes & Radio Buttons
        *   7.3.9. Data Tables (Core Styling Principles)
        *   7.3.10. Date Pickers (Conceptual Styling Principles)
        *   7.3.11. File Uploaders (Basic Styling Principles)
        *   7.3.12. Forms & Input Fields (Text, Select)
        *   7.3.13. Icons (Informational vs. Decorative)
        *   7.3.14. Modals & Dialogs (Focus Trapping Considerations)
        *   7.3.15. Navigation (Main Menus, Side Navigation)
        *   7.3.16. Pagination Controls
        *   7.3.17. Progress Bars & Spinners (Loaders)
        *   7.3.18. Sliders / Range Inputs
        *   7.3.19. Tabs
        *   7.3.20. Toggle Buttons (`.depth-toggle`)
        *   7.3.21. Toolbars
        *   7.3.22. Tooltips
        *   7.3.23. TechnoKids Specific: Session & Thematic Elements
        *   7.3.24. TechnoKids Specific: Logo & Feature Items
    *   7.4. *Conceptual Components (Principles for Future Implementation - e.g., Carousels)*

8.  **Depth, Shadows, & Layering Effects**
    *   8.1. Strategic Use of Depth for Hierarchy
    *   8.2. Standard & Interactive Shadows
    *   8.3. Decorative Background Elements (Blobs, Shapes)
    *   8.4. Overlapping Elements

9.  **Animations, Micro-interactions, & Motion**
    *   9.1. Purposeful Motion
    *   9.2. Hover Effects & Transforms
    *   9.3. Scroll-Triggered Animations (Considerations)
    *   9.4. Loading Indicators & UI Feedback (General Principles)
    *   9.5. `prefers-reduced-motion` Implementation

10. **Theming: Light, Dark, & Night Modes**
    *   10.1. Theming Strategy & User Choice
    *   10.2. Implementation via `data-theme`
    *   10.3. Specific Variable Overrides for Each Mode
    *   10.4. Accessibility & Contrast in Each Theme

11. **Accessibility Compliance & Best Practices (WCAG Focused)**
    *   11.1. Commitment to Inclusivity (POUR Principles)
    *   11.2. Color Contrast (WCAG SC 1.4.3, 1.4.6 AAA, 1.4.11)
    *   11.3. Use of Color (WCAG SC 1.4.1)
    *   11.4. Keyboard Navigation & Focus Visibility (WCAG SC 2.1.1, 2.4.7)
    *   11.5. Content Structure & Readability (WCAG SC 1.3.1, 3.1)
    *   11.6. `prefers-reduced-motion` (WCAG SC 2.2.2, 2.3.3)
    *   11.7. Semantic HTML & ARIA (WCAG SC 4.1.1, 4.1.2)
    *   11.8. Performance Considerations
    *   11.9. Tools and Testing Methodology

12. **Further Inspiration & External Tools for Palette Development**
    *   12.1. Color Palette Generators
    *   12.2. Inspiration Platforms
    *   12.3. Browser Extensions for Color Picking

13. **Conclusion & Governance**
    *   13.1. Summary of Guide's Importance
    *   13.2. Design System Governance and Maintenance
    *   13.3. Contribution and Evolution Process

---

### 1. Introduction & Guiding Design Philosophy

#### 1.1. Goal: A Unified, Engaging, Versatile, and Accessible Visual Language
This "TechnoKids Comprehensive Color System & Thematic Design Guide" (Version 18.0) aims to establish a comprehensive, consistent, accessible, and **thematically versatile** visual language for all TechnoKids digital products. A key focus is to **explicitly support and demonstrate how to achieve diverse aesthetics, including a rich "dark purple driven" experience**, alongside the default Light theme and other variations. This is achieved through an expanded accent palette, including a full systematic Violet scale, and clearer guidance on leveraging the darker shades of our core brand colors. This guide serves as the single source of truth for color application, typographic color standards, and interactive visual feedback.

#### 1.2. The Allure and Psychology of Violet & Purple
Violet and purple hues possess a unique and compelling presence. Their effective use hinges on understanding their psychological impact. Colors are not merely visual stimuli; they carry inherent associations and can evoke specific emotional responses.

Purple, a secondary color formed by blending the energy of red with the calmness of blue, embodies a complex range of meanings. Historically, its rarity and the expense of producing purple dyes linked it with royalty, luxury, wealth, and power. This association with prestige and sophistication persists, making it a strong choice for brands aiming to project an image of exclusivity or high quality.

Beyond opulence, purple is frequently connected with creativity, imagination, wisdom, and spirituality. Lighter shades like lavender and lilac can evoke calmness, peace, and tranquility, making them suitable for wellness or meditation-related designs. Conversely, deeper and more vibrant violets can stimulate and inspire, aligning well with innovative or artistic endeavors. In contemporary contexts, purple has also become a symbol of nonconformity and individuality.

However, the interpretation of purple can also carry negative connotations. Overuse or certain shades might be perceived as arrogant, excessive, artificial, or even associated with mourning in some cultures. Therefore, context and balance are crucial. Understanding these psychological nuances allows designers to strategically employ violet and purple to reinforce brand messaging and guide user perception effectively, which is a core consideration within the TechnoKids brand expression.

#### 1.3. Core Principles (with emphasis on Thematic Versatility)
These principles serve as the north star for all design decisions within the TechnoKids ecosystem:

*   **1.3.1. User-Centricity (Focus on Learners & Educators):** Our primary users are children, young adults, and educators. Color choices and applications must be tailored to their cognitive abilities, learning contexts, and interaction patterns. The design should feel inviting, engaging, and supportive of the learning journey, minimizing cognitive load and maximizing comprehension. All visual elements must contribute to a seamless and intuitive user experience.
*   **1.3.2. Brand Alignment & Emotional Resonance:** The core Purple and Indigo colors remain central to the TechnoKids brand. This guide empowers designers to express the brand with varying intensity and mood, from bright and inviting to deep and focused, using the full spectrum of provided shades. Every color choice should be intentional and reflect our brand values of creativity, wisdom, and engagement, as supported by the psychological impact of these hues.
*   **1.3.3. Clarity & Readability:** Regardless of the chosen theme (light, dark purple, or night), all visual information, especially text, must be exceptionally clear, legible, and easily perceivable. This principle guides typographic choices, text color selections, contrast ratios, and the structuring of content. Visual hierarchy must be unambiguous to guide users effectively.
*   **1.3.4. Accessibility & Inclusivity (WCAG 2.1 AA as Baseline):** We are committed to creating digital experiences usable by people with the widest possible range of abilities. This guide mandates adherence to Web Content Accessibility Guidelines (WCAG) 2.1 Level AA success criteria as a minimum standard for all color contrast, non-text contrast, and other related visual design aspects. Design choices should actively promote inclusivity.
*   **1.3.5. Consistency & Predictability:** Uniform application of colors, typography, and interactive patterns across all digital touchpoints is critical. This creates a cohesive user experience, reduces confusion, and helps users understand how to interact with our products intuitively. Standards defined herein must be applied rigorously.
*   **1.3.6. Modernity, Engagement, & **Thematic Versatility**:** The design system provides the tools (expanded palettes, gradients, depth effects, theming) to create interfaces that are modern, dynamic, and engaging. This guide specifically enhances the ability to craft UIs that range from predominantly light to deeply saturated with brand purples and violets, offering true aesthetic flexibility.

#### 1.4. How to Use This Guide (Highlighting Thematic Application)
This guide is intended for designers, developers, content creators, and QA teams involved in building TechnoKids digital products. It provides:
*   **Definitive Color Palettes:** Core Purple/Indigo, expanded Violet/Pink accents, secondary accents, neutrals, and semantic colors, all with systematic variations. Each color variation is provided with a clear rationale for its existence (e.g., achieving accessible contrast, providing UI state options).
*   **CSS Design Tokens:** A comprehensive list of variables (`--tk-` prefix) for direct implementation in stylesheets, ensuring consistency between design and code.
*   **Typographic Guidelines:** Including specific text color applications, line heights, and font weights designed for optimal readability and accessibility across various backgrounds and themes.
*   **Component Color Specifications:** Detailed color usage for key UI components and their interactive states (default, hover, focus, active, disabled, error), with explicit WCAG compliance notes.
*   **Theming Recipes (New Emphasis):** Guidance and examples (expanded in component sections) on how to achieve specific aesthetics, such as a "Dark Purple Driven" theme, by leveraging the defined color roles and dark shades.
*   **Accessibility Mandates:** Integrated throughout, not as an afterthought, but as foundational criteria.

Adherence to this guide is crucial for maintaining quality, consistency, and inclusivity across all TechnoKids offerings. Deviations should be discussed and approved through the established design system governance process. For achieving specific thematic goals (e.g., a "dark purple driven" interface), pay close attention to the usage notes for darker brand shades (800-950 levels) and the theme override sections.

---

### 2. Core Color Palette (Foundation & Expansion)

This section details the official TechnoKids color palette. It features a **full systematic Violet scale** as a key accent family alongside Pink, and reinforces the usage of darker Purple and Indigo shades for thematic depth. Each primary, brand, and key accent color is presented with a systematic scale (typically 50-950) to provide a flexible yet consistent range of shades and tints. These variations are essential for creating visual hierarchy, defining UI states, ensuring accessible contrast, and maintaining brand integrity across diverse applications.

#### 2.1. Color Theory Fundamentals for Violet/Purple Palettes
A foundational understanding of color theory is essential for constructing harmonious and effective violet or purple-based palettes. Key concepts include:

*   **Primary Colors:** Red, yellow, and blue are the foundational colors from which all other colors are derived.
*   **Secondary Colors:** These are created by mixing two primary colors. Purple (red + blue), green (blue + yellow), and orange (red + yellow) are the secondary colors.
*   **Tertiary Colors:** Formed by mixing a primary color with an adjacent secondary color (e.g., red-purple, blue-green).
*   **Tints, Shades, and Tones:**
    *   **Tints:** Created by adding white to a pure color, resulting in lighter, less intense versions (e.g., lavender is a tint of purple). The "50" to "400" values in the TechnoKids palette often represent tints.
    *   **Shades:** Created by adding black to a pure color, resulting in darker versions (e.g., deep plum is a shade of purple). The "600" to "950" values in the TechnoKids palette often represent shades.
    *   **Tones:** Created by adding both black and white (grey) to a pure color, resulting in more subdued or muted versions.
    These variations are crucial for developing depth and hierarchy within a violet/purple color scheme. A palette rarely consists of only pure, saturated colors; tints, shades, and tones provide the necessary range for backgrounds, text, accents, and interactive elements.

*   **Building Harmony: Color Relationships with Violet/Purple**
    Effective color palettes rely on harmonious relationships between colors. Common color harmony models applicable to violet/purple schemes include:
    *   **Monochromatic:** Uses various tints, shades, and tones of a single purple hue. The TechnoKids Purple scale itself is an example of a monochromatic system.
    *   **Analogous:** Combines purple with colors adjacent to it on the color wheel (e.g., blue-purple and red-purple, or purple with blues and pinks). The TechnoKids palette uses Purple, Indigo (a blue-purple), and Pink (a red-purple) in an analogous relationship.
    *   **Complementary:** Pairs purple with its direct opposite on the color wheel, which is yellow. Gold is often cited as an excellent complement to purple, adding luxury. The TechnoKids palette includes Yellows, which can act as complements.
    *   **Triadic:** Uses three colors evenly spaced around the color wheel. For purple, this could involve green and orange. The TechnoKids palette includes Greens and Oranges.
    *   When working with violet and purple, pairing them with neutrals like white, gray, or beige (as seen in the TechnoKids Neutral palette) can help balance their intensity and improve approachability. Teal is also suggested as a good complementary shade that brings balance and calm, and is included in the TechnoKids accents.

**Rationale for Systematic Variations (TechnoKids Palette):** The provision of a full spectrum of shades (darker variants) and tints (lighter variants) for each core color allows designers to:
*   Create depth and elevation in UI elements.
*   Define visually distinct and accessible interactive states (hover, focus, active, disabled).
*   Ensure text has sufficient contrast against various colored backgrounds.
*   Offer subtle variations for borders, backgrounds, and iconography without introducing off-brand colors.

#### 2.2. Primary Brand Colors: Purple & Indigo (with Systematic Variations)
These remain the cornerstone of the TechnoKids brand identity. Their darker shades are crucial for achieving a "dark purple driven" aesthetic.

| Name             | Hex Code  | Variable Name       | Usage Notes & Accessibility Rationale                                   |
| :--------------- | :-------- | :------------------ | :-------------------------------------------------------------------- |
| **Purple**       |           |                     | **Primary Brand Color Family.** Darker shades (700-950) are ideal for dominant dark theme backgrounds. |
| Purple 50        | `#f5f3ff` | `--tk-purple-50`    | Lightest tint for subtle effects, hover states.    |
| Purple 100       | `#ede9fe` | `--tk-purple-100`   | Light backgrounds, active items.                                      |
| Purple 200       | `#ddd6fe` | `--tk-purple-200`   | Borders, subtle accents.                                              |
| Purple 300       | `#c4b5fd` | `--tk-purple-300`   | Dark mode text, highlights (e.g., link hover on dark purple BG).        |
| Purple 400       | `#a78bfa` | `--tk-purple-400`   | Dark mode links/icons, medium accents.                               |
| Purple 500       | `#8b5cf6` | `--tk-purple-500`   | **Primary Brand Color**. CTAs, Headers, key interactive elements in Light Theme. |
| Purple 600       | `#7c3aed` | `--tk-purple-600`   | Stronger accents, hover states for Primary 500, link text (Light Theme). |
| Purple 700       | `#6d28d9` | `--tk-purple-700`   | Darker elements, text on light purple, key for purple-driven dark themes. |
| Purple 800       | `#5b21b6` | `--tk-purple-800`   | **Key for Dominant Dark Theme Backgrounds**, deep accents.             |
| Purple 900       | `#4c1d95` | `--tk-purple-900`   | **Deepest Dark Theme Backgrounds**, high contrast elements.            |
| Purple 950       | `#2e1065` | `--tk-purple-950`   | Near-black purple for extreme depth or fine details in dark themes.    |
| **Indigo**       |           |                     | **Secondary Brand Color Family.** Complements Purple, especially in dark/purple-driven themes. |
| Indigo 50        | `#eef2ff` | `--tk-indigo-50`    | Lightest tint, subtle backgrounds.                                    |
| Indigo 100       | `#e0e7ff` | `--tk-indigo-100`   | Light backgrounds.                                                    |
| Indigo 200       | `#c7d2fe` | `--tk-indigo-200`   | Borders, subtle accents.                                              |
| Indigo 300       | `#a5b4fc` | `--tk-indigo-300`   | Dark mode text, highlights.                                           |
| Indigo 400       | `#818cf8` | `--tk-indigo-400`   | Dark mode links/icons.                                               |
| Indigo 500       | `#6366f1` | `--tk-indigo-500`   | **Secondary Brand Color**. Gradients, Buttons in Light Theme.          |
| Indigo 600       | `#4f46e5` | `--tk-indigo-600`   | Stronger accents, gradients.                                          |
| Indigo 700       | `#4338ca` | `--tk-indigo-700`   | Darker elements, key for purple/indigo driven themes.                 |
| Indigo 800       | `#3730a3` | `--tk-indigo-800`   | **Key for Dominant Dark Theme Backgrounds (with Purple)**, deep accents. |
| Indigo 900       | `#312e81` | `--tk-indigo-900`   | **Deepest Dark Theme Backgrounds (with Purple)**.                      |
| Indigo 950       | `#1e1b4b` | `--tk-indigo-950`   | Near-black indigo for extreme depth or layered dark backgrounds.       |

#### 2.3. Key Accent Color Families: Violet & Pink (with Systematic Variations)
To enhance thematic versatility and provide richer accent options, **Violet is a fully scaled accent family.** Pink remains a key warm accent.

| Name             | Hex Code  | Variable Name       | Usage Notes & Accessibility Rationale                                     |
| :--------------- | :-------- | :------------------ | :---------------------------------------------------------------------- |
| **Violet**       |           |                     | **Key Accent Family.** Provides a distinct purple hue often seen in modern designs for highlights, calls-to-action, or thematic elements. |
| Violet 50        | `#f5f3ff` | `--tk-violet-50`    | Lightest violet tint.                                                  |
| Violet 100       | `#ede9fe` | `--tk-violet-100`   | Light violet backgrounds.                                               |
| Violet 200       | `#ddd6fe` | `--tk-violet-200`   | Subtle violet accents, borders.                                         |
| Violet 300       | `#c4b5fd` | `--tk-violet-300`   | Lighter violet accents, dark mode text on darker violet backgrounds.      |
| Violet 400       | `#a78bfa` | `--tk-violet-400`   | **Primary Violet Accent.** Often used for icons, highlights, secondary CTAs. |
| Violet 500       | `#8b5cf6` | `--tk-violet-500`   | Stronger violet accent. Visually similar to Purple 500 but semantically "Violet". |
| Violet 600       | `#7c3aed` | `--tk-violet-600`   | Deeper violet accent.                                                   |
| Violet 700       | `#6d28d9` | `--tk-violet-700`   | Dark violet for text on light violet backgrounds, or dark theme accents. |
| Violet 800       | `#5b21b6` | `--tk-violet-800`   | Deep violet accent, can be used for subtle dark theme violet backgrounds. |
| Violet 900       | `#4c1d95` | `--tk-violet-900`   | Darkest violet accent.                                                  |
| Violet 950       | `#2e1065` | `--tk-violet-950`   | Near-black violet.                                                      |
| **Pink**         |           |                     | **Key Warm Accent Family.** Provides warmth and contrast, especially against cooler purples/indigos. |
| Pink 50          | `#fdf2f8` | `--tk-pink-50`      | Lightest pink tint, subtle highlights.                                  |
| Pink 100         | `#fce7f3` | `--tk-pink-100`     | Light pink backgrounds, session backgrounds.                            |
| Pink 200         | `#fbcfe8` | `--tk-pink-200`     | Assignment backgrounds.                                                 |
| Pink 300         | `#f9a8d4` | `--tk-pink-300`     | Dark mode text on darker pinks, lighter pink accents.                   |
| Pink 400         | `#f472b6` | `--tk-pink-400`     | Key pink actions, highlights, gradients.                                |
| Pink 500         | `#ec4899` | `--tk-pink-500`     | **Primary Pink Accent Color**. Used for CTAs, highlights, gradients.      |
| Pink 600         | `#db2777` | `--tk-pink-600`     | Stronger pink accents, button hover states.                             |
| Pink 700         | `#be185d` | `--tk-pink-700`     | Darker pink elements, text on light pink backgrounds.                   |
| Pink 800         | `#9d174d` | `--tk-pink-800`     | Deep pink accents, assignment backgrounds (Dark).                       |
| Pink 900         | `#831843` | `--tk-pink-900`     | Darkest pink, session backgrounds (Dark).                               |

#### 2.4. Secondary Accent & Action Colors
These provide additional breadth for specific UI needs, such as iconography, informational cues, or tertiary actions.

| Name             | Hex Code  | Variable Name       | Usage Notes                                                           |
| :--------------- | :-------- | :------------------ | :-------------------------------------------------------------------- |
| **Rose**         | `#f43f5e` | `--tk-rose-500`     | Secondary accents, gradients, pairs with Pink for warmth.             |
| Rose 600         | `#e11d48` | `--tk-rose-600`     | Stronger rose accents, hover states.                                  |
| **Teal**         | `#14b8a6` | `--tk-teal-500`     | Tertiary accents, informational icons, provides cool contrast.        |
| Teal 600         | `#0d9488` | `--tk-teal-600`     | Darker teal for text or icons needing more contrast on light BGs.     |
| Teal 700         | `#0f766e` | `--tk-teal-700`     | Deeper teal for dark theme accents or text.                            |
| **Cyan**         | `#06b6d4` | `--tk-cyan-500`     | Informational highlights, gradients with Blue.                        |
| Cyan 600         | `#0891b2` | `--tk-cyan-600`     | Stronger cyan accents, button hover states.                           |
| **Sky**          | `#0ea5e9` | `--tk-sky-500`      | UI elements, icons, brighter blue accents.                             |
| **Orange**       | `#f97316` | `--tk-orange-500`   | Secondary warning indicators, icons, attention-grabbing highlights.   |
| **Amber**        | `#f59e0b` | `--tk-amber-500`    | Icons, gradients, tertiary attention highlights.                      |
| **Yellow**       |           |                     | Notes, Highlights, **Warning Semantic Family Base**                     |
| Yellow 50        | `#fffbeb` | `--tk-yellow-50`    | Lightest yellow, subtle hover BGs.                                    |
| Yellow 100       | `#fef3c7` | `--tk-yellow-100`   | **Semantic Warning Background (Light Theme)**.                         |
| Yellow 300       | `#fde047` | `--tk-yellow-300`   | **Semantic Warning Border (Light Theme)**, Dark mode text.             |
| Yellow 500       | `#eab308` | `--tk-yellow-500`   | **Semantic Warning Base Color**, icons, highlights.                    |
| Yellow 700       | `#a16207` | `--tk-yellow-700`   | **Semantic Warning Text (Light Theme)**.                               |
| **Green**        |           |                     | **Success Semantic Family Base**, Positive Feedback                   |
| Green 50         | `#f0fdf4` | `--tk-green-50`     | Lightest green, subtle hover BGs.                                     |
| Green 100        | `#dcfce7` | `--tk-green-100`    | **Semantic Success Background (Light Theme)**.                         |
| Green 300        | `#86efac` | `--tk-green-300`    | **Semantic Success Border (Light Theme)**, Dark mode text.             |
| Green 400        | `#4ade80` | `--tk-green-400`    | Success states, gradients.                                            |
| Green 500        | `#22c55e` | `--tk-green-500`    | **Semantic Success Base Color**, main success indicators.              |
| Green 700        | `#15803d` | `--tk-green-700`    | **Semantic Success Text (Light Theme)**.                               |
| Green 800        | `#166534` | `--tk-green-800`    | Darker green for text needing high contrast on light green BGs.        |
| **Emerald**      | `#10b981` | `--tk-emerald-500`  | Alternative success states, distinct positive indicators.             |
| **Blue**         |           |                     | **Informational Semantic Family Base**, Links, UI Elements            |
| Blue 50          | `#eff6ff` | `--tk-blue-50`      | Lightest blue, subtle BGs.                                            |
| Blue 100         | `#dbeafe` | `--tk-blue-100`     | **Semantic Info Background (Light Theme)**.                            |
| Blue 300         | `#93c5fd` | `--tk-blue-300`     | **Semantic Info Border (Light Theme)**, Dark mode text.                |
| Blue 400         | `#60a5fa` | `--tk-blue-400`     | Informational elements, links, gradients.                             |
| Blue 500         | `#3b82f6` | `--tk-blue-500`     | **Semantic Info Base Color**, primary informational elements.          |
| Blue 600         | `#2563eb` | `--tk-blue-600`     | Stronger blue accents, button hover states.                           |
| Blue 700         | `#1d4ed8` | `--tk-blue-700`     | **Semantic Info Text (Light Theme)**.                                  |
| Blue 800         | `#1e40af` | `--tk-blue-800`     | Dark blue accents.                                                    |
| Blue 900         | `#1e3a8a` | `--tk-blue-900`     | Deepest blue accents.                                                 |

#### 2.5. Neutral Colors (with Systematic Variations for Hierarchy & Contrast)
Neutrals are the backbone of the UI, providing backgrounds, text colors, and defining structure. Their systematic variation is crucial for creating hierarchy and ensuring readability in all themes.

| Name             | Hex Code  | Variable Name      | Usage Notes & Accessibility Rationale                                       |
| :--------------- | :-------- | :----------------- | :------------------------------------------------------------------------ |
| **Gray**         |           |                    | Primary Neutral Color Family                                              |
| White            | `#FFFFFF` | `--tk-white`       | Primary backgrounds (Light Theme), text on dark backgrounds. Max contrast. |
| Gray 50          | `#fafafa` | `--tk-gray-50`     | Lightest page backgrounds (Light Theme), off-white alternative.           |
| Gray 100         | `#f3f4f6` | `--tk-gray-100`    | Subtle section backgrounds, card backgrounds (Light), disabled BGs (Light). |
| Gray 200         | `#e5e7eb` | `--tk-gray-200`    | Borders, dividers, disabled element borders (Light).                        |
| Gray 300         | `#d1d5db` | `--tk-gray-300`    | Input borders (Light), secondary borders, text (Dark on Dark BG).           |
| Gray 400         | `#9ca3af` | `--tk-gray-400`    | Placeholder text, subtle icons, disabled/secondary text (Dark).             |
| Gray 500         | `#6b7280` | `--tk-gray-500`    | Tertiary/Subtle text (Light), icons, default disabled text.                |
| Gray 600         | `#4b5563` | `--tk-gray-600`    | Secondary text (Light), input borders (Dark), stronger disabled text.      |
| Gray 700         | `#374151` | `--tk-gray-700`    | Primary body text (Light Theme on light BGs), input BGs (Dark).           |
| Gray 800         | `#1f2937` | `--tk-gray-800`    | Dark backgrounds, content areas (Dark Theme), card BGs (Dark Theme).        |
| Gray 900         | `#111827` | `--tk-gray-900`    | Deepest backgrounds, page background (Dark Theme), footers (Dark Theme).    |
| Black            | `#000000` | `--tk-black`       | Limited use for text requiring maximum contrast on very light BGs.        |

#### 2.6. Semantic / Status Colors (Clearly Defined for User Feedback)
These colors convey specific meanings (success, warning, error, information). They **must always be accompanied by icons and/or unambiguous text** to ensure accessibility (WCAG SC 1.4.1 Use of Color).

| Status / Feedback | Base Color Var        | Text Color Var         | Background Color Var  | Border Color Var       | Icon Usage Note                    | WCAG Contrast (Text on BG - Light Theme) |
| :---------------- | :-------------------- | :--------------------- | :-------------------- | :------------------- | :--------------------------------- | :--------------------------------------- |
| **Success**       | `--semantic-success-base` (`--tk-green-500`) | `--semantic-success-text` (`--tk-green-700`) | `--semantic-success-bg` (`--tk-green-100`) | `--semantic-success-border` (`--tk-green-300`) | Use with checkmark icon             | **9.65:1 (AAA Pass)**                    |
| **Warning**       | `--semantic-warning-base` (`--tk-yellow-500`) | `--semantic-warning-text` (`--tk-yellow-700`) | `--semantic-warning-bg` (`--tk-yellow-100`) | `--semantic-warning-border` (`--tk-yellow-300`) | Use with warning triangle icon      | **7.07:1 (AAA Pass)**                    |
| **Error/Danger**  | `--semantic-error-base` (`--tk-red-600`)   | `--semantic-error-text` (`--tk-red-700`)   | `--semantic-error-bg` (`--tk-red-100`)   | `--semantic-error-border` (`--tk-red-300`)   | Use with error/cross icon         | **6.03:1 (AA Pass)**                     |
| **Information**   | `--semantic-info-base` (`--tk-blue-500`)  | `--semantic-info-text` (`--tk-blue-700`)   | `--semantic-info-bg` (`--tk-blue-100`)   | `--semantic-info-border` (`--tk-blue-300`)   | Use with information "i" icon     | **7.08:1 (AAA Pass)**                    |

*Note: Dark Mode and Night Mode variants for semantic backgrounds and texts are defined within their respective `[data-theme]` CSS blocks (Section 4.4) to ensure appropriate contrast in those themes.*

#### 2.7. Opacity Variants & Their Application
Opacity is applied contextually using RGBA color definitions or the CSS `opacity` property, rather than through global opacity tokens. This allows for precise control and minimizes the risk of unpredictable contrast accessibility issues.

*   **Modal Overlays:** `background-color: var(--bg-overlay);` (which is `rgba(0, 0, 0, 0.4)` in Light Theme, and adjusted for Dark/Night themes). The opacity is integral to the color definition, ensuring the overlay appropriately dims background content while maintaining perceivability.
*   **Decorative Elements:** Low opacity values (e.g., `0.1` to `0.3`) can be applied to decorative background blobs or patterns, such as those using `var(--decorative-blob-color-1)` or `var(--decorative-blob-gradient)`, to ensure they remain subtle and do not interfere with foreground content.
*   **Disabled States:** This guide primarily uses distinct, solid colors for disabled states (e.g., `var(--button-disabled-bg)`, `var(--button-disabled-text)`) to ensure better perceivability and text contrast, rather than relying solely on reduced opacity of the active state colors. If `opacity` is exceptionally used for a disabled state (e.g., `opacity: 0.7;` on a pre-styled element that cannot have its direct colors changed), the resulting contrast of any text or meaningful graphical elements against its effective background **must be re-checked and meet WCAG requirements if the element needs to be perceivable**.
*   **Image Overlays/Text Protection:** For text placed over images, a semi-transparent scrim (e.g., `background-color: rgba(var(--tk-black-rgb), 0.5);`) can be used to ensure text readability. The opacity chosen must guarantee sufficient contrast for the text.
*   **Caution:** Applying opacity to a parent element affects all its child elements. If an element contains text or meaningful icons, reducing its container's opacity can drastically lower the contrast of these children against their true background (which becomes a blend of their color and the colors visible through the semi-transparent parent). **Always test the final rendered contrast of critical elements if opacity is applied to their containers.**

#### 2.8. Inspirational Violet/Purple Palette Themes (External Context)
While the TechnoKids palette is definitive for its products, understanding how violet and purple are used in broader design contexts can provide inspiration. These are *not* for direct use in TechnoKids products but illustrate thematic possibilities:

*   **Lavender Dream:** Combines soft lavenders (`#E6E6FA`, `#D8BFD8`) with peach (`#FFDAB9`) and soft greens/grays for a calm, dreamy feel.
*   **Royal Majesty:** Pairs deep purples (`#5E2A8C`, `#702963`) with gold (`#B59643`) and majestic green/gray for an opulent, luxurious mood.
*   **Mystical Twilight:** Blends dark slate blue/purple (`#483D8B`) with lighter lilacs (`#E9E7F2`, `#C39BD3`) and cool grays for a mysterious, magical ambiance.
*   **Enchanted Garden:** Uses orchid/blue-violet (`#DDA0DD`, `#8A2BE2`) with pale blue, khaki, and thistle for a serene, elegant, and natural feel.
*   **Whimsical Dreams:** Features deep purple (`#9B59B6`), rich orchid (`#BA55D3`), soft lavender blush, vibrant coral, and lively lime green for a dynamic and playful sophistication.

These examples showcase the versatility of purple and violet in creating diverse emotional responses and aesthetic styles, reinforcing the thoughtful selection behind the TechnoKids brand and accent colors.

---

### 3. Dynamic Color: Gradients

Gradients are employed strategically to add visual depth, energy, and reinforce the TechnoKids brand identity, aligning with modern aesthetics. They are particularly effective for prominent elements like headers, primary buttons, hero sections, and thematic highlights.

#### 3.1. Gradient Philosophy & Usage (Leveraging Purple, Indigo, Violet)
*   **Visual Appeal & Branding:** Gradients using core brand colors (`--tk-purple-*`, `--tk-indigo-*`) and key accents like `--tk-violet-*` create a dynamic and recognizable brand expression.
*   **Hierarchy & Emphasis:** Gradients can draw the user's eye and give prominence to important calls to action or feature sections.
*   **Thematic Expression:** Different gradient combinations can contribute to the overall mood of a theme, e.g., deep, rich gradients for a "dark purple driven" feel, or lighter, airy gradients for the Light Theme.
*   **Subtlety & Readability:** Gradients should be smooth and generally subtle enough not to overpower content. If text is placed upon a gradient, readability is paramount (see Section 3.3).
*   **Consistency:** Predefined gradients are provided as CSS custom properties to ensure consistent application across the UI.

#### 3.2. Predefined Gradient Definitions (with new Violet combinations)
These tokens provide ready-to-use gradients. Additional custom gradients can be created using the palette tokens as needed, following similar principles.

*   **Core Brand Gradients:**
    *   `--primary-brand-gradient`: `linear-gradient(to right, var(--tk-purple-500), var(--tk-indigo-500))`
        *   *Usage: Main headers, primary buttons (Light Theme), primary feature item backgrounds.*
    *   `--secondary-brand-gradient`: `linear-gradient(to right, var(--tk-blue-500), var(--tk-cyan-500))`
        *   *Usage: Secondary buttons, informational banners, illustrative backgrounds.*
*   **Accent Gradients:**
    *   `--accent-brand-gradient`: `linear-gradient(to right, var(--tk-pink-500), var(--tk-rose-500))`
        *   *Usage: Accent call-to-action buttons, highlight sections requiring strong warm visual emphasis.*
    *   `--violet-accent-gradient`: `linear-gradient(to right, var(--tk-purple-600), var(--tk-violet-400))`
        *   *Usage: Alternative accent CTAs, feature highlights, blending primary Purple with key Violet accent.*
    *   `--violet-pink-accent-gradient`: `linear-gradient(to right, var(--tk-violet-500), var(--tk-pink-400))`
        *   *Usage: Vibrant accents, decorative elements, potentially for modern flourishes.*
*   **Dark & Thematic Gradients:**
    *   `--section-bg-dark-gradient`: `linear-gradient(135deg, var(--tk-purple-800), var(--tk-indigo-900))`
        *   *Usage: Backgrounds for dark-themed hero sections or distinct content blocks in any theme to create a focal point.*
    *   `--dark-purple-hero-gradient`: `linear-gradient(135deg, var(--tk-purple-950), var(--tk-indigo-800), var(--tk-purple-700))`
        *   *Usage: Specifically for creating a rich, deep "dark purple driven" hero or section background.*
*   **Session & Thematic Element Gradients (Light Theme Base):**
    *   `--session-1-num-bg-gradient`: `linear-gradient(to right, var(--tk-purple-500), var(--tk-indigo-600))`
    *   `--session-2-num-bg-gradient`: `linear-gradient(to right, var(--tk-blue-400), var(--tk-cyan-500))`
    *   `--session-3-num-bg-gradient`: `linear-gradient(to right, var(--tk-pink-400), var(--tk-rose-500))`
    *   `--assignment-card-gradient-light`: `linear-gradient(to bottom right, var(--tk-purple-50), var(--tk-indigo-50))`
    *   `--extension-card-gradient-light`: `linear-gradient(to bottom right, var(--tk-blue-50), var(--tk-cyan-50))`
*   **Utility & Depth Gradients:**
    *   `--decorative-blob-gradient`: `radial-gradient(circle, var(--decorative-blob-color-1), var(--decorative-blob-color-2))`
        *   *Usage: Subtle background decorative elements, often with reduced opacity and blur.*
    *   `--depth-gradient-overlay`: `linear-gradient(to bottom, var(--depth-gradient-top), transparent 50%, var(--depth-gradient-bottom))`
        *   *Usage: Applied on top of solid or gradient backgrounds for subtle 3D lighting effects on buttons and features.*

*Note: Dark Mode and Night Mode versions of thematic gradients (e.g., assignment cards, session number backgrounds) are defined or adapted within their respective `[data-theme]` CSS blocks (Section 4.4) using darker shades or theme-specific accent colors.*

#### 3.3. Accessibility Considerations for Text on Gradients
Ensuring text readability on gradient backgrounds is critical for accessibility and overall usability. This requires careful attention to color choices and contrast ratios across the entire area where text might appear.

*   **WCAG SC 1.4.3 Contrast (Minimum) Adherence:**
    *   Normal-sized text (typically < 18pt or < 14pt bold) requires a contrast ratio of at least **4.5:1** against all parts of the gradient it overlaps.
    *   Large text (typically ≥ 18pt or ≥ 14pt bold) requires a contrast ratio of at least **3:1** against all parts of the gradient it overlaps.
*   **Comprehensive Testing Procedure:**
    1.  **Identify Text Placement:** Determine the exact area where text will be rendered over the gradient.
    2.  **Sample Gradient Colors:** Using a reliable color picker tool (e.g., browser developer tools, dedicated color pickers), sample the gradient's background color at multiple points directly beneath the text. This should include:
        *   The start, middle, and end of lines of text.
        *   The top, middle, and bottom of blocks of text.
        *   Crucially, the points where the gradient is **lightest** under the text and where it is **darkest** under the text.
    3.  **Calculate Contrast Ratios:** For each sampled background color, calculate its contrast ratio against the chosen text color (e.g., `var(--text-on-dark-bg)` which is `--tk-white`).
    4.  **Verify Minimum Compliance:** The **lowest contrast ratio found** across all sampled points must meet the WCAG minimum requirements (4.5:1 or 3:1 as appropriate for the text size).
*   **Recommended Text Color for TechnoKids Gradients:**
    *   For most predefined brand gradients (which tend to be mid-to-dark tones), `var(--text-on-dark-bg)` (i.e., `var(--tk-white)`) is the recommended text color.
    *   **Example Verification for `--primary-brand-gradient` (`--tk-purple-500` to `--tk-indigo-500`) with `var(--tk-white)` text:**
        *   `#FFFFFF` on `#8b5cf6` (Purple 500): **4.53:1 (AA Pass for normal text)**.
        *   `#FFFFFF` on `#6366f1` (Indigo 500): **5.07:1 (AA Pass for normal text)**.
        *   Midpoint (approx `#7761F3`): **4.78:1 (AA Pass for normal text)**.
    *   This demonstrates that white text works well on this primary gradient. Similar checks must be done for all text-on-gradient applications.
*   **Strategies for Ensuring Contrast on Challenging Gradients:**
    1.  **Select Appropriate Text Color:** If a gradient has very light areas, white text may fail. Conversely, if it has very dark areas, black text might fail. Choose the text color that provides the best overall contrast across the gradient span.
    2.  **Gradient Design:** Favor gradients where the color transition is not excessively wide or does not include colors that are too chromatically similar to the intended text color.
    3.  **Text Shadow (Use with Extreme Caution):** A *very subtle*, well-placed text shadow can slightly improve edge definition but is **NOT a substitute for meeting base contrast requirements.**
    4.  **Background Scrim (Most Robust Solution):** For critical text on highly variable backgrounds, applying a semi-transparent solid color layer ("scrim") directly behind the text area is the most reliable method.
        ```css
        .text-container-on-gradient::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(var(--tk-black-rgb), 0.4);
          z-index: 0;
        }
        .text-on-gradient-with-scrim {
          position: relative;
          z-index: 1;
        }
        ```
*   **Avoid Text Over Extremely Busy Gradients:** If a gradient has numerous sharp color changes or its overall color range is too close to the desired text color, it is generally unsuitable as a direct background for important textual information.

---

### 4. CSS Custom Properties (Design Tokens - The Engine)

This section details the complete set of CSS Custom Properties (Design Tokens) that form the foundation of the TechnoKids visual design system. These tokens encapsulate all core design decisions, ensuring consistency, maintainability, and themability.

#### 4.1. Tokenization Strategy
All foundational design decisions—colors, typography, spacing, shadows—are encapsulated as CSS Custom Properties. These properties act as **design tokens**, providing a single source of truth and a shared language between design and development.

**Key Benefits:**
*   **Consistency:** Ensures uniform application of design attributes.
*   **Maintainability:** Global changes can be made by updating a single token.
*   **Theming:** Facilitates easy switching between Light, Dark, and Night modes.
*   **Scalability:** Simplifies the extension of the design system.
*   **Collaboration:** Provides a clear reference for designers and developers.

**Naming Conventions:**
*   **Palette Tokens:** ` --tk-[colorName]-[shade]` (e.g., `--tk-purple-500`).
*   **Semantic Role Tokens (Alias Tokens):** ` --[role]-[property]` (e.g., `--bg-page`, `--text-primary`).
*   **Functional Tokens:** ` --depth-[type]`, ` --space-[scale]`.

#### 4.2. Core & Semantic Color Definitions (Global Base for Light Theme)
This comprehensive `:root` block defines all palette tokens and the initial (Light Theme) definitions for semantic role tokens.

```css
:root {
  /* === CORE BRAND & ACCENT COLORS === */
  --tk-purple-50: #f5f3ff;
  --tk-purple-100: #ede9fe;
  --tk-purple-200: #ddd6fe;
  --tk-purple-300: #c4b5fd;
  --tk-purple-400: #a78bfa;
  --tk-purple-500: #8b5cf6;
  --tk-purple-600: #7c3aed;
  --tk-purple-700: #6d28d9;
  --tk-purple-800: #5b21b6;
  --tk-purple-900: #4c1d95;
  --tk-purple-950: #2e1065;
  --tk-indigo-50: #eef2ff;
  --tk-indigo-100: #e0e7ff;
  --tk-indigo-200: #c7d2fe;
  --tk-indigo-300: #a5b4fc;
  --tk-indigo-400: #818cf8;
  --tk-indigo-500: #6366f1;
  --tk-indigo-600: #4f46e5;
  --tk-indigo-700: #4338ca;
  --tk-indigo-800: #3730a3;
  --tk-indigo-900: #312e81;
  --tk-indigo-950: #1e1b4b;
  --tk-violet-50: #f5f3ff;
  --tk-violet-100: #ede9fe;
  --tk-violet-200: #ddd6fe;
  --tk-violet-300: #c4b5fd;
  --tk-violet-400: #a78bfa;
  --tk-violet-500: #8b5cf6;
  --tk-violet-600: #7c3aed;
  --tk-violet-700: #6d28d9;
  --tk-violet-800: #5b21b6;
  --tk-violet-900: #4c1d95;
  --tk-violet-950: #2e1065;
  --tk-pink-50: #fdf2f8;
  --tk-pink-100: #fce7f3;
  --tk-pink-200: #fbcfe8;
  --tk-pink-300: #f9a8d4;
  --tk-pink-400: #f472b6;
  --tk-pink-500: #ec4899;
  --tk-pink-600: #db2777;
  --tk-pink-700: #be185d;
  --tk-pink-800: #9d174d;
  --tk-pink-900: #831843;
  --tk-rose-500: #f43f5e;
  --tk-rose-600: #e11d48;
  --tk-teal-500: #14b8a6;
  --tk-teal-600: #0d9488;
  --tk-teal-700: #0f766e;
  --tk-cyan-50: #ecfeff;
  --tk-cyan-500: #06b6d4;
  --tk-cyan-600: #0891b2;
  --tk-sky-500: #0ea5e9;
  --tk-orange-500: #f97316;
  --tk-amber-500: #f59e0b;
  --tk-yellow-50: #fffbeb;
  --tk-yellow-100: #fef3c7;
  --tk-yellow-300: #fde047;
  --tk-yellow-500: #eab308;
  --tk-yellow-700: #a16207;
  --tk-green-50: #f0fdf4;
  --tk-green-100: #dcfce7;
  --tk-green-300: #86efac;
  --tk-green-400: #4ade80;
  --tk-green-500: #22c55e;
  --tk-green-700: #15803d;
  --tk-green-800: #166534;
  --tk-emerald-500: #10b981;
  --tk-blue-50: #eff6ff;
  --tk-blue-100: #dbeafe;
  --tk-blue-300: #93c5fd;
  --tk-blue-400: #60a5fa;
  --tk-blue-500: #3b82f6;
  --tk-blue-600: #2563eb;
  --tk-blue-700: #1d4ed8;
  --tk-blue-800: #1e40af;
  --tk-blue-900: #1e3a8a;
  --tk-white: #ffffff;
  --tk-black: #000000;
  --tk-white-rgb: 255, 255, 255;
  --tk-black-rgb: 0, 0, 0;
  --tk-gray-50: #fafafa;
  --tk-gray-100: #f3f4f6;
  --tk-gray-200: #e5e7eb;
  --tk-gray-300: #d1d5db;
  --tk-gray-400: #9ca3af;
  --tk-gray-500: #6b7280;
  --tk-gray-600: #4b5563;
  --tk-gray-700: #374151;
  --tk-gray-800: #1f2937;
  --tk-gray-900: #111827;
  --tk-red-100: #fee2e2;
  --tk-red-300: #fca5a5;
  --tk-red-600: #dc2626;
  --tk-red-700: #b91c1c;
  --tk-green-300-rgb: 134, 239, 172;
  --tk-red-300-rgb: 252, 165, 165;
  --tk-yellow-300-rgb: 253, 224, 71;
  --tk-blue-300-rgb: 147, 197, 253;
  --tk-green-800-rgb: 22, 101, 52;
  --tk-red-700-rgb: 185, 28, 28;
  --tk-yellow-700-rgb: 161, 98, 7;
  --tk-blue-800-rgb: 30, 64, 175;

  /* === GRADIENT DEFINITIONS === */
  --gradient-primary-brand: linear-gradient(to right, var(--tk-purple-500), var(--tk-indigo-500));
  --gradient-secondary-brand: linear-gradient(to right, var(--tk-blue-500), var(--tk-cyan-500));
  --gradient-accent-brand: linear-gradient(to right, var(--tk-pink-500), var(--tk-rose-500));
  --gradient-violet-accent: linear-gradient(to right, var(--tk-purple-600), var(--tk-violet-400));
  --gradient-violet-pink-accent: linear-gradient(to right, var(--tk-violet-500), var(--tk-pink-400));
  --gradient-dark-purple-hero: linear-gradient(
    135deg,
    var(--tk-purple-950),
    var(--tk-indigo-800),
    var(--tk-purple-700)
  );
  --gradient-session-1-num-bg: linear-gradient(to right, var(--tk-purple-500), var(--tk-indigo-600));
  --gradient-session-2-num-bg: linear-gradient(to right, var(--tk-blue-400), var(--tk-cyan-500));
  --gradient-session-3-num-bg: linear-gradient(to right, var(--tk-pink-400), var(--tk-rose-500));
  --gradient-assignment-card-light: linear-gradient(to bottom right, var(--tk-purple-50), var(--tk-indigo-50));
  --gradient-extension-card-light: linear-gradient(to bottom right, var(--tk-blue-50), var(--tk-cyan-50));
  --gradient-section-bg-dark: linear-gradient(135deg, var(--tk-purple-800), var(--tk-indigo-900));
  --gradient-decorative-blob: radial-gradient(circle, var(--decorative-blob-color-1), var(--decorative-blob-color-2));
  --overlay-depth-gradient-top: rgba(255, 255, 255, 0.1);
  --overlay-depth-gradient-bottom: rgba(0, 0, 0, 0.05);
  --overlay-depth-gradient: linear-gradient(
    to bottom,
    var(--overlay-depth-gradient-top),
    transparent 50%,
    var(--overlay-depth-gradient-bottom)
  );

  /* === GENERAL UI COMPONENT & ROLE-BASED TOKENS (Light Theme Defaults) === */
  --bg-page: var(--tk-gray-50);
  --bg-content: var(--tk-white);
  --bg-card: var(--tk-white);
  --bg-sidebar: var(--tk-white);
  --bg-input: var(--tk-white);
  --bg-subtle: var(--tk-gray-100);
  --bg-hover-active: var(--tk-purple-100);
  --bg-overlay: rgba(var(--tk-black-rgb), 0.4);
  --bg-hero-dark-purple: var(--gradient-dark-purple-hero);
  --text-primary: var(--tk-gray-900);
  --text-secondary: var(--tk-gray-700);
  --text-subtle: var(--tk-gray-500);
  --text-on-dark-bg: var(--tk-white);
  --text-on-color: var(--tk-white);
  --text-link: var(--tk-purple-600);
  --text-link-hover: var(--tk-purple-700);
  --text-heading-brand: var(--tk-purple-700);
  --text-placeholder: var(--tk-gray-500);
  --text-disabled: var(--tk-gray-600);
  --border-standard: var(--tk-gray-200);
  --border-input: var(--tk-gray-400);
  --border-divider: var(--tk-gray-200);
  --border-focus-ring-color: var(--tk-purple-500);
  --border-sidebar: var(--tk-purple-200);
  --border-interactive-strong: var(--tk-purple-500);
  --semantic-success-color-text: var(--tk-green-700);
  --semantic-success-color-bg: var(--tk-green-100);
  --semantic-success-color-border: var(--tk-green-300);
  --semantic-success-color-icon: var(--tk-green-500);
  --semantic-error-color-text: var(--tk-red-700);
  --semantic-error-color-bg: var(--tk-red-100);
  --semantic-error-color-border: var(--tk-red-300);
  --semantic-error-color-icon: var(--tk-red-600);
  --semantic-warning-color-text: var(--tk-yellow-700);
  --semantic-warning-color-bg: var(--tk-yellow-100);
  --semantic-warning-color-border: var(--tk-yellow-300);
  --semantic-warning-color-icon: var(--tk-yellow-500);
  --semantic-info-color-text: var(--tk-blue-700);
  --semantic-info-color-bg: var(--tk-blue-100);
  --semantic-info-color-border: var(--tk-blue-300);
  --semantic-info-color-icon: var(--tk-blue-500);
  --button-primary-bg-image: var(--gradient-primary-brand);
  --button-primary-color-text: var(--text-on-dark-bg);
  --button-primary-hover-bg-image: linear-gradient(to right, var(--tk-purple-600), var(--tk-indigo-600));
  --button-secondary-bg-image: var(--gradient-secondary-brand);
  --button-secondary-color-text: var(--text-on-dark-bg);
  --button-secondary-hover-bg-image: linear-gradient(to right, var(--tk-blue-600), var(--tk-cyan-600));
  --button-accent-bg-image: var(--gradient-accent-brand);
  --button-accent-color-text: var(--tk-black);
  --button-accent-hover-bg-image: linear-gradient(to right, var(--tk-pink-600), var(--tk-rose-600));
  --button-outline-color-border: var(--tk-purple-500);
  --button-outline-color-text: var(--tk-purple-600);
  --button-outline-hover-bg-color: var(--tk-purple-50);
  --button-outline-hover-color-text: var(--tk-purple-700);
  --button-disabled-bg-color: var(--tk-gray-100);
  --button-disabled-color-text: var(--text-disabled);
  --button-disabled-color-border: var(--tk-gray-200);
  --input-border-radius: 8px;
  --input-border-color-focus: var(--tk-purple-500);
  --input-focus-shadow-ring: 0 0 0 3px var(--border-focus-ring-color);
  --input-bg-disabled: var(--tk-gray-100);
  --input-border-color-disabled: var(--tk-gray-200);
  --input-color-text-disabled: var(--text-disabled);
  --card-padding: var(--space-lg);
  --card-border-radius: 8px;
  --modal-bg-color: var(--bg-card);
  --decorative-blob-color-start: rgba(var(--tk-purple-500-rgb), 0.15);
  --decorative-blob-color-end: rgba(
    var(--tk-cyan-500-rgb),
    0.15
  ); /* Requires --tk-purple-500-rgb & --tk-cyan-500-rgb */

  /* === 3D DEPTH & SHADOW TOKENS === */
  --depth-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05), 0 1px 1px rgba(0, 0, 0, 0.1);
  --depth-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --depth-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --depth-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --depth-shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --depth-shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  --depth-transform-hover: translateY(-2px);
  --depth-transform-active: translateY(1px);
  --depth-border-width: 1px;
  --depth-border-color-highlight: rgba(var(--tk-white-rgb), 0.1);
  --depth-border-color-shadow: rgba(var(--tk-black-rgb), 0.1);

  /* === TYPOGRAPHY SCALE TOKENS === */
  --font-family-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif,
    'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; /* ACTION REQUIRED: Define TechnoKids Brand Font */
  --font-family-body: var(--font-family-sans);
  --font-family-headings: var(--font-family-sans);
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-black: 900;
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --line-height-loose: 1.8;
  --font-size-base: 16px;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-md: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-h6: 1rem;
  --font-size-h5: 1.25rem;
  --font-size-h4: 1.5rem;
  --font-size-h3: clamp(1.5rem, 3vw + 1rem, 2.25rem);
  --font-size-h2: clamp(1.75rem, 4vw + 1rem, 3rem);
  --font-size-h1: clamp(2.25rem, 5vw + 1rem, 3.75rem);
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0em;

  /* === SPACING SCALE TOKENS === */
  --space-unit: 8px;
  --space-xs: calc(0.5 * var(--space-unit));
  --space-sm: var(--space-unit);
  --space-md: calc(2 * var(--space-unit));
  --space-lg: calc(3 * var(--space-unit));
  --space-xl: calc(4 * var(--space-unit));
  --space-2xl: calc(6 * var(--space-unit));
  --space-3xl: calc(8 * var(--space-unit));
  --space-section-padding-y: var(--space-3xl);

  /* === TRANSITION TOKENS === */
  --transition-speed-fast: 0.2s;
  --transition-speed-normal: 0.3s;
  --transition-timing-function: ease-in-out;
  --transition-default: all var(--transition-speed-normal) var(--transition-timing-function);
  --transition-fast: all var(--transition-speed-fast) var(--transition-timing-function);
}
```

#### 4.4. Theme-Specific Variables (Light, Dark, Night - with "Dark Purple Driven" options)
This section defines the overrides for semantic role tokens for each theme.

```css
/* === DARK MODE OVERRIDES === */
[data-theme='dark'] {
  --bg-page: var(--tk-gray-900);
  --bg-content: var(--tk-gray-800);
  --bg-card: var(--tk-gray-800);
  --bg-sidebar: var(--tk-gray-800);
  --bg-input: var(--tk-gray-700);
  --bg-subtle: var(--tk-gray-800);
  /*
     GUIDANCE FOR A "DARK PURPLE DRIVEN" THEME:
     To achieve a stronger purple feel, override these core background tokens
     in a more specific selector (e.g., a class on the body/main container)
     or by creating a distinct '[data-theme="dark-purple"]' variant:
     .dark-purple-theme-active {
       --bg-page: var(--tk-purple-950); --bg-content: var(--tk-purple-900);
       --bg-card: var(--tk-indigo-900); --bg-input: var(--tk-indigo-950);
       --bg-sidebar: var(--tk-purple-900); --bg-subtle: var(--tk-purple-800);
     }
     Always re-verify all text and non-text contrast ratios.
  */
  --bg-hover-active: rgba(var(--tk-purple-700-rgb), 0.15); /* Assumes --tk-purple-700-rgb */
  --bg-overlay: rgba(var(--tk-black-rgb), 0.6);
  --text-primary: var(--tk-white);
  --text-secondary: var(--tk-gray-300);
  --text-subtle: var(--tk-gray-400);
  --text-on-dark-bg: var(--tk-white);
  --text-on-color: var(--tk-white);
  --text-link: var(--tk-purple-400);
  --text-link-hover: var(--tk-purple-300);
  --text-heading-brand: var(--tk-purple-300);
  --text-placeholder: var(--tk-gray-400);
  --text-disabled: var(--tk-gray-500);
  --border-standard: var(--tk-gray-700);
  --border-input: var(--tk-gray-600);
  --border-divider: var(--tk-gray-700);
  --border-focus-ring-color: var(--tk-purple-400);
  --border-sidebar: var(--tk-gray-700);
  --border-interactive-strong: var(--tk-purple-400);
  --semantic-success-color-text: var(--tk-green-300);
  --semantic-success-color-bg: rgba(var(--tk-green-800-rgb), 0.3);
  --semantic-success-color-border: var(--tk-green-700);
  --semantic-success-color-icon: var(--tk-green-300);
  --semantic-error-color-text: var(--tk-red-300);
  --semantic-error-color-bg: rgba(var(--tk-red-700-rgb), 0.25);
  --semantic-error-color-border: var(--tk-red-600);
  --semantic-error-color-icon: var(--tk-red-300);
  --semantic-warning-color-text: var(--tk-yellow-300);
  --semantic-warning-color-bg: rgba(var(--tk-yellow-700-rgb), 0.25);
  --semantic-warning-color-border: var(--tk-yellow-500);
  --semantic-warning-color-icon: var(--tk-yellow-300);
  --semantic-info-color-text: var(--tk-blue-300);
  --semantic-info-color-bg: rgba(var(--tk-blue-800-rgb), 0.25);
  --semantic-info-color-border: var(--tk-blue-600);
  --semantic-info-color-icon: var(--tk-blue-300);
  --button-outline-color-border: var(--tk-purple-400);
  --button-outline-color-text: var(--tk-purple-400);
  --button-outline-hover-bg-color: rgba(var(--tk-purple-400-rgb), 0.15); /* Assumes --tk-purple-400-rgb */
  --button-outline-hover-color-text: var(--tk-purple-300);
  --button-disabled-bg-color: var(--tk-gray-700);
  --button-disabled-color-text: var(--tk-gray-400);
  --button-disabled-color-border: var(--tk-gray-600);
  --input-border-color-focus: var(--tk-purple-400);
  --input-focus-shadow-ring: 0 0 0 3px var(--border-focus-ring-color);
  --input-bg-disabled: var(--tk-gray-800);
  --input-border-color-disabled: var(--tk-gray-700);
  --input-color-text-disabled: var(--tk-gray-500);
  --depth-shadow-sm: 0 1px 2px rgba(var(--tk-black-rgb), 0.2), 0 1px 1px rgba(var(--tk-black-rgb), 0.3);
  --depth-shadow-md: 0 4px 6px -1px rgba(var(--tk-black-rgb), 0.3), 0 2px 4px -1px rgba(var(--tk-black-rgb), 0.2);
  --depth-shadow-lg: 0 10px 15px -3px rgba(var(--tk-black-rgb), 0.3), 0 4px 6px -2px rgba(var(--tk-black-rgb), 0.2);
  --depth-shadow-xl: 0 20px 25px -5px rgba(var(--tk-black-rgb), 0.3), 0 10px 10px -5px rgba(var(--tk-black-rgb), 0.2);
  --depth-shadow-2xl: 0 25px 50px -12px rgba(var(--tk-black-rgb), 0.4);
  --depth-shadow-inner: inset 0 2px 4px 0 rgba(var(--tk-black-rgb), 0.2);
  --depth-border-color-highlight: rgba(var(--tk-white-rgb), 0.05);
  --depth-border-color-shadow: rgba(var(--tk-black-rgb), 0.2);
  --overlay-depth-gradient-top: rgba(var(--tk-white-rgb), 0.05);
  --overlay-depth-gradient-bottom: rgba(var(--tk-black-rgb), 0.1);
  --gradient-section-bg-dark: linear-gradient(135deg, var(--tk-indigo-900), var(--tk-purple-950));
  --bg-hero-dark-purple: var(--gradient-dark-purple-hero);
  --gradient-assignment-card-dark: linear-gradient(
    to bottom right,
    rgba(var(--tk-purple-900-rgb), 0.3),
    rgba(var(--tk-indigo-900-rgb), 0.3)
  ); /* Assumes RGB vars */
  --gradient-extension-card-dark: linear-gradient(
    to bottom right,
    rgba(var(--tk-blue-900-rgb), 0.2),
    rgba(var(--tk-cyan-800-rgb), 0.2)
  ); /* Assumes RGB vars */
}

/* === NIGHT MODE OVERRIDES (Warmer, Desaturated) === */
[data-theme='night'] {
  --bg-page: #2e2925;
  --bg-content: #3a342f;
  --bg-card: #3a342f;
  --bg-sidebar: #3a342f;
  --bg-input: #4a4540;
  --bg-subtle: #3a342f;
  --bg-hover-active: #4a4540;
  --bg-overlay: rgba(var(--tk-black-rgb), 0.7);
  --text-primary: #d8cec4;
  --text-secondary: #a89f95;
  --text-subtle: #7a736a;
  --text-on-dark-bg: var(--text-primary);
  --text-on-color: var(--text-primary);
  --text-link: #9c80b1;
  --text-link-hover: #b298c1;
  --text-heading-brand: #9c80b1;
  --text-placeholder: #a89f95;
  --text-disabled: #7a736a;
  --border-standard: #5a4f45;
  --border-input: #6a635a;
  --border-divider: #5a4f45;
  --border-focus-ring-color: #9c80b1;
  --border-sidebar: #5a4f45;
  --border-interactive-strong: #9c80b1;
  --semantic-success-color-text: #90a991;
  --semantic-success-color-bg: rgba(122, 153, 123, 0.15);
  --semantic-success-color-border: #7a997b;
  --semantic-success-color-icon: #90a991;
  --semantic-error-color-text: #c88b8b;
  --semantic-error-color-bg: rgba(179, 107, 107, 0.15);
  --semantic-error-color-border: #b36b6b;
  --semantic-error-color-icon: #c88b8b;
  --semantic-warning-color-text: #c8b39b;
  --semantic-warning-color-bg: rgba(179, 153, 123, 0.15);
  --semantic-warning-color-border: #b3997b;
  --semantic-warning-color-icon: #c8b39b;
  --semantic-info-color-text: #90a4af;
  --semantic-info-color-bg: rgba(122, 142, 153, 0.15);
  --semantic-info-color-border: #7a8e99;
  --semantic-info-color-icon: #90a4af;
  --button-primary-bg-image: linear-gradient(to right, #8a6f9e, #6e5e8e);
  --button-primary-color-text: var(--text-on-dark-bg);
  --button-primary-hover-bg-image: linear-gradient(to right, #7a5f8e, #5e4e7e);
  --button-secondary-bg-image: linear-gradient(to right, #5a8e8a, #4a7e7a);
  --button-secondary-color-text: var(--text-on-dark-bg);
  --button-secondary-hover-bg-image: linear-gradient(to right, #4a7e7a, #3a6e6a);
  --button-accent-bg-image: linear-gradient(to right, #a96b8e, #995b7e);
  --button-accent-color-text: var(--text-on-dark-bg);
  --button-accent-hover-bg-image: linear-gradient(to right, #995b7e, #894b6e);
  --button-outline-color-border: var(--text-link);
  --button-outline-color-text: var(--text-link);
  --button-outline-hover-bg-color: #4a4540;
  --button-outline-hover-color-text: var(--text-primary);
  --button-disabled-bg-color: #3a342f;
  --button-disabled-color-text: var(--text-disabled);
  --button-disabled-color-border: #4a4540;
  --input-border-color-focus: #9c80b1;
  --input-focus-shadow-ring: 0 0 0 3px var(--border-focus-ring-color);
  --input-bg-disabled: #2e2925;
  --input-border-color-disabled: #3a342f;
  --input-color-text-disabled: var(--text-subtle);
  --depth-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.1), 0 1px 1px rgba(0, 0, 0, 0.15);
  --depth-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.15), 0 2px 4px -1px rgba(0, 0, 0, 0.08);
  --depth-border-color-highlight: rgba(var(--text-primary-rgb), 0.03); /* Assumes --text-primary-rgb */
  --depth-border-color-shadow: rgba(var(--tk-black-rgb), 0.15);
  --overlay-depth-gradient-top: rgba(var(--text-primary-rgb), 0.03);
  --overlay-depth-gradient-bottom: rgba(var(--tk-black-rgb), 0.1);
  --gradient-section-bg-dark: linear-gradient(135deg, #3a342f, #2e2925);
  --bg-hero-dark-purple: linear-gradient(135deg, #6e5e8e, #4a3e5e);
  --decorative-blob-color-start: rgba(138, 111, 158, 0.1);
  --decorative-blob-color-end: rgba(90, 142, 138, 0.1);
}
```

#### 4.5. 3D Depth & Shadow Variables
The global `--depth-*` tokens defined in the main `:root` (Section 4.2) provide the base for light theme. These are then overridden in `[data-theme='dark']` and `[data-theme='night']` blocks (as shown in Section 4.4) to adjust intensity and color suitable for darker backgrounds.

#### 4.6. Typography & Spacing Scale Variables
These functional tokens (e.g., `--font-size-md`, `--line-height-normal`, `--space-md`) are generally consistent across themes and are defined in the main `:root` block (Section 4.2).

#### 4.7. Implementing Tokens in Content Management Systems & Builders
While this guide defines CSS Custom Properties as the core mechanism, implementing and managing these tokens within specific platforms (like WordPress with a builder, or other CMS) often involves mapping these variables to the platform's native color management tools.
*   **Global Styles/Colors:** Many platforms offer "Global Color" or "Site-wide Palette" features. It is best practice to populate these platform-level palettes using the values from the TechnoKids CSS tokens. For example, a "Primary Brand Color" in a CMS theme customizer should be set to the value of `var(--tk-purple-500)`.
*   **Dynamic Updates:** If the platform's global color system is robust, changing a global color there can propagate throughout the site sections built with that tool. However, for custom CSS and components styled directly with the `--tk-` variables, the CSS definitions remain the single source of truth.
*   **Consistency:** The goal is to ensure that whether a color is applied via a platform's UI or directly in custom CSS, it resolves to the same TechnoKids design token value, maintaining visual consistency.

---

### 5. Layout, Spacing, & Composition

Effective visual organization is paramount for creating interfaces that are intuitive, easy to scan, and aesthetically pleasing across all themes.

#### 5.1. Principles of Visual Organization
*   **Hierarchy:** Clear visual hierarchy guides the user's attention.
*   **Alignment:** Consistent alignment creates order and reduces cognitive load.
*   **Proximity (Grouping):** Related elements are grouped visually closer.
*   **Repetition & Consistency:** Reuse of design elements and spacing patterns ensures predictability.
*   **Balance:** Achieving visual equilibrium creates a harmonious composition.
*   **White Space (or "Dark Space"):** Strategic use of negative space prevents clutter and improves focus.

#### 5.2. Grid System (Conceptual)
Consistent use of an underlying grid structure (e.g., 12-column responsive grid) is advocated for alignment and proportional placement. Spacing tokens define gutters and margins. The grid must be responsive.

#### 5.3. Spacing Scale & Vertical Rhythm
A consistent spacing scale, based on `var(--space-unit: 8px;)`, is fundamental. All padding, margins, and gaps should use these tokens.

*   **Base Unit:** `--space-unit: 8px;`
*   **Scale (Tokens defined in Section 4.2):**
    *   `--space-xs: calc(0.5 * var(--space-unit));`  (4px)
    *   `--space-sm: var(--space-unit);`             (8px)
    *   `--space-md: calc(2 * var(--space-unit));`  (16px)
    *   `--space-lg: calc(3 * var(--space-unit));`  (24px)
    *   `--space-xl: calc(4 * var(--space-unit));`  (32px)
    *   `--space-2xl: calc(6 * var(--space-unit));` (48px)
    *   `--space-3xl: calc(8 * var(--space-unit));` (64px)
    *   `--space-section-padding-y: var(--space-3xl);`

#### 5.4. White Space (Negative Space) Strategy
White space (or "dark space" in dark themes) is an active design element.
*   Improves Readability & Legibility.
*   Reduces Cognitive Load & Clutter.
*   Enhances Focus & Attention.
*   Defines Relationships & Hierarchy.

#### 5.5. Container & Section Defaults
Baseline structural components for page layouts.

```css
.container {
  width: 100%;
  max-width: 1280px;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--space-lg);
  padding-right: var(--space-lg);
  box-sizing: border-box;
}
.section {
  width: 100%;
  padding-top: var(--space-section-padding-y);
  padding-bottom: var(--space-section-padding-y);
  background-color: var(--bg-content);
  color: var(--text-secondary);
  transition:
    background-color var(--transition-normal),
    color var(--transition-normal);
  box-sizing: border-box;
}
.section--brand-feature {
  /* Example for "Dark Purple Driven" context */
  background-image: var(--gradient-dark-purple-hero);
  color: var(--text-on-dark-bg);
}
.section--brand-feature h1,
.section--brand-feature h2,
.section--brand-feature p,
.section--brand-feature a {
  color: var(--text-on-dark-bg);
}
.section--brand-feature a:hover,
.section--brand-feature a:focus-visible {
  color: var(--tk-violet-300);
  text-decoration-color: var(--tk-violet-300);
}
```

---

### 6. Typography System: Clarity, Hierarchy, and Readability

Typography is a cornerstone of the TechnoKids user experience.

#### 6.1. Typographic Philosophy
*   **Readability First:** Text must be exceptionally easy to read.
*   **Legibility:** Individual characters must be distinct.
*   **Clear Hierarchy:** A well-defined typographic scale guides users.
*   **Accessibility as Standard:** All choices must meet WCAG 2.1 AA contrast.
*   **Brand Expression:** Typography subtly contributes to the brand persona.

#### 6.2. Font Families, Weights, Line Heights
*   **Font Family (ACTION REQUIRED - Placeholder):**
    *   `--font-family-sans`: `-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'`
        *   **ACTION REQUIRED:** This system font stack is a **placeholder**. It is **imperative** to replace this with the specific, licensed web font(s) chosen for the TechnoKids brand. This choice will significantly impact the overall look and feel. The chosen font(s) must offer a good range of weights and be highly legible on screens.
    *   `--font-family-body`: `var(--font-family-sans)`
    *   `--font-family-headings`: `var(--font-family-sans)`
*   **Font Weights:**
    *   `--font-weight-normal: 400;`
    *   `--font-weight-semibold: 600;`
    *   `--font-weight-bold: 700;`
    *   (Available: `--font-weight-light: 300;`, `--font-weight-black: 900;`)
*   **Line Heights (Leading):**
    *   `--line-height-tight: 1.2;` (Headings)
    *   `--line-height-normal: 1.5;` (Body text)
    *   `--line-height-loose: 1.8;` (Specific contexts)

```css
body {
  font-family: var(--font-family-body);
  font-size: var(--font-size-md);
  line-height: var(--line-height-normal);
  color: var(--text-secondary);
  background-color: var(--bg-page);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-family-headings);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  color: var(--text-primary);
  margin-top: var(--space-lg);
  margin-bottom: var(--space-md);
}
h1 {
  font-size: var(--font-size-h1);
}
h2 {
  font-size: var(--font-size-h2);
}
h3 {
  font-size: var(--font-size-h3);
}
h4 {
  font-size: var(--font-size-h4);
  font-weight: var(--font-weight-semibold);
}
h5 {
  font-size: var(--font-size-h5);
  font-weight: var(--font-weight-semibold);
}
h6 {
  font-size: var(--font-size-h6);
  font-weight: var(--font-weight-semibold);
}
p {
  margin-bottom: var(--space-md);
  max-width: 75ch;
}
p + p {
  margin-top: var(--space-md);
}
a {
  color: var(--text-link);
  text-decoration: none;
  font-weight: var(--font-weight-semibold);
  transition: color var(--transition-fast);
}
a:hover,
a:focus-visible {
  color: var(--text-link-hover);
  text-decoration: underline;
  text-decoration-thickness: max(1px, 0.0625em);
  text-underline-offset: 0.125em;
}
strong,
b {
  font-weight: var(--font-weight-bold);
  color: inherit;
}
em,
i {
  font-style: italic;
  color: inherit;
}
ul,
ol {
  padding-left: var(--space-lg);
  margin-bottom: var(--space-md);
}
li {
  margin-bottom: var(--space-sm);
}
```

#### 6.3. Responsive Typographic Scale (with `rem` and `clamp()`)
Font sizes are defined in `rem` for accessibility. `clamp()` is used for fluid responsiveness in headings.

| Element | Variable Name       | Value (CSS `clamp` or `rem`)         | Approx. Pixel Range (at 16px base)      |
| :------ | :------------------ | :----------------------------------- | :-------------------------------------- |
| H1      | `--font-size-h1`    | `clamp(2.25rem, 5vw + 1rem, 3.75rem)`| 36px to 60px                            |
| H2      | `--font-size-h2`    | `clamp(1.75rem, 4vw + 1rem, 3rem)`   | 28px to 48px                            |
| H3      | `--font-size-h3`    | `clamp(1.5rem, 3vw + 1rem, 2.25rem)` | 24px to 36px                            |
| H4      | `--font-size-h4`    | `1.5rem`                             | 24px                                    |
| H5      | `--font-size-h5`    | `1.25rem`                            | 20px                                    |
| H6      | `--font-size-h6`    | `1rem`                               | 16px                                    |
| Body (MD)| `--font-size-md`    | `1rem`                               | 16px                                    |
| Body (LG)| `--font-size-lg`    | `1.125rem`                           | 18px                                    |
| Body (SM)| `--font-size-sm`    | `0.875rem`                           | 14px                                    |
| Body (XS)| `--font-size-xs`    | `0.75rem`                            | 12px                                    |

#### 6.4. Text Color Application & WCAG Contrast
All text color choices must prioritize readability and meet WCAG 2.1 AA contrast minimums.

*   **6.4.1. Primary, Secondary, and Tertiary Text Colors:** Semantic roles (`--text-primary`, etc.) are redefined for each theme. Contrast ratios are verified against their intended backgrounds.
    *   **Light Theme (on `--bg-content: var(--tk-white)`):**
        *   `--text-primary` (`--tk-gray-900`): **15.76:1 (AAA)**.
        *   `--text-secondary` (`--tk-gray-700`): **7.55:1 (AAA)**.
        *   `--text-subtle` (`--tk-gray-500`): **4.61:1 (AA)**.
    *   **Dark Theme (on `--bg-content: var(--tk-gray-800)`):**
        *   `--text-primary` (`--tk-white`): **13.57:1 (AAA)**.
        *   `--text-secondary` (`--tk-gray-300`): **7.05:1 (AAA)**.
        *   `--text-subtle` (`--tk-gray-400`): **4.51:1 (AA)**.
    *   **Night Theme (on `--bg-content: #3a342f`):**
        *   `--text-primary` (`#d8cec4`): **10.77:1 (AAA)**.
        *   `--text-secondary` (`#a89f95`): **5.65:1 (AA)**.
        *   `--text-subtle` (`#7a736a`): **3.06:1 (AA Large)**.
    *   **For "Dark Purple Driven" Contexts (e.g., text on `--bg-content: var(--tk-purple-900)`):**
        *   Use `--text-on-dark-bg` (`--tk-white`): Contrast: **11.0:1 (AAA)**.
*   **6.4.2. Link Text Colors & States:** Must contrast adequately.
    *   **Light Theme:** Default (`--tk-purple-600`): **5.36:1 (AA)**. Hover (`--tk-purple-700`): **7.85:1 (AAA)**.
    *   **Dark Theme:** Default (`--tk-purple-400`): **5.05:1 (AA)**. Hover (`--tk-purple-300`): **7.14:1 (AAA)**.
    *   **Night Theme:** Default (`#9c80b1`): **4.55:1 (AA)**. Hover (`#b298c1`): **6.17:1 (AA)**.
*   **6.4.3. Text on Colored/Gradient Backgrounds (`--text-on-dark-bg` or `--text-on-color`):** Typically `var(--tk-white)` or `var(--tk-black)`. Always verify contrast (see Section 3.3 for gradients).
*   **6.4.4. Placeholder Text (`--text-placeholder`):**
    *   Light Theme: `var(--tk-gray-500)` on white. Contrast: **4.61:1 (AA)**.
    *   Dark Theme: `var(--tk-gray-400)` on `var(--tk-gray-700)`. Contrast: **3.0:1 (AA Large)**.
    *   Night Theme: `#a89f95` on `#4a4540`. Contrast: **4.55:1 (AA)**.
    *   **Reminder:** Placeholder text is not a substitute for a visible `<label>`.

#### 6.5. Font Loading & Performance Considerations
*   **`font-display` Property:** Use `font-display: swap;` in `@font-face` declarations.
    ```css
    @font-face {
      font-family: 'YourTechnoKidsFont'; /* ACTION REQUIRED: Replace */
      src:
        url('/fonts/YourTechnoKidsFont-Regular.woff2') format('woff2'),
        /* ... */;
      font-weight: 400;
      font-style: normal;
      font-display: swap;
    }
    ```
*   **Preloading Critical Fonts:** Use `<link rel="preload">` for essential fonts.
    ```html
    <link rel="preload" href="/fonts/YourTechnoKidsFont-Regular.woff2" as="font" type="font/woff2" crossorigin>
    ```
*   **Font Subsetting:** Include only necessary characters/glyphs.
*   **Font Formats:** Prioritize WOFF2, with WOFF as fallback.
*   **Self-Hosting vs. Third-Party Services:** Self-hosting gives maximum control.

---

### 7. Application to UI Components & Interactive States (WCAG & Usability Focused)

This section provides detailed specifications for applying the defined color palette and typographic standards to a comprehensive range of key User Interface (UI) components and their various interactive states. The primary objective is to ensure consistency, usability, and robust accessibility across all interactive elements of TechnoKids digital products, adaptable for Light, Dark, "Dark Purple Driven," and Night themes.

#### 7.1. General Principles for Component Color Application
*   **Affordance:** Components must visually communicate their interactivity. Color, shape, depth cues (shadows), and iconography are primary tools for this. Users should intuitively understand what elements are clickable, tappable, or otherwise interactive.
*   **Feedback:** Immediate and clear visual feedback must be provided in response to user interactions (e.g., hover, focus, click/tap, selection, validation). This confirms the system has received the input and is processing it.
*   **State Clarity:** Each interactive state of a component (default, hover, focus, active, disabled, error) must be visually distinct from other states and from any static (non-interactive) elements on the page. This clarity prevents user confusion and aids in understanding the component's current status and capabilities.
*   **Accessibility (Non-Negotiable):**
    *   All color combinations used for text within components (e.g., button labels, input text, alert messages) must meet WCAG 2.1 AA text contrast ratios (4.5:1 for normal text, 3:1 for large text) against their specific background in the current theme.
    *   All meaningful graphical elements of components (e.g., input field borders, checkbox boundaries, toggle switch tracks/knobs, informational icon details) must meet WCAG 2.1 AA non-text contrast ratios (3:1 against adjacent colors).
    *   Focus indicators must be highly visible and meet these non-text contrast requirements.

#### 7.2. Definition of Standard Component States & Visual Feedback
*   **7.2.1. Default State:**
    *   **Purpose:** The component's standard, resting appearance before any user interaction.
    *   **Visuals:** Clearly conveys its purpose and, if interactive, its potential for interaction. Uses base brand, neutral, or semantic role tokens appropriate for the component's function and the current theme.
*   **7.2.2. Hover State (`:hover`):**
    *   **Purpose:** Provides visual feedback when a pointer (typically a mouse cursor) hovers over an interactive element, confirming it is interactive and about to be activated or selected.
    *   **Visuals:** Subtle but noticeable changes are preferred to avoid jarring shifts. Examples include:
        *   Slight lightening or darkening of the background color (e.g., using a 50 or 100-shade lighter/darker variant from the palette, or an RGBA overlay).
        *   A subtle lift or change in shadow (e.g., transitioning from `var(--depth-shadow-md)` to `var(--depth-shadow-lg)` along with `var(--depth-transform-hover)`).
        *   Underline appearing for links, or text color change.
*   **7.2.3. Focus State (`:focus-visible` - Critical for Accessibility):**
    *   **Purpose:** Indicates the UI element that currently has keyboard focus. This is **essential** for users navigating with a keyboard (e.g., users with motor impairments, screen reader users who also use keyboard navigation, power users).
    *   **Visuals:** Must be **highly visible and clearly distinguishable** from both the default and hover states. The `:focus-visible` pseudo-class is used to ensure these prominent indicators primarily appear for keyboard users, avoiding visual "noise" for mouse users who already receive hover feedback.
        *   **Default Standard:** `outline: 2px solid var(--border-focus-ring-color); outline-offset: 2px;`
        *   The `var(--border-focus-ring-color)` (e.g., `--tk-purple-500` in Light Theme, `--tk-purple-400` in Dark Theme) must provide at least a **3:1 contrast ratio** against the component's own background AND its immediate surrounding page/container background.
*   **7.2.4. Active/Pressed State (`:active`):**
    *   **Purpose:** Indicates that a component is currently being clicked or activated (e.g., mouse button is down on a button, or during the tap action on touch devices). This state is typically brief.
    *   **Visuals:** A more pronounced visual change than hover, signifying direct interaction. Examples include:
        *   An inset shadow effect (e.g., using `var(--depth-shadow-inner)`).
        *   A slightly darker background shade or a subtle scale down (`transform: var(--depth-transform-active);`).
*   **7.2.5. Disabled State (Perceivability & `aria-disabled`):**
    *   **Purpose:** Shows that a component is temporarily not interactive or unavailable to the user.
    *   **Visuals:** Must still be perceivable, and its general form and label (if any) should remain understandable. This guide uses distinct color tokens (`--button-disabled-bg-color`, `--button-disabled-color-text`, `--input-bg-disabled`, etc.) designed for better perceivability rather than relying solely on reduced opacity of the active state colors. `cursor: not-allowed;` should always be applied.
    *   **Text Contrast (Best Practice):** While WCAG 2.1 does not strictly mandate 4.5:1 contrast for text in *truly disabled* (non-interactive per HTML `disabled` attribute) controls for passing SC 1.4.3, it is a best practice to maintain as much readability as possible. The disabled text color tokens (`--text-disabled` or component-specific disabled text colors) are chosen to achieve good perceivability against their defined disabled backgrounds, aiming for at least 3:1 for large text where feasible.
    *   **ARIA Implementation:** For elements that are made non-interactive but should still be perceivable and understood by assistive technologies (e.g., a button that is temporarily disabled but its presence and label are relevant to the user's context), use `aria-disabled="true"`. This allows the element to potentially remain in the focus order (if logical for the user flow) and be announced as "disabled" by screen readers. The native HTML `disabled` attribute on form controls typically removes the element from the tab order and may make it invisible to assistive technologies in forms/interaction mode.
*   **7.2.6. Error/Validation State:**
    *   **Purpose:** Used primarily for form input fields to signal an error in user input or that validation has failed.
    *   **Visuals:** Typically involves changing the input's border color to `var(--semantic-error-color-border)`. An error icon (e.g., using `var(--semantic-error-color-icon)` for its fill) and a clearly worded error message (using `var(--semantic-error-color-text)`) should also be displayed adjacent to the input.
    *   **ARIA Implementation:** Set `aria-invalid="true"` on the input field when it's in an error state. Ensure the visible error message is programmatically associated with the input using `aria-describedby` or, for more complex scenarios, `aria-errormessage`.

#### 7.3. Specific Component Color & State Guidance (with CSS Examples)

**7.3.1. Accordions / Collapsible Sections**
*   **Purpose:** To allow users to show and hide sections of content.
*   **HTML/ARIA:** Typically a heading element containing a `<button>` that controls an associated content region. Use `aria-expanded` on the button and `aria-hidden` (or the `hidden` attribute) on the content region. The button should control the `id` of the region via `aria-controls`.
*   **Styling (Token-based):**
    *   **Header/Button:**
        *   Background: `var(--bg-subtle)` or transparent if on a `var(--bg-card)` background.
        *   Text: `var(--text-primary)` or `var(--text-link)` if styled more like a link.
        *   Icon (e.g., chevron): `fill: currentColor` or `var(--text-secondary)`.
        *   Padding: `var(--space-md)`.
        *   Border-bottom: `1px solid var(--border-divider)` (if multiple accordions are stacked).
    *   **Content Area:** Padding `var(--space-md)`. Background `var(--bg-content)` or `var(--bg-card)`.
*   **States (Header/Button):**
    *   **Default:** Clear visual distinction between header and content. Icon indicates collapsed state.
    *   **Hover:** `background-color: var(--bg-hover-active);` on header. Icon may subtly change color (e.g., to `var(--text-link-hover)`).
    *   **Focus-Visible:** Standard focus ring (`var(--border-focus-ring-color)`) on the button within the header.
    *   **Active (Pressed):** Header background may darken slightly (e.g., `var(--tk-purple-200)` in Light theme if base is `var(--tk-purple-100)` or similar).
    *   **Expanded State:** Icon rotates (e.g., chevron points up). Header might have a slightly different background or a more prominent bottom border to visually connect it to the open content.
*   **Thematic Adaptation:**
    *   **Light:** As above.
    *   **Dark/Purple:** Header BG `var(--tk-gray-700)` or `var(--tk-indigo-800)`. Text `var(--text-primary)`. Icon `var(--tk-gray-300)`. Hover BG `rgba(var(--tk-purple-400-rgb), 0.1)`.
    *   **Night:** Header BG `var(--bg-input)`. Text `var(--text-primary)`. Icon `var(--text-secondary)`.
*   **Accessibility:** Ensure keyboard operability (Enter/Space to toggle expansion). Icon rotation should have a `prefers-reduced-motion` consideration (i.e., might change state without animation). Clear visual cue for expanded state. Content within the panel must follow normal accessibility guidelines.

**7.3.2. Alerts & Notifications (Semantic States)**
*   **Purpose:** To convey timely and contextual messages to the user (e.g., success, error, warning, information).
*   **Structure:** Typically an icon + Text Message. A close button (`<button>`) is often included.
*   **ARIA:** `role="alert"` for critical, assertive messages that interrupt the user (use sparingly). `role="status"` for polite announcements of changes. If the alert can be closed, the close button needs an accessible label (e.g., `aria-label="Close alert"`).
*   **Styling:**
    *   Padding: `var(--space-md)`.
    *   Border radius: `var(--card-border-radius)`.
    *   Border: `var(--depth-border-width) solid var(--semantic-*-color-border)` (e.g., `--semantic-success-color-border`).
    *   Background: `var(--semantic-*-color-bg)` (e.g., `--semantic-success-color-bg`).
    *   Text: `var(--semantic-*-color-text)` (e.g., `--semantic-success-color-text`).
    *   Icon: `fill: var(--semantic-*-color-icon)` (e.g., `--semantic-success-color-icon`). Icon size typically `var(--font-size-lg)` or `var(--font-size-xl)`.
    *   Close Button: Styled as an icon-only button, using subtle colors that contrast with the alert background (e.g., `fill: var(--semantic-*-color-text)`).
*   **Thematic Adaptation:** Uses the theme-specific semantic color variables defined in Section 4.4. These are designed to provide adequate contrast in each theme (e.g., translucent backgrounds in Dark mode, specific text/icon colors).
    *   **Example Success Alert (Dark Theme):** BG `rgba(var(--tk-green-800-rgb), 0.3)`, Text `var(--tk-green-300)`, Border `var(--tk-green-700)`, Icon `var(--tk-green-300)`.
*   **Accessibility:** Text must meet contrast with its alert background. Close button must be keyboard accessible and have a visible focus state. Ensure alerts don't auto-dismiss too quickly for users to read.

**7.3.3. Avatars**
*   **Purpose:** To visually represent users or entities.
*   **Styling:**
    *   Typically circular (`border-radius: 50%;`) or rounded squares (`border-radius: var(--card-border-radius);`).
    *   Size defined by `width` and `height` (e.g., `var(--space-xl)` for small, `var(--space-2xl)` for medium, `var(--space-3xl)` for large).
    *   Image: `object-fit: cover;` to ensure the image fills the space without distortion.
    *   Placeholder (if no image is available):
        *   Background: `var(--tk-purple-100)` (Light Theme), `var(--tk-purple-800)` (Dark/Purple Theme), `var(--bg-input)` (Night Theme).
        *   Initials Text: `color: var(--tk-purple-700)` (Light), `color: var(--tk-purple-300)` (Dark/Purple), `color: var(--text-primary)` (Night). Font size should be proportional to avatar size (e.g., ~40-50% of avatar height). Text should be centered horizontally and vertically. `font-weight: var(--font-weight-semibold);`.
    *   Optional Border: `1px solid var(--border-standard)` or a subtle brand tint like `var(--tk-purple-300)`.
*   **Thematic Adaptation:** Placeholder background and text colors adapt as specified above.
*   **Accessibility:** If the avatar image conveys information not available in adjacent text (which is rare for typical user avatars), provide meaningful `alt` text for the image. If it's purely decorative or accompanied by a visible name, use `alt=""` or `aria-hidden="true"`. Initials in placeholders are generally considered decorative if a name is also present.

**7.3.4. Badges / Tags**
*   **Purpose:** To display small pieces of discrete information, status indicators, or category labels.
*   **Styling:**
    *   Padding: `var(--space-xs) var(--space-sm);` (e.g., 4px 8px).
    *   Border radius: `var(--input-border-radius)` for slightly rounded, or `9999px` for a fully pill-shaped badge.
    *   Font size: `var(--font-size-xs)` or `var(--font-size-sm)`.
    *   Font weight: `var(--font-weight-semibold)`.
    *   Line height: `var(--line-height-tight)`.
    *   **Default/Neutral Badge:** Background `var(--tk-gray-100)`, Text `var(--tk-gray-700)`.
    *   **Brand Accent Badge:** Background `var(--tk-purple-500)`, Text `var(--text-on-dark-bg)`.
    *   **Semantic Badges:** Can use semantic color variables for background and text (e.g., `background-color: var(--semantic-success-color-bg); color: var(--semantic-success-color-text); border: 1px solid var(--semantic-success-color-border);` for a subtle bordered success badge).
*   **Thematic Adaptation:**
    *   **Dark/Purple Theme:** Neutral BG `var(--tk-gray-700)`, Text `var(--tk-gray-200)`. Brand Accent BG `var(--tk-purple-400)` or `var(--tk-violet-400)`, Text `var(--text-on-dark-bg)`. Semantic badges use dark theme semantic colors.
    *   **Night Theme:** Neutral BG `var(--bg-input)`, Text `var(--text-secondary)`. Brand Accent BG `#8a6f9e` (desaturated purple), Text `var(--text-on-dark-bg)`.
*   **Accessibility:** Ensure text has sufficient contrast (at least 4.5:1) against the badge's background color. If badges are interactive (e.g., clickable to filter), they must have clear hover and focus states like buttons.

**7.3.5. Breadcrumbs**
*   **Purpose:** To show the user's current location within a site's hierarchy and provide quick navigation back to parent pages.
*   **HTML/ARIA:** Typically an ordered list (`<ol>`) wrapped in a `<nav>` element with `aria-label="breadcrumb"`. Each item, except the last, is a link. The last item (current page) is plain text and has `aria-current="page"`. Separators are often added via CSS pseudo-elements (`::before` on `<li>` excluding the first).
*   **Styling:**
    *   List style: `list-style: none; padding: 0; margin: 0; display: flex; align-items: center; flex-wrap: wrap;`.
    *   List items (`<li>`): `display: inline-flex; align-items: center;`.
    *   Links (`<a>`): `color: var(--text-link); font-weight: var(--font-weight-normal); text-decoration: none;`.
        *   Hover/Focus: `color: var(--text-link-hover); text-decoration: underline;`.
    *   Current Page Item: `color: var(--text-secondary); font-weight: var(--font-weight-semibold);`.
    *   Separator (e.g., ">", "/"): Added via `content: '/';` in CSS. `color: var(--text-subtle); margin: 0 var(--space-sm);`.
*   **Thematic Adaptation:** Uses theme-specific text link, secondary, and subtle colors.
    *   **Dark/Purple Theme:** Links `var(--tk-purple-300)`. Current Page `var(--tk-gray-300)`. Separator `var(--tk-purple-700)` or `var(--tk-gray-500)`.
    *   **Night Theme:** Links `var(--text-link)`. Current Page `var(--text-secondary)`. Separator `var(--text-subtle)`.
*   **Accessibility:** Ensure current page is clearly identified with `aria-current="page"`. Separators should be decorative (`aria-hidden="true"` on pseudo-elements if possible, or ensure screen readers don't announce them awkwardly).

**7.3.6. Buttons (Primary, Secondary, Accent, Outline, Disabled)**
*   **Purpose:** For initiating actions.
*   **HTML Structure (Recommended):** `<button type="button">Action</button>` or `<a href="..." role="button">Link Action</a>`.
*   **Base `.depth-button` Styles (Common to all variations):**
    ```css
    .depth-button {
      position: relative;
      border-radius: var(--card-border-radius);
      padding: var(--space-sm) var(--space-md);
      font-family: var(--font-family-sans);
      font-weight: var(--font-weight-semibold);
      font-size: var(--font-size-md);
      line-height: var(--line-height-normal);
      border: var(--depth-border-width) solid transparent;
      transition: var(--transition-default);
      cursor: pointer;
      overflow: hidden;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      text-decoration: none;
      white-space: nowrap;
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
      min-height: 44px;
      min-width: 44px;
      box-sizing: border-box;
    }
    .depth-button::before {
      /* Optional inner shimmer effect for 3D feel */
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: var(--overlay-depth-gradient);
      opacity: 0;
      transition: opacity var(--transition-fast);
      pointer-events: none;
      z-index: 0;
    }
    .depth-button:hover::before {
      opacity: 1;
    }
    .depth-button:focus-visible {
      outline: 2px solid var(--border-focus-ring-color);
      outline-offset: 2px;
      z-index: 1;
    }
    .depth-button:active {
      transform: var(--depth-transform-active);
    }
    /* Optional: Icon within button */
    .depth-button .icon {
      margin-right: var(--space-sm); /* If icon is leading */
    }
    .depth-button .icon--trailing {
      margin-left: var(--space-sm);
      margin-right: 0;
    }
    ```

*   **Primary Button (`.button-primary`):** For the principal call to action.
    *   **CSS (Light Theme Example):**
        ```css
        .depth-button.button-primary {
          background-image: var(--button-primary-bg-image); /* e.g., --gradient-primary-brand */
          color: var(--button-primary-color-text); /* e.g., --text-on-dark-bg (--tk-white) */
          box-shadow:
            var(--depth-shadow-md),
            inset 0 1px 0 var(--depth-border-color-highlight),
            inset 0 -1px 0 var(--depth-border-color-shadow);
          border-color: transparent;
        }
        .depth-button.button-primary:hover {
          background-image: var(--button-primary-hover-bg-image); /* Darker gradient variant */
          transform: var(--depth-transform-hover);
          box-shadow:
            var(--depth-shadow-lg),
            inset 0 1px 0 var(--depth-border-color-highlight),
            inset 0 -1px 0 var(--depth-border-color-shadow);
        }
        .depth-button.button-primary:active {
          box-shadow:
            var(--depth-shadow-inner),
            inset 0 1px 0 var(--depth-border-color-shadow),
            inset 0 -1px 0 var(--depth-border-color-highlight);
        }
        ```
    *   **WCAG Text Contrast (Light Theme):** `var(--tk-white)` on average of `--tk-purple-500` & `--tk-indigo-500` (approx `#7143D8`) is **~6.4:1 (AA Pass)**.
    *   **"Dark Purple Driven" Theme Adaptation:** If page BG is dark purple (e.g., `--tk-purple-900`), primary buttons might use a brighter or contrasting gradient: `background-image: linear-gradient(to right, var(--tk-purple-500), var(--tk-violet-400));`. Focus ring: `var(--border-focus-ring-color)` should be a light, contrasting color from the dark theme overrides (e.g., `--tk-purple-300`).

*   **Secondary Button (`.button-secondary`):** For less prominent actions.
    *   **CSS (Light Theme Example):** Uses `var(--button-secondary-bg-image)` (Blue/Cyan gradient), `var(--button-secondary-color-text)` (`--text-on-dark-bg`). Hover uses `var(--button-secondary-hover-bg-image)`. Shadows similar to primary.
    *   **WCAG Text Contrast (Light Theme):** `var(--tk-white)` on average of `--tk-blue-500` & `--tk-cyan-500` (approx `#2284F8`) is **~4.63:1 (AA Pass)**.
    *   **"Dark Purple Driven" Adaptation:** Could use a lighter purple/violet gradient or a distinct neutral dark gradient. Text color remains light.

*   **Accent Button (`.button-accent`):** For high-emphasis actions.
    *   **CSS (Light Theme Example):** Uses `var(--button-accent-bg-image)` (Pink/Rose gradient), `var(--button-accent-color-text)` (`--tk-black` for contrast). Hover uses `var(--button-accent-hover-bg-image)`. Shadows similar to primary.
    *   **WCAG Text Contrast (Light Theme):** `var(--tk-black)` on average of `--tk-pink-500` & `--tk-rose-500` (approx `#F0417B`) is **~7.05:1 (AAA Pass)**.
    *   **"Dark Purple Driven" Adaptation:** The existing Pink/Rose accent often provides good contrast on dark purple. `var(--tk-black)` text would still work.

*   **Outline Button (`.button-outline`):** For secondary or tertiary actions requiring less visual weight.
    *   **CSS (Light Theme Example):**
        ```css
        .depth-button.button-outline {
          background-color: transparent;
          color: var(--button-outline-color-text); /* e.g., --tk-purple-600 */
          border: 2px solid var(--button-outline-color-border); /* e.g., --tk-purple-500 */
          box-shadow: none;
        }
        .depth-button.button-outline::before {
          display: none;
        } /* No inner shimmer */
        .depth-button.button-outline:hover {
          background-color: var(--button-outline-hover-bg-color); /* e.g., --tk-purple-50 */
          color: var(--button-outline-hover-color-text); /* e.g., --tk-purple-700 */
        }
        .depth-button.button-outline:active {
          background-color: var(--tk-purple-100); /* Slightly darker active BG */
          transform: var(--depth-transform-active);
        }
        .depth-button.button-outline:focus-visible {
          /* Ensure focus ring is distinct from border */
          outline: 2px solid var(--border-focus-ring-color);
          outline-offset: 1px; /* Adjust if border is thick */
        }
        ```
    *   **WCAG Text Contrast (Text/Page BG - Light Theme):** `--tk-purple-600` on `--bg-content (#FFF)` is **5.36:1 (AA Pass)**.
    *   **WCAG Non-Text Contrast (Border/Page BG - Light Theme):** `--tk-purple-500` on `--bg-content (#FFF)` is **6.29:1 (AAA Pass)**.
    *   **"Dark Purple Driven" Adaptation (on `--tk-purple-900` page BG):** `color: var(--tk-purple-300); border-color: var(--tk-purple-400);`. Hover BG `rgba(var(--tk-purple-400-rgb), 0.15);`. Focus Outline `var(--tk-purple-300)`.

*   **Disabled Button (`.button-disabled`, or `[aria-disabled="true"]` on any button variant):**
    *   **CSS (Applied to any `.depth-button` variant):**
        ```css
        .depth-button.button-disabled,
        .depth-button[aria-disabled='true'] {
          background-image: none; /* Remove gradients */
          background-color: var(--button-disabled-bg-color); /* Theme-dependent */
          color: var(--button-disabled-color-text); /* Theme-dependent */
          border: 1px solid var(--button-disabled-color-border); /* Theme-dependent */
          box-shadow: none;
          cursor: not-allowed;
          transform: none !important;
          opacity: 1;
        }
        .depth-button.button-disabled::before,
        .depth-button[aria-disabled='true']::before {
          display: none;
        }
        .depth-button[aria-disabled='true']:focus-visible {
          /* Subdued focus for ARIA-disabled if focusable */
          outline: 2px dashed var(--text-disabled);
          outline-offset: 2px;
        }
        ```
    *   **WCAG Text Perceivability (Light Theme):** `--tk-gray-600` (text) on `--tk-gray-100` (bg). Contrast: **5.43:1 (AA Pass)**.
    *   **Dark Theme:** Uses `--button-disabled-bg-color: var(--tk-gray-700); --button-disabled-color-text: var(--tk-gray-400);`. Contrast: **3.0:1 (AA for Large Text)**.
    *   **Night Theme:** Uses `--button-disabled-bg-color: #3a342f; --button-disabled-color-text: #7a736a;`. Contrast: **3.06:1 (AA for Large Text)**.

**7.3.7. Cards (`.depth-card`) & Content Containers**
*   **Purpose:** To group related content or functionality into a distinct visual unit.
*   **Styling:**
    *   Background: `var(--bg-card)`.
    *   Padding: `var(--card-padding)` (e.g., `var(--space-lg)`).
    *   Border radius: `var(--card-border-radius)` (e.g., 8px).
    *   Shadow: `var(--depth-shadow-md)`.
    *   Optional accent: A top border (`border-top: 4px solid var(--tk-purple-500);`) or a subtle gradient strip.
*   **States (if card is interactive/link):**
    *   **Hover:** `box-shadow: var(--depth-shadow-lg); transform: var(--depth-transform-hover);`.
    *   **Focus-Visible:** Standard focus ring around the entire card.
*   **Thematic Adaptation:**
    *   **Light Theme:** BG `var(--tk-white)`. Accent `var(--tk-purple-500)`.
    *   **Dark/Purple Theme:** BG `var(--tk-gray-800)` or for "Dark Purple Driven" `var(--tk-indigo-900)` or `var(--tk-purple-800)`. Text within these dark cards must use light colors (`--text-on-dark-bg`, `--tk-gray-200`, etc.) ensuring contrast against the specific card BG. Accent `var(--tk-violet-400)`.
    *   **Night Theme:** BG `var(--bg-card)`. Text uses Night theme colors. Accent could be `#9c80b1`.
*   **Accessibility:** Ensure content within cards follows standard readability and contrast guidelines. If cards are links, they must be clearly focusable and operable.

**7.3.8. Checkboxes & Radio Buttons**
*   **Purpose:** To allow single (radio) or multiple (checkbox) selections from a list.
*   **HTML/ARIA:** `<input type="checkbox/radio">` correctly associated with a `<label>` (e.g., via `for` attribute or by wrapping the input). For custom styling, the native input is often visually hidden, and a pseudo-element or `<span>` is styled. The native input must remain focusable and operable.
*   **Styling (Custom Control Example):**
    *   **Container (`.checkbox-custom`, `.radio-custom`):** `display: inline-flex; align-items: center; cursor: pointer;`.
    *   **Visual Box/Circle (styled `<span>` or pseudo-element):**
        *   Size: Approx `var(--font-size-xl)` (e.g., 20px width/height).
        *   Border: `var(--depth-border-width) solid var(--border-input)`.
        *   Background (unchecked): `var(--bg-input)`.
        *   Checkbox `border-radius: var(--input-border-radius) / 2;` (slightly rounded). Radio `border-radius: 50%;`.
        *   Transition: `background-color var(--transition-fast), border-color var(--transition-fast);`.
    *   **Checkmark/Dot (SVG icon or pseudo-element, visible when checked):**
        *   Color: `var(--text-on-color)` (typically `var(--tk-white)`).
        *   Positioned within the box/circle.
    *   **Checked State (Visual Box/Circle):**
        *   Background: `var(--tk-purple-500)`.
        *   Border: `var(--tk-purple-500)`.
    *   **Label Text:** `margin-left: var(--space-sm); color: var(--text-primary);`.
*   **States:**
    *   **Hover (on label/control):** Visual box/circle border may change to `var(--input-border-color-focus)`.
    *   **Focus-Visible (on native hidden input):** Apply standard focus ring style to the custom visual box/circle or the label.
        ```css
        input[type='checkbox']:focus-visible + .checkbox-custom-visual,
        input[type='radio']:focus-visible + .radio-custom-visual {
          outline: 2px solid var(--border-focus-ring-color);
          outline-offset: 2px;
        }
        ```
    *   **Disabled:** Visual box/circle BG `var(--input-bg-disabled)`, border `var(--input-border-color-disabled)`. Checkmark/dot (if present) `var(--input-color-text-disabled)`. Label text `var(--text-disabled)`. `cursor: not-allowed;` on container.
*   **Thematic Adaptation:**
    *   **Dark/Purple Theme:** Unchecked Border `var(--tk-gray-500)`. Unchecked BG `var(--tk-gray-700)`. Checked BG/Border `var(--tk-violet-400)`. Checkmark/Dot `var(--tk-black)` or `var(--tk-white)`. Label Text `var(--text-primary)`.
    *   **Night Theme:** Unchecked Border `var(--border-input)`. Unchecked BG `var(--bg-input)`. Checked BG/Border `var(--text-link)`. Checkmark/Dot `var(--text-on-dark-bg)`. Label Text `var(--text-primary)`.
*   **Accessibility:** Critical that the hidden native input is focusable and its state is programmatically linked to the visual representation. Clicking the label must toggle the input's state. Non-text contrast for the boundaries of the control (checked and unchecked) and the checkmark/dot against its immediate background must be at least 3:1.

**7.3.9. Data Tables (Core Styling Principles)**
*   **Purpose:** To display structured, tabular data in rows and columns.
*   **HTML/ARIA:** Use `<table>`, `<thead>`, `<tbody>`, `<tfoot>` (optional), `<tr>`. `<th>` for header cells (with `scope="col"` for column headers or `scope="row"` for row headers). `<td>` for data cells. A `<caption>` element should be used to provide a title for the table.
*   **Styling:**
    *   **Overall Table:** `width: 100%; border-collapse: collapse; /* Prevents double borders */`.
    *   **Cell Padding:** `padding: var(--space-sm) var(--space-md);` for both `<th>` and `<td>`.
    *   **Text Alignment:** Typically `text-align: left;` for `<th>` and `<td>` containing text. `text-align: right;` for cells containing numerical data.
    *   **Header Cells (`<th>`):**
        *   Background: `var(--bg-subtle)`.
        *   Text: `color: var(--text-primary); font-weight: var(--font-weight-semibold);`.
        *   Borders: `border-bottom: 2px solid var(--border-standard);`.
    *   **Body Rows (`<tr>`):**
        *   Borders: `border-bottom: 1px solid var(--border-divider);`.
        *   Striped Rows (optional for readability): `tbody tr:nth-child(odd) { background-color: var(--tk-gray-50); }`.
    *   **Data Cells (`<td>`):** `color: var(--text-secondary);`.
*   **States (if applicable):**
    *   **Hover (on row, optional):** `tbody tr:hover { background-color: var(--bg-hover-active); }`.
    *   **Sortable Headers:** If headers are clickable to sort, they should be styled like buttons (perhaps subtle outline or text buttons) and include a sort direction icon. They need clear hover and focus states.
*   **Thematic Adaptation:**
    *   **Dark/Purple Theme:** Header BG `var(--tk-gray-700)` or `var(--tk-indigo-800)`. Header Text `var(--text-primary)`. Row Borders `var(--tk-gray-700)`. Striped Rows `background-color: rgba(var(--tk-white-rgb), 0.03);` (subtle). Cell Text `var(--text-secondary)`.
    *   **Night Theme:** Header BG `var(--bg-input)`. Header Text `var(--text-primary)`. Row Borders `var(--border-standard)`. Striped Rows `background-color: rgba(var(--tk-black-rgb), 0.1);`. Cell Text `var(--text-secondary)`.
*   **Accessibility:** Proper use of `<th>` and `scope` attributes is crucial for screen reader users to understand table structure. Ensure sufficient contrast for text and borders. If the table is horizontally scrollable on small screens, provide clear visual cues and ensure it can be scrolled via keyboard (e.g., by making the container focusable with `tabindex="0"`).

**7.3.10. Date Pickers (Conceptual Styling Principles)**
*   **Purpose:** To allow users to select a single date or a date range, typically associated with an input field.
*   **Note:** Full date picker styling is complex and often handled by JavaScript libraries or native browser implementations. This provides conceptual guidance for applying TechnoKids styles if a custom or themeable picker is used.
*   **Styling (Conceptual for a custom pop-up picker):**
    *   **Container:** Styled like a modal or dropdown, using `background-color: var(--bg-card); box-shadow: var(--depth-shadow-lg); border-radius: var(--card-border-radius); padding: var(--space-md);`.
    *   **Header (Month/Year Navigation & Selection):**
        *   Buttons (Prev/Next Month): Styled as icon-only buttons (see Buttons 7.3.6), using `var(--text-secondary)` for icon fill.
        *   Month/Year Display/Selects: Text `var(--text-primary)`. If dropdowns, style like Select elements (7.3.12).
    *   **Day Grid (Table-like structure):**
        *   Day Headers (Mon, Tue, etc.): `color: var(--text-subtle); font-size: var(--font-size-sm);`.
        *   Day Cells: `padding: var(--space-xs); text-align: center;`.
        *   Day Numbers (Current Month): `color: var(--text-primary);`.
        *   Day Numbers (Previous/Next Month): `color: var(--text-subtle); opacity: 0.6;`.
        *   Selected Day(s): `background-color: var(--tk-purple-500); color: var(--text-on-dark-bg); border-radius: 50%;`.
        *   Today's Date (if not selected): `border: 1px solid var(--tk-purple-300); border-radius: 50%; color: var(--tk-purple-600);`.
        *   Hover/Focus on Day Cell (if interactive): `background-color: var(--tk-purple-100); color: var(--tk-purple-700); border-radius: 50%; cursor: pointer;`.
*   **Thematic Adaptation:** All color tokens adapt to the current theme.
    *   **Dark/Purple Theme:** Container BG `var(--tk-gray-800)`. Selected Day BG `var(--tk-violet-500)`, Text `var(--text-on-dark-bg)`. Today's Date Border `var(--tk-violet-300)`, Text `var(--tk-violet-300)`. Hover/Focus BG `var(--tk-purple-800)` or `rgba(var(--tk-violet-400-rgb), 0.15)`.
    *   **Night Theme:** Container BG `var(--bg-card)`. Selected Day BG `#9c80b1`, Text `var(--text-on-dark-bg)`.
*   **Accessibility:** Must be fully keyboard navigable (arrow keys to move between dates, PageUp/PageDown for months, etc.). Selected dates, current date, and focused date must be clearly indicated and announced by screen readers. Use ARIA roles for grid, cells, and button states as per APG.

**7.3.11. File Uploaders (Basic Styling Principles)**
*   **Purpose:** To allow users to select and upload files from their device.
*   **HTML/ARIA:** Native `<input type="file">` is often visually hidden and its functionality triggered by a custom-styled button. The list of selected files and upload progress should be clearly displayed and announced (e.g., using `aria-live` regions).
*   **Styling:**
    *   **"Choose File" Button:** Styled as `.button-secondary` or `.button-outline` (see 7.3.6). Label: "Choose File(s)", "Upload Document", etc.
    *   **Drop Zone (Optional):**
        *   A visually distinct area where users can drag and drop files.
        *   Border: `2px dashed var(--border-input)`.
        *   Background: `var(--bg-subtle)`.
        *   Text prompt (e.g., "Drag and drop files here or click to select"): `color: var(--text-subtle);`.
        *   Hover state (when dragging over): Border style/color changes (e.g., `border-color: var(--tk-purple-500); background-color: var(--tk-purple-50);`).
    *   **Selected File List (displayed after selection):**
        *   Typically an unordered list (`<ul>`). Each list item shows file name, optionally size, and upload progress/status.
        *   File Name Text: `var(--text-secondary)`.
        *   Progress Bars: See section 7.3.17.
        *   Remove/Cancel Icon/Button per file: Small icon-only button.
*   **Thematic Adaptation:** All elements use theme-appropriate tokens. Drop zone hover state in Dark/Purple theme could use `border-color: var(--tk-violet-400); background-color: rgba(var(--tk-violet-400-rgb), 0.1);`.
*   **Accessibility:** Ensure the visually hidden native file input is still part of the tab order if the custom button doesn't directly wrap it, or ensure its functionality is perfectly mirrored. Announce selected files, upload progress, and success/error states clearly.

**7.3.12. Forms & Input Fields (Text, Select)**
*   **Purpose:** To collect user data. All form inputs **must** have programmatically associated `<label>` elements (WCAG SC 1.3.1, SC 3.3.2).
*   **Text Input (`.depth-input`):**
    *   **HTML Structure:**
        ```html
        <div class="form-group">
          <label for="username" class="form-label">Username <span class="required-indicator">*</span></label>
          <input type="text" id="username" class="depth-input" placeholder="e.g., techkid123" aria-describedby="usernameHint usernameError">
          <div class="form-hint" id="usernameHint">Your unique username for login.</div>
          <div class="form-error-message" id="usernameError" role="alert" style="display:none;">Username is required.</div>
        </div>
        <style>.required-indicator { color: var(--semantic-error-base); margin-left: var(--space-xs); }</style>
        ```
    *   **CSS (Base Styles - Light Theme Defaults):**
        ```css
        .form-group {
          margin-bottom: var(--space-lg);
        }
        .form-label {
          display: block;
          font-weight: var(--font-weight-semibold);
          color: var(--text-primary);
          margin-bottom: var(--space-xs);
          font-size: var(--font-size-sm);
        }
        .depth-input {
          width: 100%;
          background-color: var(--bg-input);
          color: var(--text-primary);
          border: var(--depth-border-width) solid var(--border-input);
          border-radius: var(--input-border-radius);
          padding: var(--space-sm) var(--space-md);
          font-size: var(--font-size-md);
          line-height: var(--line-height-normal);
          box-shadow:
            var(--depth-shadow-inner),
            inset 0 1px 2px var(--depth-border-color-shadow);
          transition:
            border-color var(--transition-fast),
            box-shadow var(--transition-fast),
            background-color var(--transition-normal),
            color var(--transition-normal);
          box-sizing: border-box;
        }
        .depth-input::placeholder {
          color: var(--text-placeholder);
          opacity: 1;
        }
        .depth-input:focus-visible {
          border-color: var(--input-border-color-focus);
          box-shadow: var(--depth-shadow-sm), var(--input-focus-shadow-ring);
          outline: none;
        }
        .depth-input[aria-invalid='true'] {
          border-color: var(--semantic-error-color-border);
          background-color: var(--semantic-error-color-bg);
          /* color: var(--semantic-error-color-text); /* Optional: if text itself should be error colored */
        }
        .form-hint {
          font-size: var(--font-size-sm);
          margin-top: var(--space-xs);
          color: var(--text-subtle);
        }
        .form-error-message {
          display: block; /* Shown by JS */
          font-size: var(--font-size-sm);
          margin-top: var(--space-xs);
          color: var(--semantic-error-color-text);
          font-weight: var(--font-weight-semibold);
        }
        .depth-input[disabled],
        .depth-input[aria-disabled='true'] {
          background-color: var(--input-bg-disabled);
          color: var(--input-color-text-disabled);
          border-color: var(--input-border-color-disabled);
          cursor: not-allowed;
          box-shadow: var(--depth-shadow-inner);
        }
        ```
    *   **WCAG Contrast (Light Theme):** Default Border (`--tk-gray-400`) on Input BG (`--tk-white`): **3.0:1 (AA Non-Text)**. Placeholder Text (`--tk-gray-500`) on Input BG (`--tk-white`): **4.61:1 (AA Text)**.
    *   **"Dark Purple Driven" Adaptation:** Input BG `var(--tk-indigo-950)`. Text `var(--tk-gray-200)`. Border `var(--tk-gray-600)` (for contrast on Indigo-950). Placeholder `var(--tk-indigo-400)`. Focus border `var(--tk-violet-400)`. Error state uses dark theme semantic colors.
*   **Select Element (`<select>`):**
    *   Styled to visually match `.depth-input` using the same background, border, color, padding, and state variables.
    *   A custom dropdown arrow (SVG icon) is typically used for consistent appearance.
        *   Arrow `fill`: `var(--tk-gray-600)` (Light), `var(--tk-gray-300)` (Dark/Purple), `var(--text-secondary)` (Night).
    *   Disabled state should also mute the arrow color and apply disabled input styles.
    *   Native `<select>` styling is notoriously difficult; often requires significant CSS overrides or custom JavaScript-based solutions for full control. Focus on ensuring the custom appearance is accessible.

**7.3.13. Icons (Informational vs. Decorative)**
*   **Purpose:** To visually communicate actions, status, or concepts, often supplementing text.
*   **Styling:**
    *   Typically SVG icons for scalability and CSS control.
    *   Default Color: `fill: currentColor;` to inherit the text color of their parent element. This is highly flexible.
    *   Specific Colors:
        *   Informational (standalone or with subtle text): `fill: var(--text-secondary);` or `var(--text-subtle)`.
        *   Interactive (within buttons, links): `fill: currentColor;` (inherits button/link text color).
        *   Semantic (as part of alerts, validation): `fill: var(--semantic-*-color-icon);` (e.g., `var(--semantic-error-color-icon)`).
*   **Thematic Adaptation:** `currentColor` handles most theme changes automatically. Specific icon colors adapt with their corresponding text or semantic tokens.
    *   **Dark/Purple Theme Informational:** `fill: var(--tk-gray-300);` or `var(--tk-purple-300)`.
*   **Accessibility:**
    *   **Informational Icons (convey meaning):** If an icon conveys information not available in visible text, it MUST have a text alternative (e.g., visually hidden text via `<span>` with appropriate CSS, or `aria-label` on the icon's container if it's interactive, or `title` attribute though support is inconsistent).
    *   **Decorative Icons (add no new information or are redundant with text):** Should be hidden from assistive technologies: `aria-hidden="true"`.
    *   **WCAG Non-Text Contrast (SC 1.4.11):** If an icon is essential for understanding content or functionality and is not accompanied by a visible text label that conveys the same meaning, its primary visual strokes/shapes need a 3:1 contrast ratio with its immediate background.

**7.3.14. Modals & Dialogs (Focus Trapping Considerations)**
*   **Purpose:** To display focused content or tasks that interrupt the main user flow, requiring user interaction before returning to the underlying page.
*   **HTML/ARIA:** Use `<dialog>` element or a `div` with `role="dialog"`, `aria-modal="true"`, `aria-labelledby` (pointing to the modal title's ID), and `aria-describedby` (pointing to the modal content's ID if applicable).
*   **Styling:**
    *   **Overlay:** `position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: var(--bg-overlay); z-index: 1000; /* High z-index */`.
    *   **Modal Container:**
        *   Positioned typically centered on screen.
        *   Background: `var(--modal-bg-color)` (which is `var(--bg-card)`).
        *   Shadow: `var(--depth-shadow-xl)` or `var(--depth-shadow-2xl)`.
        *   Border radius: `var(--card-border-radius)`.
        *   Padding: `var(--card-padding)`.
    *   **Header/Title:** `font-size: var(--font-size-h4); color: var(--text-primary); margin-bottom: var(--space-md);`.
    *   **Content Area:** `color: var(--text-secondary);`.
    *   **Footer (for actions):** Padding `var(--space-md)`. Often uses flexbox to align buttons.
    *   **Close Button:** Typically an icon-only button (X icon) positioned in a corner. `fill: var(--text-subtle);`. Hover: `fill: var(--text-primary);`. Needs accessible label (e.g., `aria-label="Close dialog"`).
*   **Thematic Adaptation:**
    *   **Dark/Purple Theme:** Overlay `var(--bg-overlay)` (dark theme override, e.g., `rgba(17,24,39,0.8)`). Modal BG `var(--modal-bg-color)` (Dark theme: `--tk-gray-800`). For "Dark Purple Driven": could be `var(--tk-indigo-950)` or `var(--tk-purple-900)`. All text and button colors within adapt to the dark theme.
    *   **Night Theme:** Uses Night theme variables for overlay, modal BG, text, etc.
*   **Accessibility:**
    *   **Focus Trapping:** Keyboard focus **MUST** be trapped within the modal while it is open. Users should not be able to Tab to elements behind the modal.
    *   **Dismissal:** Provide clear ways to close (e.g., Close button, Esc key).
    *   Initial focus should be set to an appropriate element within the modal when it opens (often the first interactive element or the modal container itself).
    *   When closed, focus should return to the element that triggered the modal.

**7.3.15. Navigation (Main Menus, Side Navigation)**
*   **Purpose:** To provide primary site navigation pathways.
*   **HTML/ARIA:** `<nav>` element with an `aria-label` (e.g., "Main navigation", "Secondary navigation"). Navigation links are typically an unordered list (`<ul>`) of links (`<a>`). The link corresponding to the current page should have `aria-current="page"`. For dropdown submenus, the trigger element needs `aria-haspopup="true"` and `aria-expanded="true/false"`.
*   **Styling (Horizontal Main Menu / Header Example):**
    *   Container Background: `var(--bg-sidebar)` (for a light header) or `var(--gradient-primary-brand)` for a feature header.
    *   Link Item Padding: `padding: var(--space-sm) var(--space-md);`.
    *   Link Text: `color: var(--text-link);` (if on light BG) or `color: var(--text-on-dark-bg);` (if on dark/gradient BG). `text-decoration: none;`.
    *   Active/Current Link: `color: var(--text-primary); font-weight: var(--font-weight-bold);` and often a visual indicator like `border-bottom: 3px solid var(--border-interactive-strong);` or a contrasting background on the item.
*   **Styling (Vertical Side Navigation Example):**
    *   Container Background: `var(--bg-sidebar)`.
    *   Link Item Padding: `padding: var(--space-md); display: block;`.
    *   Link Text: `color: var(--text-link);`.
    *   Active/Current Link: `background-color: var(--bg-hover-active); color: var(--text-primary); font-weight: var(--font-weight-semibold); border-left: 4px solid var(--border-interactive-strong); padding-left: calc(var(--space-md) - 4px); /* Adjust padding for border */`.
*   **States (for individual navigation links):**
    *   **Hover:** `background-color: var(--bg-hover-active); color: var(--text-link-hover);`.
    *   **Focus-Visible:** Standard focus ring (`var(--border-focus-ring-color)`) around the link.
    *   **Active (Pressed, if applicable):** May use a slightly darker background.
*   **Thematic Adaptation:** All color tokens adapt based on the active theme.
    *   **Dark/Purple Theme (Header Example):** BG `var(--tk-purple-900)`. Link Text `var(--tk-purple-300)`. Active Link `color: var(--tk-white); border-bottom-color: var(--tk-violet-400);`.
    *   **Dark/Purple Theme (Side Nav Example):** BG `var(--tk-gray-800)`. Link Text `var(--tk-purple-300)`. Active Link BG `rgba(var(--tk-purple-400-rgb), 0.15)`, `color: var(--tk-white)`, `border-left-color: var(--tk-violet-400)`.
*   **Accessibility:** Fully keyboard navigable (Tab to links, Enter to activate). Dropdown menus must be properly implemented for keyboard and screen reader users (see ARIA APG for menu patterns). `aria-current="page"` is essential.

**7.3.16. Pagination Controls**
*   **Purpose:** To navigate between multiple pages of a list of content (e.g., search results, blog posts).
*   **HTML/ARIA:** Typically a `<nav>` element with `aria-label="Pagination"`. Contains an ordered list (`<ol>`) of links or buttons. "Previous" and "Next" links/buttons are common. The current page item should be marked with `aria-current="page"` and visually distinct. Disabled links/buttons (e.g., "Previous" on page 1) should use `aria-disabled="true"` or be actual `disabled` buttons.
*   **Styling:**
    *   **Container:** `display: flex; justify-content: center; align-items: center; gap: var(--space-xs);`.
    *   **Page Link/Button:** Styled like a small `.button-outline` (see 7.3.6) but with reduced padding (e.g., `padding: var(--space-xs) var(--space-sm);`). Ensure `min-width` and `min-height` for adequate touch target size (e.g., `36px`).
    *   **Current Page Item:** `background-color: var(--tk-purple-500); color: var(--text-on-dark-bg); border-color: var(--tk-purple-500); font-weight: var(--font-weight-bold); cursor: default;`.
    *   **Disabled Link/Button:** Use disabled button styles (`var(--button-disabled-bg-color)`, etc.). `pointer-events: none;`.
    *   **Ellipsis (if used for truncation):** `color: var(--text-subtle); padding: var(--space-xs) var(--space-sm);`.
*   **Thematic Adaptation:**
    *   **Dark/Purple Theme:** Current Page BG `var(--tk-violet-500)`, Text `var(--text-on-dark-bg)`, Border `var(--tk-violet-500)`. Outline buttons use dark theme variables. Disabled states use dark theme disabled colors.
    *   **Night Theme:** Current Page BG `#9c80b1`, Text `var(--text-on-dark-bg)`.
*   **Accessibility:** Clear visual indication of the current page. Disabled states must be clear. All active controls must be keyboard navigable and operable.

**7.3.17. Progress Bars & Spinners (Loaders)**
*   **Progress Bars:**
    *   **Purpose:** To indicate the completion status of a determinate task (e.g., file upload, form submission steps).
    *   **HTML/ARIA:** Native `<progress value="70" max="100"></progress>`. For custom `div`-based progress bars: `role="progressbar"`, `aria-valuenow="70"`, `aria-valuemin="0"`, `aria-valuemax="100"`. If the value is not easily discernible visually, provide text (e.g., "70% complete").
    *   **Styling:**
        *   **Track (Container):** `height: var(--space-sm); background-color: var(--tk-gray-200); border-radius: var(--space-sm); overflow: hidden;`.
        *   **Fill (Progress):** `height: 100%; background-color: var(--tk-purple-500); width: /* percentage from aria-valuenow */; transition: width var(--transition-fast);`. For task completion, can use `var(--tk-green-500)`.
    *   **Thematic Adaptation:** Track BG `var(--tk-gray-700)` (Dark/Purple), `var(--bg-input)` (Night). Fill `var(--tk-violet-500)` (Dark/Purple), `#9c80b1` (Night).
*   **Spinners (Loaders - Indeterminate Progress):**
    *   **Purpose:** To indicate that an operation is in progress but its completion time is unknown.
    *   **HTML/ARIA:** A `div` with `role="status"` and visually hidden text (e.g., `<span class="visually-hidden">Loading content...</span>`).
    *   **Styling (Example CSS Spinner):**
        ```css
        .spinner {
          width: var(--space-xl);
          height: var(--space-xl);
          border: 4px solid var(--tk-gray-200); /* Light track */
          border-top-color: var(--tk-purple-500); /* Active spinner color */
          border-radius: 50%;
          animation: spin 0.8s linear infinite;
        }
        @keyframes spin {
          to {
            transform: rotate(360deg);
          }
        }
        ```
    *   **Thematic Adaptation:** Light track `var(--tk-gray-700)` (Dark/Purple), `var(--bg-input)` (Night). Active spinner color `var(--tk-violet-400)` (Dark/Purple), `#9c80b1` (Night).
*   **Accessibility:** For progress bars, ensure `aria-valuenow` is updated. For spinners, the visually hidden text provides context to screen reader users. All animations must respect `prefers-reduced-motion` (spinner animation should stop or be replaced with a static "loading" icon).

**7.3.18. Sliders / Range Inputs**
*   **Purpose:** To allow users to select a single value from a continuous or stepped range.
*   **HTML/ARIA:** Native `<input type="range">`. For custom sliders, use `role="slider"`, `aria-valuenow`, `aria-valuemin`, `aria-valuemax`, `aria-orientation` (horizontal/vertical), and `aria-label` or `aria-labelledby`.
*   **Styling (Conceptual for Custom Horizontal Slider):**
    *   **Track Container:** `position: relative; height: var(--space-xl); /* For thumb interaction area */`.
    *   **Track Visual:** `height: var(--space-xs); background-color: var(--tk-gray-300); border-radius: var(--space-xs); position: absolute; top: 50%; transform: translateY(-50%); width: 100%;`.
    *   **Filled Track (Active Range):** `height: 100%; background-color: var(--tk-purple-500); border-radius: var(--space-xs); width: /* percentage based on value */;`.
    *   **Thumb:** `width: var(--space-md); height: var(--space-md); background-color: var(--tk-purple-600); border-radius: 50%; box-shadow: var(--depth-shadow-sm); position: absolute; top: 50%; transform: translate(-50%, -50%); cursor: grab;`.
*   **States (Thumb):**
    *   **Hover:** `background-color: var(--tk-purple-700); box-shadow: var(--depth-shadow-md);`.
    *   **Focus-Visible:** Standard focus ring (`var(--border-focus-ring-color)`) around the thumb.
    *   **Active (Dragging):** `background-color: var(--tk-purple-700); cursor: grabbing;`.
*   **Thematic Adaptation:**
    *   **Dark/Purple Theme:** Track Visual BG `var(--tk-gray-700)`. Filled Track/Thumb BG `var(--tk-violet-500)`.
    *   **Night Theme:** Track Visual BG `var(--bg-input)`. Filled Track/Thumb BG `#9c80b1`.
*   **Accessibility:** Fully keyboard navigable (Left/Right arrow keys to adjust value, Home/End for min/max). Value changes should be announced by screen readers if not visually apparent.

**7.3.19. Tabs**
*   **Purpose:** To organize content into discrete sections that can be switched by the user, displaying one section at a time.
*   **HTML/ARIA:** A container with `role="tablist"`. Each tab control is a `<button role="tab">` (or `<a>` if tabs link to different URLs, though button is common for in-page content switching). The active tab has `aria-selected="true"`. Each tab controls a panel with `role="tabpanel"`, linked via `aria-controls` (on tab) and `aria-labelledby` (on panel). Inactive tab panels should be hidden (e.g., `display: none;` or `hidden` attribute).
*   **Styling (Tab Buttons within Tablist):**
    *   `display: inline-flex; padding: var(--space-sm) var(--space-md); cursor: pointer; border: none; background: transparent; border-bottom: 3px solid transparent; /* Inactive state border */`.
    *   Inactive Text: `color: var(--text-link);`.
    *   Active Tab: `color: var(--text-primary); font-weight: var(--font-weight-bold); border-bottom-color: var(--border-interactive-strong); /* e.g., --tk-purple-500 */`.
    *   Tab Panel (`.tabpanel`): `padding: var(--space-lg); border: 1px solid var(--border-standard); border-top: none; /* If tabs are above */`.
*   **States (Tab Buttons):**
    *   **Hover (Inactive Tab):** `color: var(--text-link-hover); background-color: var(--bg-hover-active);`.
    *   **Focus-Visible:** Standard focus ring on the tab button.
*   **Thematic Adaptation:**
    *   **Dark/Purple Theme:** Inactive Text `var(--tk-purple-300)`. Active Text `var(--tk-white)`. Active Indicator `var(--tk-violet-400)`. Tab Panel Border `var(--tk-gray-700)`.
    *   **Night Theme:** Inactive Text `var(--text-link)`. Active Text `var(--text-primary)`. Active Indicator `var(--text-link)`.
*   **Accessibility:** Keyboard navigable: Left/Right arrow keys should move focus between tabs in the tablist (and optionally activate them, or require Enter/Space). Tab key moves focus out of the tablist to the active tab panel content (if any focusable elements exist there).

**7.3.20. Toggle Buttons (`.depth-toggle`)**
*   **Purpose:** To switch between two states (on/off, enabled/disabled), typically for a single setting.
*   **HTML/ARIA:** A `<button>` element with `role="switch"` and `aria-checked="true/false"`. The button's accessible name should describe what it toggles.
*   **Styling (Custom Switch Visual):**
    *   **Track:** `width: calc(2 * var(--space-xl)); height: var(--space-xl); background-color: var(--tk-gray-300); border-radius: var(--space-xl); position: relative; transition: background-color var(--transition-fast);`.
    *   **Knob:** `width: calc(var(--space-xl) - var(--space-xs)); height: calc(var(--space-xl) - var(--space-xs)); background-color: var(--tk-white); border-radius: 50%; box-shadow: var(--depth-shadow-sm); position: absolute; top: calc(var(--space-xs) / 2); left: calc(var(--space-xs) / 2); transition: transform var(--transition-fast);`.
    *   **Checked State:**
        *   Track: `background-color: var(--tk-purple-500);`.
        *   Knob: `transform: translateX(var(--space-xl));`.
*   **States (Button itself):**
    *   **Focus-Visible:** Standard focus ring around the entire toggle (track).
*   **Thematic Adaptation:**
    *   **Dark/Purple Theme:** Unchecked Track BG `var(--tk-gray-700)`. Checked Track BG `var(--tk-violet-500)`. Knob BG `var(--tk-gray-300)` (or `var(--tk-white)` if preferred for higher knob contrast).
    *   **Night Theme:** Unchecked Track BG `var(--bg-input)`. Checked Track BG `#9c80b1`. Knob BG `var(--text-primary)`.
*   **Accessibility:** State change (aria-checked) must be updated on interaction. Visual state must match programmatic state. Keyboard operable with Space/Enter. Non-text contrast for knob vs. track and track vs. page background is important.

**7.3.21. Toolbars**
*   **Purpose:** To group a set of related controls or actions, often for a specific context (e.g., text editor controls, table actions).
*   **HTML/ARIA:** A `<div>` with `role="toolbar"` and an `aria-label` describing its purpose. Contains interactive elements like buttons (often icon-only), toggle buttons, select dropdowns for fonts, etc.
*   **Styling:**
    *   **Container:** `display: flex; align-items: center; background-color: var(--bg-subtle); padding: var(--space-sm); border-radius: var(--card-border-radius); gap: var(--space-xs); box-shadow: var(--depth-shadow-sm);`.
    *   **Controls within Toolbar:**
        *   Buttons: Often use a smaller variant or icon-only style. Padding might be `var(--space-xs)`.
        *   Separators (optional): `width: 1px; height: var(--space-lg); background-color: var(--border-divider); margin: 0 var(--space-xs);`.
*   **Thematic Adaptation:** Toolbar BG `var(--tk-gray-800)` (Dark/Purple), `var(--bg-input)` (Night). Controls within adapt using their respective theme variables.
*   **Accessibility:** Keyboard navigation *within* the toolbar is typically handled with arrow keys (Left/Right, sometimes Up/Down for vertical toolbars). Tab key moves focus into and out of the toolbar. All controls must have accessible names.

**7.3.22. Tooltips**
*   **Purpose:** To provide contextual, non-essential information that appears on hover or focus of an element.
*   **HTML/ARIA:** The element triggering the tooltip should have `aria-describedby` pointing to the `id` of the tooltip content. The tooltip itself should have `role="tooltip"`. Tooltips are typically shown/hidden using JavaScript.
*   **Styling:**
    *   **Container:** `position: absolute; /* Positioned relative to trigger */ z-index: 1010; /* Above most content */`.
    *   Background: `var(--tk-gray-900)` (for high contrast with light text, default).
    *   Text: `color: var(--tk-white);`.
    *   Padding: `var(--space-xs) var(--space-sm);`.
    *   Border radius: `var(--input-border-radius)`.
    *   Font size: `var(--font-size-sm)`.
    *   Shadow: `var(--depth-shadow-md)`.
    *   Optional Pointer/Arrow: Created using CSS borders on a pseudo-element.
*   **Thematic Adaptation:**
    *   **Dark/Purple/Night Themes:** While dark BG/light text is a common default for tooltips for good contrast, if the tooltip appears over a very dark surface, it might need to invert: BG `var(--tk-gray-100)` with Text `var(--tk-gray-900)`. The key is ensuring the tooltip text has high contrast against its *own* background, and the tooltip as a whole is distinguishable from what's beneath it.
*   **Accessibility:**
    *   Tooltips must be dismissible (e.g., on Esc key, or by moving focus/hover away from the trigger).
    *   They should not obscure other essential content.
    *   If triggered by hover, they **must** also be triggerable by keyboard focus on the same element.
    *   Avoid auto-hiding tooltips too quickly, giving users enough time to read.
    *   Content within tooltips should generally not be interactive itself. If it needs to be, consider a popover/disclosure widget instead.

**7.3.23. TechnoKids Specific: Session & Thematic Elements**
*   **Purpose:** Visual cues related to specific TechnoKids learning sessions or thematic content groupings.
*   **Styling:**
    *   **Session Number Backgrounds (Light Theme):** Uses predefined gradients like `var(--gradient-session-1-num-bg)`. Text `var(--text-on-dark-bg)`.
    *   **Session Content Area Backgrounds (Light Theme):** May use light brand tints (e.g., `--session-1-bg: var(--tk-purple-100); --session-2-bg: var(--tk-blue-100);`). Text within uses `var(--text-primary)` or darker brand shades for headings (e.g., `var(--tk-purple-700)`).
    *   **Assignment/Extension Card Gradients (Light Theme):** `var(--gradient-assignment-card-light)`, `var(--gradient-extension-card-light)`.
*   **Thematic Adaptation:**
    *   **Dark/Purple Theme:** Session Number Backgrounds use darker, richer gradients (e.g., `linear-gradient(to right, var(--tk-purple-700), var(--tk-indigo-800))`). Session Content Area BGs use translucent dark brand colors (e.g., `background-color: rgba(var(--tk-purple-900-rgb), 0.5);`). Text within uses light colors (`var(--text-on-dark-bg)`, `var(--tk-purple-300)`). Assignment/Extension Card Gradients use `var(--gradient-assignment-card-dark)`, etc.
    *   **Night Theme:** Uses desaturated, warmer versions of brand colors or specific Night theme neutrals for backgrounds. Text ensures contrast.
*   **Accessibility:** Ensure sufficient text contrast on all thematic backgrounds.

**7.3.24. TechnoKids Specific: Logo & Feature Items (`.depth-logo-item`, `.depth-feature`)**
*   **`.depth-logo-item` (e.g., for displaying partner logos or small feature icons with text):**
    *   Styling: Typically styled like a small card or a padded content box.
    *   Light Theme: `background-color: var(--bg-card)`. Text: `var(--text-primary)`.
    *   Dark/Purple Theme: `background-color: var(--tk-indigo-900)` or `var(--tk-purple-800)`. Text: `var(--text-on-dark-bg)`.
    *   Night Theme: `background-color: var(--bg-card)`.
*   **`.depth-feature` (e.g., for highlighting key product features):**
    *   Styling: Designed to be visually prominent, often using strong brand colors or gradients.
    *   Light Theme: `background: var(--overlay-depth-gradient), var(--gradient-primary-brand); color: var(--text-on-dark-bg); padding: var(--space-lg); border-radius: var(--card-border-radius); box-shadow: var(--depth-shadow-lg);`.
    *   Dark/Purple Theme: Can retain its vibrant primary gradient for contrast, or for a more integrated "dark purple" feel, it could use:
        *   `background: var(--overlay-depth-gradient), var(--gradient-dark-purple-hero);`
        *   `background: var(--overlay-depth-gradient), var(--gradient-violet-accent);`
        *   Text color remains `var(--text-on-dark-bg)`. Contrast must be verified if the chosen gradient is lighter.
    *   Night Theme: Uses desaturated brand gradients (e.g., `linear-gradient(to right, #8a6f9e, #6e5e8e)`).
*   **Accessibility:** All text must meet contrast. If items are interactive, they need appropriate states.

#### 7.4. *Conceptual Components (Principles for Future Implementation - e.g., Carousels)*
For components not exhaustively detailed above (e.g., Carousels/Image Sliders, complex multi-step wizards, tree views), the following principles apply:
*   **ARIA Patterns:** Adhere strictly to WAI-ARIA Authoring Practices Guide (APG) for structure, states, properties, and keyboard interaction patterns. Carousels, for example, need careful attention to `aria-live` regions for announcements, play/pause/stop controls, and accessible navigation for slides (e.g., tabbed slide selectors, next/prev buttons).
*   **Color & Theming:** Consistently apply TechnoKids color tokens (Section 4) for backgrounds, text, borders, and interactive states, ensuring thematic adaptability across Light, Dark/Purple, and Night modes.
*   **Interactivity & States:** All interactive parts (buttons, links, draggable elements) must have clear default, hover, focus-visible, active, and disabled states using the defined system (Section 7.2).
*   **Typography & Spacing:** Utilize the established typographic scale (Section 6) and spacing units (Section 5) for consistency and readability.
*   **Accessibility:** Prioritize keyboard navigation, focus management (including trapping focus where appropriate), clear screen reader announcements for state changes and content updates, and full respect for `prefers-reduced-motion` (Section 9.5). For carousels, provide mechanisms to pause, stop, or hide auto-rotation (WCAG SC 2.2.2).

---

### 8. Depth, Shadows, & Layering Effects

Visual depth and layering enhance UI hierarchy, draw attention to interactive elements, and contribute to a modern aesthetic. This section details the strategic and consistent use of shadows and layering techniques across all themes.

#### 8.1. Strategic Use of Depth for Hierarchy
*   **Elevation & Prominence:** Elements that are conceptually "closer" to the user or require more attention (e.g., modals, dropdown menus, active/primary buttons) use stronger, more diffuse shadows (`var(--depth-shadow-lg)`, `var(--depth-shadow-xl)`, `var(--depth-shadow-2xl)`) to appear elevated from the page.
*   **Interactivity Cues:** Interactive elements often gain a subtle shadow lift on hover (e.g., transitioning from `var(--depth-shadow-md)` to `var(--depth-shadow-lg)`) to indicate affordance and responsiveness.
*   **Containment & Separation:** Cards, content panels, and distinct UI sections use shadows (typically `var(--depth-shadow-md)` or `var(--depth-shadow-sm)`) to define their boundaries and visually separate them from the underlying background, aiding in content organization.
*   **Inset Effects:** Inner shadows (`var(--depth-shadow-inner)`) are used for specific states like pressed buttons or to give input fields a subtle recessed appearance.

#### 8.2. Standard & Interactive Shadows
The `--depth-shadow-*` variables provide a consistent and scalable system for applying shadows. These variables are redefined for Dark and Night themes (see Section 4.4) to ensure they render appropriately on darker backgrounds—typically meaning the shadow color itself becomes less dark (or even a very dark opaque color rather than translucent black) or more diffuse to be perceivable.

*   **Light Theme Shadow Appearance:** Shadows are typically soft, translucent blacks (`rgba(0,0,0, opacity)`).
*   **Dark/Night Theme Shadow Appearance:**
    *   Shadows are often more subtle as they have less contrast against dark backgrounds.
    *   The color of the shadow might shift from pure black to a very dark gray or a desaturated version of the background color to appear more natural.
    *   Alternatively, depth in dark themes can also be conveyed by slightly lighter surface colors for elevated elements, rather than relying solely on shadows. The TechnoKids system uses darker, more opaque black shadows for dark themes to give some perceivable depth.

**CSS Application:**```css
.card-example {
  box-shadow: var(--depth-shadow-md);
  transition: box-shadow var(--transition-fast);
}
.card-example:hover {
  box-shadow: var(--depth-shadow-lg);
}
.button-pressed-example {
  box-shadow: var(--depth-shadow-inner);
}
```

#### 8.3. Decorative Background Elements (Blobs, Shapes)
Subtle, low-opacity, blurred shapes or gradients can be used in backgrounds to add visual interest, texture, and a sense of depth without distracting from primary content or interactive elements.
*   **Implementation:** Typically achieved using absolutely positioned pseudo-elements (`::before`, `::after`) or dedicated `div` elements with `z-index: -1;` (or a low positive z-index if behind specific content but above a main background).
*   **Color & Appearance:**
    *   Use `var(--gradient-decorative-blob)` which is defined with low-alpha brand colors (`--decorative-blob-color-start`, `--decorative-blob-color-end`).
    *   Opacity is often controlled within the RGBA definition of these color variables or by applying an additional `opacity` property to the element.
    *   A heavy `filter: blur(VALUEpx);` (e.g., `blur(60px)` or more) is applied to create a soft, ethereal effect.
    *   **Thematic Adaptation:** The base colors for these blobs (`--decorative-blob-color-start`, `--decorative-blob-color-end`) are defined globally. In Dark/Night themes, their perceived intensity will change. If more distinct blob colors are needed for dark themes, theme-specific blob color variables could be introduced (as shown in Night theme overrides in Section 4.4).
    ```css
    .section-with-blob::before {
      content: '';
      position: absolute;
      top: -10%;
      left: -10%; /* Example positioning */
      width: clamp(300px, 50vw, 600px); /* Responsive size */
      height: clamp(300px, 50vw, 600px);
      background-image: var(--gradient-decorative-blob);
      opacity: 0.1; /* Further adjust overall opacity, can be theme-dependent */
      filter: blur(70px);
      z-index: -1;
      pointer-events: none;
    }
    ```

#### 8.4. Overlapping Elements
Creating visual depth and hierarchy through overlapping elements is a common modern UI technique.
*   **Implementation:** Achieved using CSS positioning properties (`position: relative;` on a parent container, and `position: absolute;` or `position: fixed;` for child elements that overlap). The `z-index` property is crucial for controlling the stacking order of these layered elements. Negative margins can also be used carefully to create overlap.
*   **Purpose:**
    *   Creates visual interest and dynamism.
    *   Reinforces relationships between elements (e.g., an image thumbnail peeking out from under a card's header).
    *   Can guide the user's eye to focal points.
*   **Accessibility Considerations:**
    *   **Logical Content Order:** Ensure the reading order in the DOM remains logical and comprehensible for screen reader users, even if elements are visually repositioned.
    *   **No Obscuring:** Overlapping elements must not obscure essential information or interactive controls for any users, including those navigating with keyboards or using screen magnification.
    *   **Focus Management:** If overlapping creates distinct layers (like a dropdown menu appearing over content), ensure keyboard focus is managed correctly (e.g., focus moves into the dropdown and is contained if necessary).

---

### 9. Animations, Micro-interactions, & Motion

Animations and micro-interactions are used thoughtfully within the TechnoKids design system to enhance user experience, provide clear visual feedback, guide attention, and add a touch of brand personality, without being distracting or causing accessibility issues.

#### 9.1. Purposeful Motion
All motion introduced into the interface should have a clear and beneficial purpose:
*   **Feedback & Confirmation:** Animations can confirm user actions (e.g., a button press effect, success/error state transitions, item added to a list).
*   **State Changes:** Smoothly transition UI elements between different states (e.g., expanding/collapsing accordions, opening/closing modals, tab switches), making these changes easier to follow.
*   **Guidance & Attention:** Subtly direct the user's attention to important information, new content, or the next logical step in a flow.
*   **Hierarchy & Spatial Relationships:** Animations can help establish or clarify spatial relationships between UI elements (e.g., a panel sliding in from the side).
*   **Delight & Brand Expression (Used Sparingly):** Subtle, engaging animations can enhance the brand experience and make interactions more enjoyable, but these should never impede usability or become intrusive.

#### 9.2. Hover Effects & Transforms
*   **Buttons & Interactive Cards:** On hover (`:hover`), elements like buttons and cards may utilize `transform: var(--depth-transform-hover);` (e.g., `translateY(-2px)`) and a change in `box-shadow` (e.g., from `var(--depth-shadow-md)` to `var(--depth-shadow-lg)`) to create a subtle "lift" effect, indicating interactivity.
*   **Links:** Typically receive an underline and a color change to `var(--text-link-hover)` on hover and focus.
*   **Transitions:** All property changes for hover, focus, and active states should use smooth CSS transitions, typically `var(--transition-fast)` or `var(--transition-normal)`.
    ```css
    .interactive-element {
      /* ... other styles ... */
      transition: var(--transition-default); /* Applies to transform, box-shadow, background-color etc. */
    }
    .interactive-element:hover {
      transform: var(--depth-transform-hover);
      box-shadow: var(--depth-shadow-lg);
      /* Potentially other changes like background-color */
    }
    ```

#### 9.3. Scroll-Triggered Animations (Considerations)
Subtle fade-ins or slide-ins for content elements as they enter the viewport during scrolling can enhance engagement and guide the user through long pages.
*   **Implementation:** Best implemented using JavaScript with the Intersection Observer API to efficiently detect when elements become visible, then adding a class (e.g., `.is-visible`) to trigger CSS animations/transitions.
    ```css
    .animate-on-scroll {
      opacity: 0;
      transform: translateY(var(--space-md)); /* Start slightly offset (e.g., 16px down) */
      transition:
        opacity 0.5s var(--transition-timing-function),
        transform 0.5s var(--transition-timing-function);
      will-change: opacity, transform; /* Hint to browser for performance */
    }
    .animate-on-scroll.is-visible {
      opacity: 1;
      transform: translateY(0);
    }
    ```
*   **Accessibility (`prefers-reduced-motion`):** These animations **must** be disabled or significantly reduced if the user has indicated a preference for reduced motion (see Section 9.5).

#### 9.4. Loading Indicators & UI Feedback (General Principles)
*(Specific examples for Progress Bars & Spinners are in Section 7.3.17)*
*   **Purpose:** To inform users that the system is processing their request or loading content, managing expectations and reducing perceived wait times.
*   **Types:**
    *   **Determinate:** Used when progress can be tracked (e.g., file uploads, multi-step processes). Progress Bars are suitable.
    *   **Indeterminate:** Used when progress cannot be precisely tracked (e.g., fetching data, initial page load). Spinners or pulsing animations are common.
*   **Visuals:** Should be subtle yet noticeable, and aligned with the TechnoKids brand (e.g., using brand accent colors like `var(--tk-purple-500)` or `var(--tk-violet-400)` for active parts of loaders).
*   **Placement:** Should be contextually relevant, appearing near the element or area that is loading. For full-page loads, a more central indicator might be used.
*   **Skeleton Screens:** For complex content areas or cards, using skeleton screens (simplified placeholder layouts that mimic the structure of the content before it loads) can significantly improve perceived performance.
*   **Accessibility:**
    *   Loading states must be clearly announced to screen reader users (e.g., using `aria-live="polite"` or `aria-live="assertive"` regions with descriptive text like "Loading content...", "Submitting form...").
    *   Ensure animations respect `prefers-reduced-motion`.
    *   If loading blocks interaction, this should be clear (e.g., disabled state on a submit button while processing).

#### 9.5. `prefers-reduced-motion` Implementation
It is **critical** to respect user preferences for reduced motion to prevent discomfort, distraction, or potential health issues (e.g., for users with vestibular disorders or photosensitivity) (WCAG SC 2.2.2 Pause, Stop, Hide; SC 2.3.3 Animation from Interactions).
*   **CSS Media Query:** Implement system-wide or component-specific overrides.
    ```css
    @media (prefers-reduced-motion: reduce) {
      /* Global reset for transitions and animations */
      *,
      *::before,
      *::after {
        animation-duration: 0.01ms !important; /* Effectively freeze keyframe animations */
        animation-iteration-count: 1 !important; /* Run only once if not frozen */
        transition-duration: 0.01ms !important; /* Make transitions instant */
        scroll-behavior: auto !important; /* Disable smooth scrolling */
      }

      /* Specific component overrides */
      .depth-float-animation { /* Example: a continuously floating element */
        animation: none;
      }
      .animate-on-scroll { /* Ensure elements are visible without animation */
        opacity: 1;
        transform: translateY(0);
      }
      .depth-button:hover,
      .depth-card:hover { /* Disable hover transforms that cause movement */
        transform: none;
        /* Note: Box-shadow changes on hover might still be acceptable if not too distracting */
      }
      .spinner { /* Replace spin with a static indicator or very subtle pulse if any */
        animation: none;
        /* border-top-color: var(--tk-gray-400); /* Make it look static */
      }
      .accordion-icon-animated { /* Example for an animated chevron in accordion */
        transition-property: none; /* Or set specific non-motion property like color */
      }
    }
    ```
*   **JavaScript-Controlled Animations:** If animations are triggered or controlled by JavaScript, the script should check the media query result before applying motion:
    ```javascript
    const motionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    function handleAnimation(element) {
      if (!motionQuery.matches) {
        // Apply full animation
        element.classList.add('full-animation-effect');
        element.classList.remove('reduced-animation-effect');
      } else {
        // Apply reduced or no animation
        element.classList.add('reduced-animation-effect');
        element.classList.remove('full-animation-effect');
      }
    }
    // Call handleAnimation(myElement) when needed
    // Listen for changes if the user changes their OS preference while on the page
    // motionQuery.addEventListener('change', () => handleAnimation(myElement)); // Be careful with scope
    ```
*   **Flashing Content (WCAG SC 2.3.1 Three Flashes or Below Threshold):** Web pages must not contain anything that flashes more than three times in any one-second period, or the flash must be below the general flash and red flash thresholds. This is critical to prevent seizures. All animations, especially those involving color changes or rapid blinking, must adhere to this.

---

### 10. Theming: Light, Dark, & Night Modes

The TechnoKids design system offers robust theming capabilities to cater to diverse user preferences, varying ambient lighting conditions, and specific accessibility needs, thereby enhancing usability and comfort. This section recaps the theming strategy and emphasizes considerations for maintaining accessibility and brand consistency across themes.

#### 10.1. Theming Strategy & User Choice
*   **Light Mode (Default `[data-theme="light"]`):** This is the primary and most vibrant theme, optimized for well-lit environments. It features light backgrounds (e.g., `var(--bg-page): --tk-gray-50;`, `var(--bg-content): --tk-white;`) with dark text (`var(--text-primary): --tk-gray-900;`), allowing brand colors (Purples, Indigos, Violets, Pinks) to stand out as accents and for key interactive elements.
*   **Dark Mode (`[data-theme="dark"]`):** Designed to reduce eye strain in low-light conditions, especially during prolonged use. It employs dark backgrounds (e.g., `var(--bg-page): --tk-gray-900;`, `var(--bg-content): --tk-gray-800;`) with light text (`var(--text-primary): --tk-white;`). Brand colors remain vibrant but are carefully balanced against dark surfaces.
    *   **"Dark Purple Driven" Variant (within Dark Mode context):** As detailed in Section 4.4, designers can achieve a "dark purple driven" aesthetic by overriding core background tokens like `--bg-page`, `--bg-content`, and `--bg-card` with dark purple/indigo shades (e.g., from the 800-950 range: `var(--tk-purple-950)`, `var(--tk-indigo-900)`). This provides a richer brand expression within the dark theme paradigm. All text and non-text elements must maintain high contrast against these specific dark brand backgrounds.
*   **Night Mode (`[data-theme="night"]`):** A specialized dark theme utilizing a distinct, specially curated warmer, and desaturated color palette (defined in Section 4.4). It's designed to offer an alternative comfortable viewing experience for extended night-time use, further minimizing perceived blue light exposure and providing a softer visual experience compared to the standard Dark Mode.
*   **User Control & System Preference:**
    *   Ideally, users should be provided with a clear mechanism (e.g., a settings toggle) to select their preferred theme.
    *   The system should also respect OS-level theme preferences (`prefers-color-scheme: dark` or `prefers-color-scheme: light`) as an initial default if no user preference is explicitly saved via `localStorage`.

#### 10.2. Implementation via `data-theme`
Themes are applied by setting a `data-theme` attribute on the `<html>` element. JavaScript is used to manage theme switching and persistence.
```javascript
// Example JavaScript for theme switching and persistence
function applyTechnoKidsTheme(themeName) {
  document.documentElement.setAttribute('data-theme', themeName);
  localStorage.setItem('technoKidsPreferredTheme', themeName);
}

function initializeTechnoKidsTheme() {
  const savedTheme = localStorage.getItem('technoKidsPreferredTheme');
  const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
  const systemPrefersLight = window.matchMedia('(prefers-color-scheme: light)').matches;

  if (savedTheme) {
    applyTechnoKidsTheme(savedTheme);
  } else if (systemPrefersDark) {
    applyTechnoKidsTheme('dark'); // Default to dark if system prefers it and no user choice saved
  } else if (systemPrefersLight) {
    applyTechnoKidsTheme('light'); // Default to light if system prefers it
  } else {
    applyTechnoKidsTheme('light'); // Fallback default
  }
}

// Call initializeTechnoKidsTheme() on page load
// UI controls (e.g., buttons, dropdown) would call applyTechnoKidsTheme('light'/'dark'/'night')
```

#### 10.3. Specific Variable Overrides for Each Mode
The comprehensive CSS Custom Property blocks for `[data-theme='dark']` and `[data-theme='night']` in Section 4.4 detail all the specific token overrides. These include adjustments for:
*   Background colors (`--bg-page`, `--bg-content`, `--bg-card`, `--bg-input`, `--bg-subtle`, etc.)
*   Text colors (`--text-primary`, `--text-secondary`, `--text-subtle`, `--text-link`, etc.)
*   Border colors (`--border-standard`, `--border-input`, `--border-focus-ring-color`, etc.)
*   Semantic state colors (backgrounds and text for success, error, warning, info)
*   Shadow intensity and color (`--depth-shadow-*` variables)
*   Specific component state colors (e.g., `--button-disabled-bg-color`)

#### 10.4. Accessibility & Contrast in Each Theme
**Crucially, all themes must independently meet WCAG 2.1 AA contrast requirements for text and non-text elements.** This cannot be overstated.
*   **Dark Mode & "Dark Purple Driven" Challenges:**
    *   **Shadows:** Traditional dark shadows are less effective on dark backgrounds. Depth in dark themes is often conveyed by slightly lighter surface colors for elevated elements, subtle borders, or carefully tuned, more opaque shadows. The shadow variables are adjusted in dark themes to reflect this.
    *   **Color "Glow" / Halation:** Highly saturated brand colors (especially lighter tints of purples, violets, or pinks if used as text or icons) might appear to "glow" or cause halation (visual spreading) on very dark backgrounds, potentially impairing readability. The chosen text colors (`--text-on-dark-bg`, various light grays, and lighter brand tints like `--tk-purple-300`) are selected to mitigate this while maintaining brand character.
    *   **Input Borders (Non-Text Contrast):** Achieving 3:1 contrast for default state input borders against *both* their immediate input background *and* the overall page/content background can be challenging in highly chromatic dark themes (like deep purple on deep purple). This guide prioritizes:
        1.  A clear visual distinction of the input field itself (e.g., its background color differs from the page).
        2.  An **exceptionally clear and high-contrast focus state** (`:focus-visible`) for the input border.
        3.  If default state borders are subtle, hover states might provide an intermediate visual cue.
*   **Night Mode Considerations:**
    *   Uses a distinct, warmer, and desaturated palette. All contrast ratios are re-evaluated and defined specifically for these unique color combinations to ensure readability and comfort.
*   **Universal Testing:** Each theme **must be individually tested** for all accessibility criteria, including color contrast for text and non-text elements, focus visibility, and readability. Assumptions from Light Mode behavior do not automatically transfer to Dark or Night modes. Design and QA processes must include theme-specific accessibility validation.

---

### 11. Accessibility Compliance & Best Practices (WCAG Focused)

Accessibility is not an optional feature but a core requirement for all TechnoKids digital products. This design system is built with the Web Content Accessibility Guidelines (WCAG) 2.1 Level AA as the minimum standard, aiming for Level AAA where feasible without compromising core design goals or usability for other groups.

#### 11.1. Commitment to Inclusivity (POUR Principles)
Our design and development practices are fundamentally guided by WCAG's four foundational principles:
*   **Perceivable:** Information and user interface components must be presentable to users in ways they can perceive. This includes providing text alternatives for non-text content, ensuring content is distinguishable (e.g., color contrast), and making content adaptable to different presentations.
*   **Operable:** User interface components and navigation must be operable. This covers keyboard accessibility, providing users enough time to read and use content, avoiding content that can cause seizures, and ensuring content is easily navigable.
*   **Understandable:** Information and the operation of the user interface must be understandable. This involves making text readable and understandable, making web pages appear and operate in predictable ways, and helping users avoid and correct mistakes.
*   **Robust:** Content must be robust enough that it can be interpreted reliably by a wide variety of user agents, including current and future assistive technologies. This involves using valid code and ensuring custom components have correct names, roles, and values.

#### 11.2. Color Contrast (WCAG SC 1.4.3, 1.4.6 AAA, 1.4.11)
Adherence to these contrast ratios is critical for users with low vision or color vision deficiencies.
*   **SC 1.4.3 Contrast (Minimum) - AA (Required):**
    *   Normal Text (<18pt or <14pt bold): **4.5:1** against its background.
    *   Large Text (≥18pt or ≥14pt bold): **3:1** against its background.
    *   *All text color tokens (`--text-primary`, `--text-secondary`, etc.) and semantic text colors (`--semantic-*-color-text`) are defined and verified to meet these ratios on their intended default backgrounds (`--bg-content`, `--semantic-*-color-bg`, input backgrounds, button backgrounds) across all themes (Light, Dark, Night). Specific contrast ratios are noted in Section 4 (CSS Variables) and Section 6.4 (Typography).*
*   **SC 1.4.6 Contrast (Enhanced) - AAA (Strive For):**
    *   Normal Text: **7:1**. Large Text: **4.5:1**.
    *   *While AA is the minimum, for primary content text (e.g., body paragraphs, main headings), this guide strives for AAA where brand aesthetics and thematic goals allow. For instance, `--text-primary` on `--bg-content` in Light and Dark themes often meets or exceeds AAA.*
*   **SC 1.4.11 Non-Text Contrast - AA (Required):**
    *   User interface components (e.g., input field borders, checkbox/radio button boundaries, toggle switch tracks/knobs that indicate state) and graphical objects (e.g., informational icons whose meaning is tied to their visual appearance) require a **3:1** contrast ratio against their adjacent background color(s).
    *   *This is particularly critical for:*
        *   *Focus indicators (see Section 11.4).*
        *   *Default state borders of input fields (e.g., `var(--border-input)` must contrast 3:1 with `var(--bg-input)`).*
        *   *Boundaries of custom controls like checkboxes, radios, and toggles.*
        *   *Active state indicators for tabs or navigation items if their boundary is the primary visual cue.*
    *   *Component specifications in Section 7.3 detail how this is achieved for each component across themes.*

#### 11.3. Use of Color (WCAG SC 1.4.1)
*   Color is **never used as the sole means** of conveying information, indicating an action, prompting a response, or distinguishing a visual element. Redundant cues are always provided.
*   **Examples within this Guide:**
    *   Links are not only colored but also receive an underline on hover/focus (and optionally by default for inline links).
    *   Error states for forms use explicit error messages and icons in addition to red coloration for borders or text.
    *   Required fields are indicated with an asterisk (`*`) and potentially visually hidden text like "(required)" in addition to any color cues.
    *   Active navigation items use visual cues beyond color, such as a contrasting border, background change, or font weight change.
    *   Status messages (success, warning, error, info) use distinct icons alongside their semantic coloring.

#### 11.4. Keyboard Navigation & Focus Visibility (WCAG SC 2.1.1, 2.4.3, 2.4.7)
*   **SC 2.1.1 Keyboard:** All functionality of the content is operable through a keyboard interface without requiring specific timings for individual keystrokes. There must be no "keyboard traps" where focus can move into a component but cannot be moved out using only the keyboard.
*   **SC 2.4.3 Focus Order:** If a web page can be navigated sequentially and the navigation sequences affect meaning or operation, focusable components receive focus in an order that preserves meaning and operability. This typically means a logical DOM order.
*   **SC 2.4.7 Focus Visible (Critical):** Any keyboard operable user interface has a mode of operation where the keyboard focus indicator is highly visible.
    *   **TechnoKids Standard:** A clearly defined focus style using `outline: 2px solid var(--border-focus-ring-color); outline-offset: 2px;` is applied via the `:focus-visible` pseudo-class to all interactive elements.
    *   The `var(--border-focus-ring-color)` is theme-dependent (e.g., `--tk-purple-500` in Light, `--tk-purple-400` in Dark, a warm purple in Night) and is chosen to ensure at least a **3:1 contrast ratio** against both the component's background and its immediate surrounding background.
    *   See Section 7.2.3 and individual component styles in Section 7.3 for specific focus state definitions.

#### 11.5. Content Structure & Readability (WCAG SC 1.3.1, 2.4.6, 3.1.2)
*   **SC 1.3.1 Info and Relationships:** Semantic HTML (headings `<h1>-<h6>` in logical order, lists `<ul>, <ol>, <li>`, tables `<table>, <th>, <td>, scope`, landmarks `<main>, <nav>, <aside>`) is used correctly to convey the structure and relationships within content, making it understandable for assistive technologies.
*   **SC 2.4.6 Headings and Labels:** Headings and labels describe the topic or purpose of the content they are associated with. Labels are programmatically associated with form controls.
*   **SC 3.1.2 Language of Parts:** The human language of any content that differs from the page's default language is programmatically identifiable (e.g., using `lang` attribute on a `<span>` or `<div>`).
*   *Typography guidelines (Section 6) and Layout/Spacing guidelines (Section 5) directly support readability through appropriate font choices, sizes, line heights, clear visual hierarchy, and sufficient white space.*

#### 11.6. `prefers-reduced-motion` (WCAG SC 2.2.2 Pause, Stop, Hide; SC 2.3.3 Animation from Interactions)
*   User preferences for reduced motion are respected to prevent discomfort, distraction, or potential health issues (e.g., for users with vestibular disorders or photosensitivity). Non-essential animations are disabled or significantly reduced.
*   *See Section 9.5 for CSS implementation examples.*

#### 11.7. Semantic HTML & ARIA (WCAG SC 4.1.1 Parsing, 4.1.2 Name, Role, Value)
*   **SC 4.1.1 Parsing:** Content implemented using markup languages has complete start and end tags, elements are nested according to their specifications, elements do not contain duplicate attributes, and any IDs are unique. Code is validated against HTML/CSS standards.
*   **SC 4.1.2 Name, Role, Value:** For all user interface components (including form elements, links, and custom components generated by scripts), their name and role are programmatically determinable; states, properties, and values that can be set by the user are programmatically settable; and notification of changes to these items is available to user agents, including assistive technologies.
    *   Native HTML elements are used correctly and preferred where possible.
    *   For custom components (e.g., custom toggles, modals, tabs, dropdowns), appropriate ARIA roles, states, and properties are applied as per WAI-ARIA Authoring Practices Guide (APG). Component specifications in Section 7.3 include relevant ARIA guidance.

#### 11.8. Performance Considerations
*   **Optimized Assets:** Use optimized images (correct format, compression, responsive sizes via `<picture>` or `srcset`).
*   **Efficient CSS:** Leverage CSS Custom Properties for theming and maintainability, minimize selector overrides, and avoid CSS that causes excessive reflows/repaints.
*   **Font Loading:** Implement strategies like `font-display: swap;` and preloading for critical web fonts to improve perceived performance and content availability (see Section 6.5).
*   **Code Minification:** Minify HTML, CSS, and JavaScript files for production environments.

#### 11.9. Tools and Testing Methodology
A combination of automated tools and manual testing is essential for comprehensive accessibility validation:
*   **Automated Contrast Checkers:** WebAIM Contrast Checker, browser developer tools (e.g., Chrome DevTools color picker, Firefox Accessibility Inspector), Axe DevTools.
*   **Automated General Accessibility Checkers:** Axe DevTools browser extension, WAVE Evaluation Tool (for quick scans and structure checks). These tools help identify common issues but cannot catch all problems.
*   **Manual Testing (Indispensable):**
    *   **Keyboard-Only Navigation:** Test all interactive elements and workflows using only the keyboard (Tab, Shift+Tab, Enter, Space, Arrow keys, Escape). Verify logical focus order and visible focus indicators.
    *   **Screen Reader Testing:** Test with major screen readers (e.g., NVDA for Windows, VoiceOver for macOS/iOS, JAWS if available) to ensure content is announced correctly, roles and states are clear, and interactive elements are operable.
    *   **Zoom/Magnification Testing:** Test interface usability when zoomed up to 400% (WCAG SC 1.4.4 Resize Text, SC 1.4.10 Reflow).
    *   **Reduced Motion Simulation:** Test with `prefers-reduced-motion` enabled in OS or browser settings.
*   **Code Validators:** W3C Markup Validation Service (for HTML) and W3C CSS Validation Service (for CSS) to ensure code robustness.

---

### 12. Further Inspiration & External Tools for Palette Development

While the TechnoKids color system is comprehensive, external tools and resources can aid in discovery and refinement.

#### 12.1. Recommended Third-Party Color Palette Generators
*   **Coolors.co:** Fast palette generation, explore trending schemes, extract from images.
*   **Adobe Color (color.adobe.com):** Powerful, harmony rules, Creative Cloud integration.
*   **Canva Palette Generator:** User-friendly, image-based palette creation.
*   **ColorHexa:** In-depth analysis of single colors, scheme generation.

#### 12.2. Inspiration Platforms
*   **Pinterest:** Visual discovery for "purple color palettes," "violet color schemes," etc.
*   **Dribbble & Behance:** Showcase of contemporary web and UI design.

#### 12.3. Browser Extensions for Color Picking
*   **ColorZilla (and similar):** Eyedropper tool to identify colors on webpages.

The synergy between a design system's internal color management (like TechnoKids' CSS tokens) and these external resources offers a comprehensive approach. External tools excel in discovery and ideation. Promising shades can then be integrated into the design system's defined palette and token structure.

---

### 13. Conclusion & Governance

#### 13.1. Summary of Guide's Importance
The "TechnoKids Comprehensive Color System & Thematic Design Guide" (Version 18.0) is a critical asset for creating high-quality, consistent, accessible, and brand-aligned digital experiences. By adhering to this guide, we ensure that TechnoKids products are not only visually engaging, especially with the enhanced capacity for "dark purple driven" themes and richer accents like Violet, but also usable and inclusive for our entire diverse audience of learners and educators.

#### 13.2. Design System Governance and Maintenance
This guide is a living document.
*   **Ownership & Stewardship:** A designated team/individual is responsible for maintenance.
*   **Change Management Process:** Defined process for proposing, reviewing, testing (emphasizing accessibility and cross-theme consistency), and approving changes.
*   **Regular Audits & Reviews:** Periodic audits against live products.
*   **Documentation Updates:** Prompt and clear documentation of approved changes. Version control maintained.
*   **Communication & Training:** Updates communicated; new team members onboarded.

#### 13.3. Contribution and Evolution Process
Evolution should be collaborative.
*   **Feedback Channels:** Accessible channels for feedback and suggestions.
*   **Collaborative Review & Decision Making:** Cross-functional review for significant updates.
*   **Pilot Testing:** Consider for major changes.
*   **Iterative Refinement:** Embrace an iterative approach.

By fostering a collaborative approach and rigorously applying its principles, TechnoKids can ensure its design system remains a powerful tool for innovation, quality, and inclusivity.

---

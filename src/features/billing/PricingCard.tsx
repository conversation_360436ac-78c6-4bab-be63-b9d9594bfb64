import { useTranslations } from 'next-intl';
import React from 'react';

import type { BillingInterval } from '@/types/Subscription';

export const PricingCard = (props: {
  planId: string;
  price: number;
  interval: BillingInterval;
  button: React.ReactNode;
  children: React.ReactNode;
}) => {
  const t = useTranslations('PricingPlan');

  return (
    <div
      className="rounded-[var(--card-border-radius)] bg-[var(--card-bg)] p-[var(--card-padding)] text-center
                 shadow-[var(--card-shadow)] transition-all
                 duration-300 ease-in-out
                 hover:translate-y-[-5px] hover:shadow-[var(--card-hover-shadow)]"
    >
      <div className="text-lg font-semibold text-[var(--text-primary)]">
        {t(`${props.planId}_plan_name`)}
      </div>

      <div className="mt-3 flex items-center justify-center">
        <div className="text-5xl font-bold text-[var(--text-primary)]">
          {`$${props.price}`}
        </div>

        <div className="ml-1 text-[var(--text-secondary)]">
          {`/ ${t(`plan_interval_${props.interval}`)}`}
        </div>
      </div>

      <div className="mt-2 text-sm text-[var(--text-secondary)]">
        {t(`${props.planId}_plan_description`)}
      </div>

      {props.button}

      <ul className="mt-8 space-y-3">{props.children}</ul>
    </div>
  );
};

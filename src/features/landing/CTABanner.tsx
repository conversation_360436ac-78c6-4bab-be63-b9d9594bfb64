export const CTABanner = (props: {
  title: string;
  description: string;
  buttons: React.ReactNode;
}) => (
  <div className="relative overflow-hidden rounded-xl bg-gradient-to-br from-tk-purple-600 via-tk-purple-500 to-tk-teal-600
                  px-8 py-12 text-center shadow-[var(--shadow-gradient-glow)]
                  transition-all duration-500 hover:shadow-[var(--shadow-purple-glow-lg)]"
  >
    {/* Subtle overlay pattern */}
    <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-black/10" />

    {/* Animated background accent */}
    <div
      className="absolute -right-20 -top-20 size-40 animate-pulse rounded-full
                    bg-gradient-to-br from-white/20 to-transparent blur-2xl"
      style={{ animationDuration: '3s' }}
    />
    <div
      className="from-tk-teal-400/30 absolute -bottom-20 -left-20 size-32 animate-pulse
                    rounded-full bg-gradient-to-br to-transparent blur-2xl"
      style={{ animationDuration: '4s', animationDelay: '1s' }}
    />

    {/* Content */}
    <div className="relative z-10">
      <div className="text-3xl font-bold text-tk-white drop-shadow-sm lg:text-4xl">
        {props.title}
      </div>

      <div className="mt-4 text-lg font-medium text-tk-purple-100 lg:text-xl">
        {props.description}
      </div>

      <div className="mt-8">{props.buttons}</div>
    </div>
  </div>
);

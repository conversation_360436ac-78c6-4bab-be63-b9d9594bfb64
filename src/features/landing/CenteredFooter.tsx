import { useTranslations } from 'next-intl';
import React from 'react';

export const CenteredFooter = (props: {
  logo: React.ReactNode;
  name: string;
  iconList: React.ReactNode;
  legalLinks: React.ReactNode;
  children: React.ReactNode;
}) => {
  const t = useTranslations('Footer');

  return (
    <div className="flex flex-col items-center text-center">
      {props.logo}

      <ul className="mt-4 flex gap-x-8 text-lg max-sm:flex-col [&_a:hover]:text-vibrant-teal [&_a]:text-charcoal-night">
        {props.children}
      </ul>

      <ul className="mt-4 flex flex-row gap-x-5 [&_svg:hover]:fill-vibrant-teal [&_svg]:size-5 [&_svg]:fill-majestic-purple">
        {props.iconList}
      </ul>

      <div className="mt-6 flex w-full items-center justify-between gap-y-2 border-t border-silver-lining pt-3 text-sm text-slate-gray max-md:flex-col">
        <div>
          {`© Copyright ${new Date().getFullYear()} ${props.name}. `}
          {t.rich('designed_by', {
            author: () => (
              <a
                className="font-medium text-deep-amethyst hover:text-vibrant-teal"
                href="https://technokids.com"
              >
                TechnoKidsAI
              </a>
            ),
          })}
          {/*
           * PLEASE READ THIS SECTION
           * I'm an indie maker with limited resources and funds, I'll really appreciate if you could have a link to my website.
           * The link doesn't need to appear on every pages, one link on one page is enough.
           * For example, in the `About` page. Thank you for your support, it'll mean a lot to me.
           */}
        </div>

        <ul className="flex gap-x-4 font-medium [&_a:hover]:text-vibrant-teal [&_a]:text-deep-amethyst">
          {props.legalLinks}
        </ul>
      </div>
    </div>
  );
};

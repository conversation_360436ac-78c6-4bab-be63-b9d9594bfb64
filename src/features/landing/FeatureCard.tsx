export const FeatureCard = (props: {
  icon: React.ReactNode;
  title: React.ReactNode; // Changed from string
  children: React.ReactNode;
}) => (
  <div
    className="group overflow-hidden rounded-[var(--card-border-radius)] bg-[var(--card-bg)]
               p-[var(--card-padding)] shadow-[var(--card-shadow)]
               transition-all duration-500 ease-out
               focus-within:shadow-[var(--card-focus-shadow)] hover:translate-y-[-8px]
               hover:scale-[1.02] hover:shadow-[var(--card-hover-shadow)]"
  >
    {/* Enhanced Colored Top Border with Gradient */}
    <div className="mx-[-var(--card-padding)] mb-[calc(var(--card-padding)-5px)] mt-[-var(--card-padding)]
                    h-[5px] bg-gradient-to-r from-tk-purple-600 via-tk-indigo-600 to-tk-teal-600
                    transition-all duration-500 group-hover:h-[6px]"
    />

    {/* Enhanced Icon Container with Glow Effect */}
    <div className="relative size-12 rounded-lg bg-gradient-to-br from-tk-purple-600 via-tk-indigo-600 to-tk-teal-600
                    p-2 shadow-[var(--shadow-purple-glow)] transition-all duration-500
                    group-hover:scale-110 group-hover:shadow-[var(--shadow-purple-glow-lg)]
                    [&_svg]:stroke-tk-white [&_svg]:stroke-2 [&_svg]:transition-transform [&_svg]:duration-500
                    group-hover:[&_svg]:scale-110"
    >
      {props.icon}
      {/* Subtle inner glow */}
      <div className="absolute inset-0 rounded-lg bg-gradient-to-br from-white/20 to-transparent opacity-0
                      transition-opacity duration-500 group-hover:opacity-100"
      />
    </div>

    <div className="mt-5 text-lg font-bold text-[var(--text-primary)] transition-colors duration-300
                    group-hover:text-tk-purple-700"
    >
      {props.title}
    </div>

    {/* Enhanced Accent Line */}
    <div className="my-4 w-8 border-t-2 border-tk-teal-600 transition-all duration-500
                    group-hover:w-12 group-hover:border-tk-purple-600"
    />

    <div className="mt-3 leading-relaxed text-[var(--text-secondary)] transition-colors duration-300">
      {props.children}
    </div>
  </div>
);

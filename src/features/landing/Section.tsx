import { forwardRef } from 'react';

import { cn } from '@/utils/Helpers';

type SectionProps = {
  children: React.ReactNode;
  title?: React.ReactNode; // Changed from string
  subtitle?: string;
  description?: React.ReactNode; // Changed from string
  className?: string;
};

export const Section = forwardRef<HTMLDivElement, SectionProps>(
  (props, ref) => (
    <div ref={ref} className={cn('px-3 py-16', props.className)}>
      {(props.title || props.subtitle || props.description) && (
        <div className="mx-auto mb-12 max-w-screen-md text-center">
          {props.subtitle && (
            <div className="bg-gradient-to-r from-majestic-purple via-deep-amethyst to-vibrant-teal bg-clip-text text-sm font-bold text-transparent">
              {props.subtitle}
            </div>
          )}

          {props.title && (
            <div className="mt-1 text-3xl font-bold text-deep-amethyst">{props.title}</div>
          )}

          {props.description && (
            <div className="mt-2 text-lg text-charcoal-night">
              {props.description}
            </div>
          )}
        </div>
      )}

      <div className="mx-auto max-w-screen-lg">{props.children}</div>
    </div>
  ),
);

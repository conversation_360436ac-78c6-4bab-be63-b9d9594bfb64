export const CenteredHero = (props: {
  banner: React.ReactNode;
  title: React.ReactNode;
  description: React.ReactNode; // Changed from string to React.ReactNode
  buttons: React.ReactNode;
}) => (
  <>
    <div className="text-center">{props.banner}</div>

    <div className="mt-6 text-center text-5xl font-bold tracking-tight text-[var(--text-primary)]
                    drop-shadow-sm lg:text-6xl"
    >
      {props.title}
    </div>

    <div className="mx-auto mt-6 max-w-screen-md text-center text-xl leading-relaxed text-[var(--text-secondary)]
                    lg:text-2xl lg:leading-relaxed"
    >
      {props.description}
    </div>

    <div className="mt-10 flex justify-center gap-x-6 gap-y-4 max-sm:flex-col">
      {props.buttons}
    </div>
  </>
);

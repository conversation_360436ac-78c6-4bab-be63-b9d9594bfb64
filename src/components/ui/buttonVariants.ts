import { cva } from 'class-variance-authority';

export const buttonVariants = cva(
  'inline-flex items-center justify-center whitespace-nowrap rounded-[var(--input-border-radius)] text-sm font-semibold ring-offset-background transition-all duration-300 ease-in-out focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
  // Base styles using TechnoKids v18 design tokens
  {
    variants: {
      variant: {
        default: // Primary CTA using gradient with enhanced shadows
          'transform bg-[image:var(--button-primary-bg-image)] text-[var(--button-primary-color-text)] shadow-[var(--button-shadow)] hover:translate-y-[-3px] hover:bg-[image:var(--button-primary-hover-bg-image)] hover:shadow-[var(--button-hover-shadow)] active:translate-y-px active:shadow-[var(--button-active-shadow)]',
        secondary: // Secondary CTA using gradient with enhanced shadows
          'transform bg-[image:var(--button-secondary-bg-image)] text-[var(--button-secondary-color-text)] shadow-[var(--button-shadow)] hover:translate-y-[-3px] hover:bg-[image:var(--button-secondary-hover-bg-image)] hover:shadow-[var(--button-hover-shadow)] active:translate-y-px active:shadow-[var(--button-active-shadow)]',
        accent: // Accent CTA using gradient with enhanced shadows
          'transform bg-[image:var(--button-accent-bg-image)] text-[var(--button-accent-color-text)] shadow-[var(--button-shadow)] hover:translate-y-[-3px] hover:bg-[image:var(--button-accent-hover-bg-image)] hover:shadow-[var(--button-hover-shadow)] active:translate-y-px active:shadow-[var(--button-active-shadow)]',
        destructive:
          'bg-destructive text-destructive-foreground shadow-[var(--button-shadow)] hover:bg-destructive/90 hover:shadow-[var(--button-hover-shadow)]',
        outline: // Outline CTA with enhanced hover effects
          'border-2 border-[var(--button-outline-color-border)] bg-transparent text-[var(--button-outline-color-text)] transition-all duration-300 hover:-translate-y-px hover:bg-[var(--button-outline-hover-bg-color)] hover:text-[var(--button-outline-hover-color-text)] hover:shadow-[var(--shadow-purple-glow)]',
        ghost:
          'text-[var(--text-link)] hover:bg-[var(--bg-content)] hover:text-[var(--text-link-hover)]',
        link: 'text-[var(--text-link)] underline-offset-4 hover:text-[var(--text-link-hover)] hover:underline',
      },
      size: {
        default: 'px-lg py-md', // Using spacing tokens
        sm: 'h-9 px-md',
        lg: 'px-xl py-lg',
        icon: 'size-10',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  },
);

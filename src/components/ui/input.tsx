import * as React from 'react';

import { cn } from '@/utils/Helpers';

export type InputProps = {} & React.InputHTMLAttributes<HTMLInputElement>;

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, ...props }, ref) => {
    return (
      <input
        type={type}
        className={cn(
          'flex h-9 w-full rounded-[var(--input-border-radius)] border border-[var(--input-border)] bg-[var(--input-bg)] px-3 py-1 text-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-[var(--input-placeholder-text)] focus-visible:outline-none focus-visible:border-[var(--input-focus-border)] focus-visible:shadow-[var(--input-focus-shadow)] disabled:cursor-not-allowed disabled:opacity-50',
          // Base styles from colorguide:
          // - rounded-[var(--input-border-radius)] (5px)
          // - border-[var(--input-border)]
          // - bg-[var(--input-bg)]
          // - placeholder:text-[var(--input-placeholder-text)]
          // Focus styles from colorguide:
          // - focus-visible:border-[var(--input-focus-border)]
          // - focus-visible:shadow-[var(--input-focus-shadow)] (e.g. 0 0 0 3px rgba(106,13,173,0.2))
          // Removed: shadow-sm (base state shadow not in colorguide for inputs)
          // Removed: focus-visible:ring-1 focus-visible:ring-ring (replaced by new focus shadow)
          className,
        )}
        ref={ref}
        {...props}
      />
    );
  },
);
Input.displayName = 'Input';

export { Input };

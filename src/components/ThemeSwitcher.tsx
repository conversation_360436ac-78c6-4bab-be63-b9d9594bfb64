'use client';

import { useEffect, useState } from 'react';

const themes = [
  { name: 'Light', value: 'light' },
  { name: 'Dark', value: 'dark' },
  { name: 'Night', value: 'night' },
];

export function ThemeSwitcher() {
  const [mounted, setMounted] = useState(false);
  // Initialize theme state from localStorage or default to 'light'
  const [theme, setTheme] = useState(() => {
    if (typeof window !== 'undefined') {
      const storedTheme = localStorage.getItem('theme');
      if (storedTheme && themes.some(t => t.value === storedTheme)) {
        return storedTheme;
      }
    }
    return 'light'; // Default theme
  });

  // Effect to apply theme on initial mount (client-side only)
  useEffect(() => {
    setMounted(true);
    // Apply the initial theme to the documentElement once component is mounted
    const initialTheme = localStorage.getItem('theme');
    if (initialTheme && themes.some(t => t.value === initialTheme)) {
      document.documentElement.setAttribute('data-theme', initialTheme);
      // setTheme(initialTheme); // Set state if not already set by useState initializer
    } else {
      document.documentElement.setAttribute('data-theme', 'light');
      // localStorage.setItem('theme', 'light'); // Set default in localStorage
    }
  }, []);

  // Effect to update data-theme attribute and localStorage when theme changes
  useEffect(() => {
    if (mounted) { // Ensure this runs only after initial mount and on client
      document.documentElement.setAttribute('data-theme', theme);
      localStorage.setItem('theme', theme);
    }
  }, [theme, mounted]);

  if (!mounted) {
    // To prevent hydration mismatch, render nothing or a placeholder until mounted
    // This ensures that the server-rendered HTML matches the initial client render
    // before the theme is applied based on localStorage.
    // Alternatively, render a basic version of the buttons without active state.
    return (
      <div className="flex h-9 animate-pulse items-center space-x-2">
        {themes.map(t => (
          <div key={t.value} className="h-full w-16 rounded bg-muted p-2" />
        ))}
      </div>
    );
  }

  return (
    <div className="flex space-x-1 rounded-md border border-[var(--border-primary)] bg-[var(--bg-secondary)] p-1">
      {themes.map(t => (
        <button
          key={t.value}
          onClick={() => setTheme(t.value)}
          className={`relative rounded-md border-2 px-3 py-1.5 text-sm font-semibold transition-all duration-200
            ${
        theme === t.value
          ? 'border-[var(--border-interactive)] bg-[var(--cta-primary-bg)] text-[var(--cta-primary-text)] shadow-[var(--cta-primary-shadow)]'
          : 'border-transparent text-[var(--text-secondary)] hover:bg-[var(--bg-tertiary)] hover:text-[var(--text-primary)]'
        }
          `}
          aria-pressed={theme === t.value}
          aria-current={theme === t.value ? 'page' : undefined}
          type="button"
        >
          {t.name}
          {theme === t.value && (
            <span className="absolute -bottom-1 left-1/2 size-1 -translate-x-1/2 rounded-full bg-[var(--border-interactive)]" />
          )}
        </button>
      ))}
    </div>
  );
}

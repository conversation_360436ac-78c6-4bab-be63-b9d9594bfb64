'use client';
import { TwitterLogoIcon } from '@radix-ui/react-icons';
import { useTranslations } from 'next-intl';

import { badgeVariants } from '@/components/ui/badgeVariants';
import { buttonVariants } from '@/components/ui/buttonVariants';
import { CenteredHero } from '@/features/landing/CenteredHero';
import { Section } from '@/features/landing/Section';
import { useIntersectionObserver } from '@/hooks/useIntersectionObserver'; // Added
import { cn } from '@/utils/Helpers'; // Added for combining classNames

export const Hero = () => {
  const t = useTranslations('Hero');
  const { ref, hasBeenVisible } = useIntersectionObserver({ threshold: 0.1 }); // Added

  return (
    <Section
      ref={ref} // Added ref
      className={cn(
        'relative py-36 overflow-x-hidden transition-all duration-700 ease-out', // Enhanced duration
        hasBeenVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8', // Enhanced animation
      )}
    >
      {/* Enhanced Decorative Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Primary gradient blob */}
        <div
          className="decorative-blob absolute -left-80 -top-60 size-[500px] animate-pulse opacity-40"
          style={{ animationDuration: '4s' }}
        />
        {/* Secondary gradient blob */}
        <div
          className="decorative-blob absolute -bottom-80 -right-60 size-[600px] animate-pulse opacity-25"
          style={{ animationDuration: '6s', animationDelay: '2s' }}
        />
        {/* Additional accent blob */}
        <div
          className="absolute left-1/2 top-1/2 size-[800px] -translate-x-1/2 -translate-y-1/2 animate-pulse rounded-full blur-3xl"
          style={{
            background: 'linear-gradient(to right, rgba(139, 92, 246, 0.05), rgba(99, 102, 241, 0.05), rgba(20, 184, 166, 0.05))',
            animationDuration: '8s',
            animationDelay: '1s',
          }}
        />
      </div>

      <CenteredHero
        banner={(
          <a
            className={badgeVariants()}
            href="https://twitter.com/TechnoKidsAI"
            target="_blank"
            rel="noopener noreferrer"
          >
            <TwitterLogoIcon className="mr-1 size-5" />
            {' '}
            {t('follow_twitter')}
          </a>
        )}
        title={t.rich('title', {
          important: chunks => (
            <span className="animate-gradient-shift bg-gradient-to-r from-tk-purple-600 via-tk-indigo-600 to-tk-teal-600 bg-clip-text font-bold text-transparent">
              {chunks}
            </span>
          ),
        })}
        description={<span className="text-[var(--text-secondary)]">{t('description')}</span>}
        buttons={(
          <>
            <a
              className={buttonVariants({ variant: 'default', size: 'lg' })} // Uses updated default variant
              href="https://technokids.com/explore"
            >
              {t('primary_button')}
            </a>

            <a
              className={buttonVariants({ variant: 'outline', size: 'lg' })} // Uses updated outline variant
              href="https://technokids.com/learn"
            >
              {t('secondary_button')}
            </a>
          </>
        )}
      />
    </Section>
  );
};

import Link from 'next/link';
import { useTranslations } from 'next-intl';

import { LocaleSwitcher } from '@/components/LocaleSwitcher';
import { ThemeSwitcher } from '@/components/ThemeSwitcher';
import { buttonVariants } from '@/components/ui/buttonVariants';
import { CenteredMenu } from '@/features/landing/CenteredMenu';
import { Section } from '@/features/landing/Section';

import { Logo } from './Logo';

export const Navbar = () => {
  const t = useTranslations('Navbar');

  return (
    <Section className="px-3 py-6">
      <CenteredMenu
        logo={<Logo />}
        rightMenu={(
          <div className="flex items-center gap-4">
            {/* Theme and Language Switchers */}
            <div className="relative z-10">
              <ThemeSwitcher />
            </div>
            <div className="relative z-10">
              <LocaleSwitcher />
            </div>

            {/* Auth Buttons */}
            <div className="flex items-center gap-3">
              <Link
                href="/sign-in"
                className={buttonVariants({
                  variant: 'outline',
                  size: 'default',
                })}
              >
                {t('sign_in')}
              </Link>
              <Link
                href="/sign-up"
                className={buttonVariants({
                  variant: 'default',
                  size: 'default',
                })}
              >
                {t('sign_up')}
              </Link>
            </div>
          </div>
        )}
      >
        <li>
          <Link href="/features">{t('product')}</Link>
        </li>
        <li>
          <Link href="/documentation">{t('docs')}</Link>
        </li>
        <li>
          <Link href="/blog">{t('blog')}</Link>
        </li>
        <li>
          <Link href="/community">{t('community')}</Link>
        </li>
        <li>
          <Link href="/about">{t('company')}</Link>
        </li>
      </CenteredMenu>
    </Section>
  );
};

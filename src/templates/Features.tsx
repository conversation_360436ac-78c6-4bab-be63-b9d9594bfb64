import { useTranslations } from 'next-intl';

import { Background } from '@/components/Background';
import { FeatureCard } from '@/features/landing/FeatureCard';
import { Section } from '@/features/landing/Section';

export const Features = () => {
  const t = useTranslations('Features');

  return (
    <Background color="bg-[var(--bg-secondary)]">
      {' '}
      {/* Updated background */}
      <Section
        subtitle={t('section_subtitle')}
        title={<span className="text-[var(--text-heading-brand)]">{t('section_title')}</span>}
        description={<span className="text-[var(--text-secondary)]">{t('section_description')}</span>}
      >
        <div className="grid grid-cols-1 gap-x-3 gap-y-8 md:grid-cols-3">
          <FeatureCard
            icon={(
              <svg
                className="stroke-[var(--brand-majestic-purple)] stroke-2" // Updated stroke color
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M0 0h24v24H0z" stroke="none" />
                <path d="M3 19a2 2 0 0 0 2 2h14a2 2 0 0 0 2 -2v-5a2 2 0 0 0 -2 -2h-9a2 2 0 0 0 -2 2v5a2 2 0 0 0 -2 -2h-1a2 2 0 0 1 -2 -2v-8a2 2 0 0 1 2 -2h1a2 2 0 0 1 2 2v8a2 2 0 0 0 2 -2h9a2 2 0 0 1 2 2v5a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-5" />
                <path d="M12 12v-6" />
                <path d="M16 8h-8" />
              </svg>
            )}
            title={t('feature1_title')}
          >
            {t('feature_description')}
          </FeatureCard>

          <FeatureCard
            icon={(
              <svg
                className="stroke-[var(--brand-vibrant-teal)] stroke-2" // Updated stroke color
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M0 0h24v24H0z" stroke="none" />
                <path d="M6 4h11a2 2 0 0 1 2 2v12a2 2 0 0 1 -2 2h-11a1 1 0 0 1 -1 -1v-14a1 1 0 0 1 1 -1m3 0v18m4 -14h2m-2 4h2m-2 4h2" />
              </svg>
            )}
            title={t('feature2_title')}
          >
            {t('feature_description')}
          </FeatureCard>

          <FeatureCard
            icon={(
              <svg
                className="stroke-tk-purple-600 stroke-2" // Updated stroke color
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M0 0h24v24H0z" stroke="none" />
                <path d="M15 17a3 3 0 1 0 6 0a3 3 0 0 0 -6 0m-8 -2a2 2 0 1 0 4 0a2 2 0 0 0 -4 0m0 -7a2 2 0 1 0 4 0a2 2 0 0 0 -4 0m12 -2a2 2 0 1 0 4 0a2 2 0 0 0 -4 0m-2.932 4.386l2.932 -1.386m-4 -3l-2 1m2 7.227l-2 1.173" />
              </svg>
            )}
            title={t('feature3_title')}
          >
            {t('feature_description')}
          </FeatureCard>

          <FeatureCard
            icon={(
              <svg
                className="stroke-[var(--brand-vibrant-teal)] stroke-2" // Updated stroke color
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M0 0h24v24H0z" stroke="none" />
                <path d="M9 5h-2a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-12a2 2 0 0 0 -2 -2h-2" />
                <rect x="9" y="3" width="6" height="4" rx="2" />
                <path d="M9 12h6" />
                <path d="M9 16h6" />
              </svg>
            )}
            title={t('feature4_title')}
          >
            {t('feature_description')}
          </FeatureCard>

          <FeatureCard
            icon={(
              <svg
                className="stroke-[var(--brand-majestic-purple)] stroke-2" // Updated stroke color
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M0 0h24v24H0z" stroke="none" />
                <path d="M9 12l2 2l4 -4" />
                <path d="M12 3c7.2 0 9 1.8 9 9s-1.8 9 -9 9s-9 -1.8 -9 -9s1.8 -9 9 -9z" />
              </svg>
            )}
            title={t('feature5_title')}
          >
            {t('feature_description')}
          </FeatureCard>

          <FeatureCard
            icon={(
              <svg
                className="stroke-[var(--brand-vibrant-teal)] stroke-2" // Updated stroke color
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M0 0h24v24H0z" stroke="none" />
                <circle cx="12" cy="12" r="9" />
                <line x1="3.6" y1="15" x2="14.15" y2="15" />
                <line x1="3.6" y1="9" x2="9.15" y2="9" />
                <line x1="14.6" y1="9" x2="20.4" y2="9" />
                <line x1="14.6" y1="15" x2="20.4" y2="15" />
              </svg>
            )}
            title={t('feature6_title')}
          >
            {t('feature_description')}
          </FeatureCard>
        </div>
      </Section>
    </Background>
  );
};

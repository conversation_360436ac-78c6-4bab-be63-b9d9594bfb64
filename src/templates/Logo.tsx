import Image from 'next/image';

import { AppConfig } from '@/utils/AppConfig';

export const Logo = (props: {
  isTextHidden?: boolean;
}) => (
  <div className="flex items-center text-xl font-semibold">
    <Image
      src="/assets/images/technokidslogo.gif"
      alt="TechnoKidsAI Logo"
      width={32} // Adjust width as needed
      height={32} // Adjust height as needed
      className="mr-1"
    />
    {!props.isTextHidden && <span className="bg-gradient-to-r from-tk-purple-600 via-tk-indigo-600 to-tk-teal-600 bg-clip-text font-bold text-transparent">{AppConfig.name}</span>}
  </div>
);

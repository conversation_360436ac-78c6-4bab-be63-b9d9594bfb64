import { useTranslations } from 'next-intl';

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Section } from '@/features/landing/Section';

export const FAQ = () => {
  const t = useTranslations('FAQ');
  const tf = useTranslations('Features'); // Using Features for section title

  return (
    <Section
      // Use a more generic or FAQ-specific title if available in locales
      title={<span className="text-deep-amethyst">{tf('section_title')}</span>}
      description={<span className="text-charcoal-night">{tf('section_description')}</span>}
    >
      <Accordion type="multiple" className="mx-auto w-full max-w-3xl">
        <AccordionItem value="item-1">
          <AccordionTrigger className="font-medium text-deep-amethyst">{t('question')}</AccordionTrigger>
          <AccordionContent className="text-charcoal-night">{t('answer')}</AccordionContent>
        </AccordionItem>
        <AccordionItem value="item-2">
          <AccordionTrigger className="font-medium text-deep-amethyst">{t('question')}</AccordionTrigger>
          <AccordionContent className="text-charcoal-night">{t('answer')}</AccordionContent>
        </AccordionItem>
        <AccordionItem value="item-3">
          <AccordionTrigger className="font-medium text-deep-amethyst">{t('question')}</AccordionTrigger>
          <AccordionContent className="text-charcoal-night">{t('answer')}</AccordionContent>
        </AccordionItem>
        <AccordionItem value="item-4">
          <AccordionTrigger className="font-medium text-deep-amethyst">{t('question')}</AccordionTrigger>
          <AccordionContent className="text-charcoal-night">{t('answer')}</AccordionContent>
        </AccordionItem>
        <AccordionItem value="item-5">
          <AccordionTrigger className="font-medium text-deep-amethyst">{t('question')}</AccordionTrigger>
          <AccordionContent className="text-charcoal-night">{t('answer')}</AccordionContent>
        </AccordionItem>
        <AccordionItem value="item-6">
          <AccordionTrigger className="font-medium text-deep-amethyst">{t('question')}</AccordionTrigger>
          <AccordionContent className="text-charcoal-night">{t('answer')}</AccordionContent>
        </AccordionItem>
      </Accordion>
    </Section>
  );
};

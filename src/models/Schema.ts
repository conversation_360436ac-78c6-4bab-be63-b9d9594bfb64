// LEGACY SCHEMA FILE - MAINTAINED FOR BACKWARD COMPATIBILITY
// This file now imports from the new unified schema structure in packages/db/schema/
// The new schema structure provides a comprehensive data model for educational applications

// Re-export everything from the new schema structure
export * from '../../packages/db/schema';

// Legacy exports for backward compatibility
import { organizationSchema as organization, todoSchema as todo } from '../../packages/db/schema';

// Maintain the original export names for existing code
export const organizationSchema = organization;
export const todoSchema = todo;

@tailwind base;
@tailwind components;
@tailwind utilities;

/* TechnoKids Color System v18 - Comprehensive Design Tokens */
/* Based on colorguidev18.md */

@layer base {
  :root {
    /* === CORE BRAND & ACCENT COLORS (TechnoKids v18) === */
    /* Purple Scale (Primary Brand) */
    --tk-purple-50: #f5f3ff;
    --tk-purple-100: #ede9fe;
    --tk-purple-200: #ddd6fe;
    --tk-purple-300: #c4b5fd;
    --tk-purple-400: #a78bfa;
    --tk-purple-500: #8b5cf6; /* Primary Brand Color */
    --tk-purple-600: #7c3aed;
    --tk-purple-700: #6d28d9;
    --tk-purple-800: #5b21b6;
    --tk-purple-900: #4c1d95;
    --tk-purple-950: #2e1065;

    /* Indigo Scale (Secondary Brand) */
    --tk-indigo-50: #eef2ff;
    --tk-indigo-100: #e0e7ff;
    --tk-indigo-200: #c7d2fe;
    --tk-indigo-300: #a5b4fc;
    --tk-indigo-400: #818cf8;
    --tk-indigo-500: #6366f1; /* Secondary Brand Color */
    --tk-indigo-600: #4f46e5;
    --tk-indigo-700: #4338ca;
    --tk-indigo-800: #3730a3;
    --tk-indigo-900: #312e81;
    --tk-indigo-950: #1e1b4b;

    /* Violet Scale (Key Accent Family) */
    --tk-violet-50: #f5f3ff;
    --tk-violet-100: #ede9fe;
    --tk-violet-200: #ddd6fe;
    --tk-violet-300: #c4b5fd;
    --tk-violet-400: #a78bfa; /* Primary Violet Accent */
    --tk-violet-500: #8b5cf6;
    --tk-violet-600: #7c3aed;
    --tk-violet-700: #6d28d9;
    --tk-violet-800: #5b21b6;
    --tk-violet-900: #4c1d95;
    --tk-violet-950: #2e1065;

    /* Pink Scale (Key Warm Accent Family) */
    --tk-pink-50: #fdf2f8;
    --tk-pink-100: #fce7f3;
    --tk-pink-200: #fbcfe8;
    --tk-pink-300: #f9a8d4;
    --tk-pink-400: #f472b6;
    --tk-pink-500: #ec4899; /* Primary Pink Accent */
    --tk-pink-600: #db2777;
    --tk-pink-700: #be185d;
    --tk-pink-800: #9d174d;
    --tk-pink-900: #831843;

    /* Other Accent & Action Colors */
    --tk-rose-500: #f43f5e;
    --tk-rose-600: #e11d48;
    --tk-teal-500: #14b8a6;
    --tk-teal-600: #0d9488;
    --tk-teal-700: #0f766e;
    --tk-cyan-50: #ecfeff;
    --tk-cyan-500: #06b6d4;
    --tk-cyan-600: #0891b2;
    --tk-sky-500: #0ea5e9;
    --tk-orange-500: #f97316;
    --tk-amber-500: #f59e0b;

    /* Yellow Scale (Warning Semantic Family) */
    --tk-yellow-50: #fffbeb;
    --tk-yellow-100: #fef3c7;
    --tk-yellow-300: #fde047;
    --tk-yellow-500: #eab308;
    --tk-yellow-700: #a16207;

    /* Green Scale (Success Semantic Family) */
    --tk-green-50: #f0fdf4;
    --tk-green-100: #dcfce7;
    --tk-green-300: #86efac;
    --tk-green-400: #4ade80;
    --tk-green-500: #22c55e;
    --tk-green-700: #15803d;
    --tk-green-800: #166534;
    --tk-emerald-500: #10b981;

    /* Blue Scale (Informational Semantic Family) */
    --tk-blue-50: #eff6ff;
    --tk-blue-100: #dbeafe;
    --tk-blue-300: #93c5fd;
    --tk-blue-400: #60a5fa;
    --tk-blue-500: #3b82f6;
    --tk-blue-600: #2563eb;
    --tk-blue-700: #1d4ed8;
    --tk-blue-800: #1e40af;
    --tk-blue-900: #1e3a8a;

    /* Neutral Colors */
    --tk-white: #ffffff;
    --tk-black: #000000;
    --tk-white-rgb: 255, 255, 255;
    --tk-black-rgb: 0, 0, 0;

    --tk-gray-50: #fafafa;
    --tk-gray-100: #f3f4f6;
    --tk-gray-200: #e5e7eb;
    --tk-gray-300: #d1d5db;
    --tk-gray-400: #9ca3af;
    --tk-gray-500: #6b7280;
    --tk-gray-600: #4b5563;
    --tk-gray-700: #374151;
    --tk-gray-800: #1f2937;
    --tk-gray-900: #111827;

    /* Semantic Color Palette Tokens */
    --tk-red-100: #fee2e2;
    --tk-red-300: #fca5a5;
    --tk-red-600: #dc2626;
    --tk-red-700: #b91c1c;

    /* === GRADIENT DEFINITIONS (Role Tokens) === */
    --gradient-primary-brand: linear-gradient(to right, var(--tk-purple-500), var(--tk-indigo-500));
    --gradient-secondary-brand: linear-gradient(to right, var(--tk-blue-500), var(--tk-cyan-500));
    --gradient-accent-brand: linear-gradient(to right, var(--tk-pink-500), var(--tk-rose-500));
    --gradient-violet-accent: linear-gradient(to right, var(--tk-purple-600), var(--tk-violet-400));
    --gradient-violet-pink-accent: linear-gradient(to right, var(--tk-violet-500), var(--tk-pink-400));
    --gradient-dark-purple-hero: linear-gradient(
      135deg,
      var(--tk-purple-950),
      var(--tk-indigo-800),
      var(--tk-purple-700)
    );

    /* === GENERAL UI COMPONENT & ROLE-BASED TOKENS (Light Theme Defaults) === */
    /* Backgrounds */
    --bg-page: var(--tk-gray-50);
    --bg-content: var(--tk-white);
    --bg-card: var(--tk-white);
    --bg-sidebar: var(--tk-white);
    --bg-input: var(--tk-white);
    --bg-subtle: var(--tk-gray-100);
    --bg-hover-active: var(--tk-purple-100);
    --bg-overlay: rgba(var(--tk-black-rgb), 0.4);
    --bg-hero-dark-purple: var(--gradient-dark-purple-hero);

    /* Text */
    --text-primary: var(--tk-gray-900);
    --text-secondary: var(--tk-gray-700);
    --text-subtle: var(--tk-gray-500);
    --text-on-dark-bg: var(--tk-white);
    --text-on-color: var(--tk-white);
    --text-link: var(--tk-purple-600);
    --text-link-hover: var(--tk-purple-700);
    --text-heading-brand: var(--tk-purple-700);
    --text-placeholder: var(--tk-gray-500);
    --text-disabled: var(--tk-gray-600);

    /* Borders */
    --border-standard: var(--tk-gray-200);
    --border-input: var(--tk-gray-400);
    --border-divider: var(--tk-gray-200);
    --border-focus-ring-color: var(--tk-purple-500);
    --border-sidebar: var(--tk-purple-200);
    --border-interactive-strong: var(--tk-purple-500);

    /* Semantic States (Role Tokens) */
    --semantic-success-color-text: var(--tk-green-700);
    --semantic-success-color-bg: var(--tk-green-100);
    --semantic-success-color-border: var(--tk-green-300);
    --semantic-success-color-icon: var(--tk-green-500);

    --semantic-error-color-text: var(--tk-red-700);
    --semantic-error-color-bg: var(--tk-red-100);
    --semantic-error-color-border: var(--tk-red-300);
    --semantic-error-color-icon: var(--tk-red-600);

    --semantic-warning-color-text: var(--tk-yellow-700);
    --semantic-warning-color-bg: var(--tk-yellow-100);
    --semantic-warning-color-border: var(--tk-yellow-300);
    --semantic-warning-color-icon: var(--tk-yellow-500);

    --semantic-info-color-text: var(--tk-blue-700);
    --semantic-info-color-bg: var(--tk-blue-100);
    --semantic-info-color-border: var(--tk-blue-300);
    --semantic-info-color-icon: var(--tk-blue-500);

    /* Buttons */
    --button-primary-bg-image: var(--gradient-primary-brand);
    --button-primary-color-text: var(--text-on-dark-bg);
    --button-primary-hover-bg-image: linear-gradient(to right, var(--tk-purple-600), var(--tk-indigo-600));

    --button-secondary-bg-image: var(--gradient-secondary-brand);
    --button-secondary-color-text: var(--text-on-dark-bg);
    --button-secondary-hover-bg-image: linear-gradient(to right, var(--tk-blue-600), var(--tk-cyan-600));

    --button-accent-bg-image: var(--gradient-accent-brand);
    --button-accent-color-text: var(--tk-black);
    --button-accent-hover-bg-image: linear-gradient(to right, var(--tk-pink-600), var(--tk-rose-600));

    --button-outline-color-border: var(--tk-purple-500);
    --button-outline-color-text: var(--tk-purple-600);
    --button-outline-hover-bg-color: var(--tk-purple-50);
    --button-outline-hover-color-text: var(--tk-purple-700);

    --button-disabled-bg-color: var(--tk-gray-100);
    --button-disabled-color-text: var(--text-disabled);
    --button-disabled-color-border: var(--tk-gray-200);

    /* Forms */
    --input-border-radius: 8px;
    --input-border-color-focus: var(--tk-purple-500);
    --input-focus-shadow-ring: 0 0 0 3px var(--border-focus-ring-color);
    --input-bg-disabled: var(--tk-gray-100);
    --input-border-color-disabled: var(--tk-gray-200);
    --input-color-text-disabled: var(--text-disabled);

    /* Cards & Modals */
    --card-padding: 1.5rem;
    --card-border-radius: 8px;
    --modal-bg-color: var(--bg-card);

    /* Decorative Elements */
    --decorative-blob-color-1: rgba(139, 92, 246, 0.15); /* Purple 500 with low alpha */
    --decorative-blob-color-2: rgba(6, 182, 212, 0.15); /* Cyan 500 with low alpha */
    --decorative-blob-gradient: radial-gradient(
      circle,
      var(--decorative-blob-color-1) 0%,
      var(--decorative-blob-color-2) 100%
    );

    /* === 3D DEPTH & SHADOW TOKENS === */
    --depth-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05), 0 1px 1px rgba(0, 0, 0, 0.1);
    --depth-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --depth-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --depth-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --depth-shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --depth-shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

    /* === ENHANCED TECHNOKIDS BRANDED SHADOWS === */
    --shadow-purple-glow: 0 4px 14px 0 rgba(139, 92, 246, 0.15);
    --shadow-purple-glow-lg: 0 10px 25px 0 rgba(139, 92, 246, 0.2);
    --shadow-teal-glow: 0 4px 14px 0 rgba(20, 184, 166, 0.15);
    --shadow-gradient-glow: 0 8px 32px 0 rgba(139, 92, 246, 0.12), 0 4px 16px 0 rgba(20, 184, 166, 0.08);

    /* Card Shadows with Brand Colors */
    --card-shadow: var(--depth-shadow-md);
    --card-hover-shadow: var(--depth-shadow-lg), var(--shadow-purple-glow);
    --card-focus-shadow: var(--depth-shadow-xl), var(--shadow-gradient-glow);

    /* Button Shadows */
    --button-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    --button-hover-shadow: 0 4px 12px rgba(139, 92, 246, 0.15), 0 2px 6px rgba(0, 0, 0, 0.1);
    --button-active-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

    /* === TYPOGRAPHY SCALE TOKENS === */
    --font-family-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif,
      'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';
    --font-family-body: var(--font-family-sans);
    --font-family-headings: var(--font-family-sans);

    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-black: 900;

    --line-height-tight: 1.2;
    --line-height-normal: 1.5;
    --line-height-loose: 1.8;

    --font-size-base: 16px;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-md: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-h6: 1rem;
    --font-size-h5: 1.25rem;
    --font-size-h4: 1.5rem;
    --font-size-h3: clamp(1.5rem, 3vw + 1rem, 2.25rem);
    --font-size-h2: clamp(1.75rem, 4vw + 1rem, 3rem);
    --font-size-h1: clamp(2.25rem, 5vw + 1rem, 3.75rem);

    --letter-spacing-tight: -0.025em;
    --letter-spacing-normal: 0em;

    /* === SPACING SCALE TOKENS === */
    --space-unit: 8px;
    --space-xs: calc(0.5 * var(--space-unit));
    --space-sm: var(--space-unit);
    --space-md: calc(2 * var(--space-unit));
    --space-lg: calc(3 * var(--space-unit));
    --space-xl: calc(4 * var(--space-unit));
    --space-2xl: calc(6 * var(--space-unit));
    --space-3xl: calc(8 * var(--space-unit));
    --space-section-padding-y: var(--space-3xl);

    /* === TRANSITION TOKENS === */
    --transition-speed-fast: 0.2s;
    --transition-speed-normal: 0.3s;
    --transition-timing-function: ease-in-out;
    --transition-default: all var(--transition-speed-normal) var(--transition-timing-function);
    --transition-fast: all var(--transition-speed-fast) var(--transition-timing-function);

    /* General Border Radius */
    --radius: 0.5rem;

    /* === SHADCN UI MAPPINGS (Light Theme Defaults) === */
    --background: var(--tk-gray-50);
    --foreground: var(--tk-gray-900);

    --muted: var(--tk-gray-100);
    --muted-foreground: var(--tk-gray-500);

    --popover: var(--tk-white);
    --popover-foreground: var(--tk-gray-900);

    --card: var(--tk-white);
    --card-foreground: var(--tk-gray-900);

    --border: var(--tk-gray-200);
    --input: var(--tk-gray-400);

    --primary: var(--tk-purple-500);
    --primary-foreground: var(--tk-white);

    --secondary: var(--tk-gray-100);
    --secondary-foreground: var(--tk-gray-900);

    --accent: var(--tk-gray-100);
    --accent-foreground: var(--tk-gray-900);

    --destructive: var(--tk-red-600);
    --destructive-foreground: var(--tk-white);

    --ring: var(--tk-purple-500);
  }

  /* === DARK MODE OVERRIDES === */
  [data-theme='dark'] {
    /* Backgrounds */
    --bg-page: var(--tk-gray-900);
    --bg-content: var(--tk-gray-800);
    --bg-card: var(--tk-gray-800);
    --bg-sidebar: var(--tk-gray-800);
    --bg-input: var(--tk-gray-700);
    --bg-subtle: var(--tk-gray-800);
    --bg-hover-active: rgba(139, 92, 246, 0.15); /* Purple 500 with low alpha */
    --bg-overlay: rgba(var(--tk-black-rgb), 0.6);

    /* Text */
    --text-primary: var(--tk-white);
    --text-secondary: var(--tk-gray-300);
    --text-subtle: var(--tk-gray-400);
    --text-on-dark-bg: var(--tk-white);
    --text-on-color: var(--tk-white);
    --text-link: var(--tk-purple-400);
    --text-link-hover: var(--tk-purple-300);
    --text-heading-brand: var(--tk-purple-300);
    --text-placeholder: var(--tk-gray-400);
    --text-disabled: var(--tk-gray-500);

    /* Borders */
    --border-standard: var(--tk-gray-700);
    --border-input: var(--tk-gray-600);
    --border-divider: var(--tk-gray-700);
    --border-focus-ring-color: var(--tk-purple-400);
    --border-sidebar: var(--tk-gray-700);
    --border-interactive-strong: var(--tk-purple-400);

    /* Semantic States (Dark Theme) */
    --semantic-success-color-text: var(--tk-green-300);
    --semantic-success-color-bg: rgba(22, 101, 52, 0.3); /* Green 800 with alpha */
    --semantic-success-color-border: var(--tk-green-700);
    --semantic-success-color-icon: var(--tk-green-300);

    --semantic-error-color-text: var(--tk-red-300);
    --semantic-error-color-bg: rgba(185, 28, 28, 0.25); /* Red 700 with alpha */
    --semantic-error-color-border: var(--tk-red-600);
    --semantic-error-color-icon: var(--tk-red-300);

    --semantic-warning-color-text: var(--tk-yellow-300);
    --semantic-warning-color-bg: rgba(161, 98, 7, 0.25); /* Yellow 700 with alpha */
    --semantic-warning-color-border: var(--tk-yellow-500);
    --semantic-warning-color-icon: var(--tk-yellow-300);

    --semantic-info-color-text: var(--tk-blue-300);
    --semantic-info-color-bg: rgba(30, 64, 175, 0.25); /* Blue 800 with alpha */
    --semantic-info-color-border: var(--tk-blue-600);
    --semantic-info-color-icon: var(--tk-blue-300);

    /* Buttons (Dark Theme) */
    --button-outline-color-border: var(--tk-purple-400);
    --button-outline-color-text: var(--tk-purple-400);
    --button-outline-hover-bg-color: rgba(167, 139, 250, 0.15); /* Purple 400 with alpha */
    --button-outline-hover-color-text: var(--tk-purple-300);

    --button-disabled-bg-color: var(--tk-gray-700);
    --button-disabled-color-text: var(--tk-gray-400);
    --button-disabled-color-border: var(--tk-gray-600);

    /* Forms (Dark Theme) */
    --input-border-color-focus: var(--tk-purple-400);
    --input-focus-shadow-ring: 0 0 0 3px var(--border-focus-ring-color);
    --input-bg-disabled: var(--tk-gray-800);
    --input-border-color-disabled: var(--tk-gray-700);
    --input-color-text-disabled: var(--tk-gray-500);

    /* Decorative Elements */
    --decorative-blob-color-1: rgba(167, 139, 250, 0.2); /* Purple 400 with alpha */
    --decorative-blob-color-2: rgba(6, 182, 212, 0.25); /* Cyan 500 with alpha */

    /* ShadCN UI Mappings (Dark Theme) */
    --background: var(--tk-gray-900);
    --foreground: var(--tk-white);

    --muted: var(--tk-gray-800);
    --muted-foreground: var(--tk-gray-400);

    --popover: var(--tk-gray-800);
    --popover-foreground: var(--tk-white);

    --card: var(--tk-gray-800);
    --card-foreground: var(--tk-white);

    --border: var(--tk-gray-700);
    --input: var(--tk-gray-600);

    --primary: var(--tk-purple-400);
    --primary-foreground: var(--tk-gray-900);

    --secondary: var(--tk-gray-700);
    --secondary-foreground: var(--tk-white);

    --accent: var(--tk-gray-700);
    --accent-foreground: var(--tk-white);

    --destructive: var(--tk-red-600);
    --destructive-foreground: var(--tk-white);

    --ring: var(--tk-purple-400);
  }

  /* === NIGHT MODE OVERRIDES (Warmer, Desaturated) === */
  [data-theme='night'] {
    /* Night theme colors from TechnoKids v18 */
    --bg-page: #2e2925;
    --bg-content: #3a342f;
    --bg-card: #3a342f;
    --bg-sidebar: #3a342f;
    --bg-input: #4a4540;
    --bg-subtle: #3a342f;
    --bg-hover-active: #4a4540;
    --bg-overlay: rgba(var(--tk-black-rgb), 0.7);

    /* Text */
    --text-primary: #d8cec4;
    --text-secondary: #a89f95;
    --text-subtle: #7a736a;
    --text-on-dark-bg: var(--text-primary);
    --text-on-color: var(--text-primary);
    --text-link: #9c80b1;
    --text-link-hover: #b298c1;
    --text-heading-brand: #9c80b1;
    --text-placeholder: #a89f95;
    --text-disabled: #7a736a;

    /* Borders */
    --border-standard: #5a4f45;
    --border-input: #6a635a;
    --border-divider: #5a4f45;
    --border-focus-ring-color: #9c80b1;
    --border-sidebar: #5a4f45;
    --border-interactive-strong: #9c80b1;

    /* ShadCN UI Mappings (Night Theme) */
    --background: #2e2925;
    --foreground: #d8cec4;

    --muted: #3a342f;
    --muted-foreground: #a89f95;

    --popover: #2e2925;
    --popover-foreground: #d8cec4;

    --card: #3a342f;
    --card-foreground: #d8cec4;

    --border: #5a4f45;
    --input: #5a4f45;

    --primary: #9c80b1;
    --primary-foreground: #d8cec4;

    --secondary: #5a8e8a;
    --secondary-foreground: #d8cec4;

    --accent: #a96b8e;
    --accent-foreground: #d8cec4;

    --destructive: #c88b8b;
    --destructive-foreground: #d8cec4;

    --ring: #9c80b1;
  }

  /* === BASE HTML ELEMENT STYLING === */
  body {
    font-family: var(--font-family-body);
    font-size: var(--font-size-md);
    color: var(--text-primary);
    line-height: var(--line-height-normal);
    font-weight: var(--font-weight-normal);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: var(--font-family-headings);
    color: var(--text-primary);
    line-height: var(--line-height-tight);
    margin-bottom: var(--space-md);
  }

  h1 {
    font-size: var(--font-size-h1);
    font-weight: var(--font-weight-black);
  }
  h2 {
    font-size: var(--font-size-h2);
    font-weight: var(--font-weight-bold);
  }
  h3 {
    font-size: var(--font-size-h3);
    font-weight: var(--font-weight-bold);
  }
  h4 {
    font-size: var(--font-size-h4);
    font-weight: var(--font-weight-bold);
  }
  h5 {
    font-size: var(--font-size-h5);
    font-weight: var(--font-weight-bold);
  }
  h6 {
    font-size: var(--font-size-h6);
    font-weight: var(--font-weight-bold);
  }

  p {
    margin-bottom: var(--space-md);
    color: var(--text-secondary);
    line-height: var(--line-height-normal);
  }

  a {
    color: var(--text-link);
    font-weight: var(--font-weight-semibold);
    text-decoration: none;
    transition: var(--transition-fast);
  }

  a:hover,
  a:focus {
    color: var(--text-link-hover);
    text-decoration: underline;
  }

  .decorative-blob {
    position: absolute;
    z-index: -1;
    border-radius: 50%;
    opacity: 0.3;
    width: 300px;
    height: 300px;
    background-image: var(--decorative-blob-gradient);
    filter: blur(50px);
  }

  [data-theme='dark'] .decorative-blob {
    opacity: 0.2;
  }
  [data-theme='night'] .decorative-blob {
    opacity: 0.25;
  }

  /* === ENHANCED ANIMATIONS & MICRO-INTERACTIONS === */
  @keyframes float {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  @keyframes glow-pulse {
    0%,
    100% {
      box-shadow: var(--shadow-purple-glow);
    }
    50% {
      box-shadow: var(--shadow-purple-glow-lg);
    }
  }

  @keyframes gradient-shift {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-glow-pulse {
    animation: glow-pulse 2s ease-in-out infinite;
  }

  .animate-gradient-shift {
    background-size: 200% 200%;
    animation: gradient-shift 3s ease infinite;
  }

  /* Enhanced focus states */
  .focus-ring-enhanced:focus-visible {
    outline: none;
    box-shadow:
      0 0 0 3px var(--border-focus-ring-color),
      var(--shadow-purple-glow);
  }

  /* Smooth scroll behavior */
  html {
    scroll-behavior: smooth;
  }

  /* Enhanced selection colors */
  ::selection {
    background-color: var(--tk-purple-200);
    color: var(--tk-purple-900);
  }

  [data-theme='dark'] ::selection {
    background-color: var(--tk-purple-700);
    color: var(--tk-purple-100);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

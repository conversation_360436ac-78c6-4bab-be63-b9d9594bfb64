{"Index": {"meta_title": "TechnoKidsAI - Chat IA pour l'Éducation K12", "meta_description": "Une application de chat IA engageante pour les enseignants et les élèves K12, favorisant l'apprentissage interactif et la créativité."}, "Navbar": {"sign_in": "Se connecter", "sign_up": "S'inscrire", "product": "Produit", "docs": "Docs", "blog": "Blog", "community": "Communauté", "company": "Entreprise"}, "Hero": {"follow_twitter": "Su<PERSON>z @TechnoKidsAI sur Twitter", "title": "Bienvenue sur <important>TechnoKidsAI</important>: Éveiller la Curiosité dans l'Apprentissage K12.", "description": "Une application de chat IA engageante pour les enseignants et les élèves K12, favorisant l'apprentissage interactif et la créativité. Construit avec Next.js, TypeScript et Tailwind CSS.", "primary_button": "Découvrir TechnoKidsAI", "secondary_button": "En savoir plus"}, "Sponsors": {"title": "Soutenu par"}, "Features": {"section_subtitle": "Fonctionnalités", "section_title": "Découvrez la Puissance de TechnoKidsAI", "section_description": "TechnoKidsAI offre une suite d'outils conçus pour améliorer l'expérience éducative K12 grâce à l'IA interactive.", "feature1_title": "Chat Interactif", "feature2_title": "Support de Curriculum", "feature3_title": "<PERSON><PERSON> <PERSON>", "feature4_title": "Analyse des Performances", "feature5_title": "Personnalisation de l'Apprentissage", "feature6_title": "Intégration Facile", "feature_description": "Chaque fonctionnalité est conçue pour offrir une expérience d'apprentissage enrichie, rendant l'éducation plus accessible et engageante."}, "Pricing": {"section_subtitle": "Tarification", "section_title": "Choisissez le Plan qui Vous Convient", "section_description": "Des options flexibles pour s'adapter aux besoins de chaque éducateur et élève.", "button_text": "Commencer Gratuitement"}, "PricingPlan": {"free_plan_name": "<PERSON><PERSON><PERSON>", "premium_plan_name": "Premium", "enterprise_plan_name": "Enterprise", "basic_classroom_plan_name": "Plan Classe de Base", "basic_classroom_plan_description": "Idéal pour les enseignants individuels", "school_wide_plan_name": "Plan École Entière", "school_wide_plan_description": "Parfait pour une école complète", "district_level_plan_name": "Plan Niveau District", "district_level_plan_description": "Pour les déploiements à l'échelle du district", "free_plan_description": "Pour les particuliers", "premium_plan_description": "Pour les petites équipes", "enterprise_plan_description": "Pour les leaders de l'industrie", "feature_team_member": "{number} membres", "feature_website": "{number} sites internet", "feature_storage": "{number} Go de stockage", "feature_transfer": "{number} TB de transfert", "feature_email_support": "Support par e-mail", "plan_interval_month": "mois", "plan_interval_year": "ann<PERSON>", "next_renew_date": "Votre abonnement sera renouvelé le {date}"}, "FAQ": {"question": "Lorem ipsum dolor sit amet, consectetur adipiscing elit?", "answer": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam aliquam enim dui, id consequat turpis ullamcorper ac. Mauris id quam dolor. Nullam eu egestas turpis. Proin risus elit, sollicitudin in mi a, accumsan euismod turpis. In euismod mi sed diam tristique hendrerit."}, "CTA": {"title": "Vous êtes prêt?", "description": "Lorem ipsum dolor sit amet, consectetur adipiscing elit.", "button_text": "<PERSON><PERSON>z une étoile sur GitHub"}, "Footer": {"product": "Produit", "docs": "Docs", "blog": "Blog", "community": "Communauté", "company": "Entreprise", "terms_of_service": "Conditions d'utilisation", "privacy_policy": "Politique de confidentialité", "designed_by": "Designé by <author></author>."}, "ProtectFallback": {"not_enough_permission": "Vous n'avez pas les permissions pour effectuer cette action"}, "SignIn": {"meta_title": "Se connecter", "meta_description": "Connectez-vous à votre compte avec facilité."}, "SignUp": {"meta_title": "S'inscrire", "meta_description": "Créez un compte sans effort grâce à notre processus d'inscription intuitif."}, "DashboardLayout": {"home": "Accueil", "todos": "Todos", "members": "Me<PERSON><PERSON>", "billing": "Facturation", "settings": "Réglages"}, "Dashboard": {"meta_title": "Tableau de bord de TechnoKidsAI", "meta_description": "Une application d'IA pour l'éducation K12, construite avec Next.js, TypeScript, Shadcn UI et Tailwind CSS."}, "DashboardIndex": {"title_bar": "Tableau de bord", "title_bar_description": "Bienvenue sur votre tableau de bord", "message_state_title": "C'est parti", "message_state_description": "Vous pouvez personnaliser cette page en modifiant le fichier dans <code>dashboard/page.tsx</code>", "message_state_button": "<PERSON><PERSON>z une étoile sur GitHub", "message_state_alternative": "Vous voulez plus de fonctionnalités en utilisant la même stack ? Essayez <url></url>."}, "UserProfile": {"title_bar": "Profil utilisateur", "title_bar_description": "<PERSON><PERSON><PERSON><PERSON> et gérer votre profil utilisateur"}, "OrganizationProfile": {"title_bar": "Gestion de l’organisation", "title_bar_description": "Gérer votre organisation"}, "Billing": {"title_bar": "Facturation", "title_bar_description": "Gérer votre facturation et votre abonnement", "current_section_title": "Plan actuel", "current_section_description": "Ajuster votre plan de paiement pour le mieux répondre à vos besoins", "manage_subscription_button": "<PERSON><PERSON>rer l'abonnement"}, "BillingOptions": {"current_plan": "Plan actuel", "upgrade_plan": "<PERSON><PERSON><PERSON><PERSON>"}, "CheckoutConfirmation": {"title_bar": "Confirmation du paiement", "message_state_title": "Paiement accepté", "message_state_description": "Votre paiement a été traité avec succès. Merci pour votre achat !", "message_state_button": "Revenir à la facturation"}, "DataTable": {"no_results": "Aucun résultat."}, "Todos": {"title_bar": "Liste de Todos", "title_bar_description": "Afficher et gérer votre liste de todos", "add_todo_button": "Nouveau todo"}, "TodoTableColumns": {"open_menu": "<PERSON><PERSON><PERSON><PERSON><PERSON> le menu", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "title_header": "Titre", "message_header": "Message", "created_at_header": "<PERSON><PERSON><PERSON>"}, "AddTodo": {"title_bar": "Ajouter une todo", "add_todo_section_title": "<PERSON><PERSON><PERSON> une nouvelle todo", "add_todo_section_description": "Remplissez le formulaire ci-dessous pour créer une nouvelle todo"}, "EditTodo": {"title_bar": "Editer le todo", "edit_todo_section_title": "Modifier le todo", "edit_todo_section_description": "Remplissez le formulaire ci-dessous pour modifier la todo"}, "TodoForm": {"title_label": "Titre", "title_description": "Entrez un titre descriptif pour votre todo.", "message_title": "Message", "message_description": "Entrez un message détaillé pour votre todo.", "submit_button": "Envoyer"}}
{"Index": {"meta_title": "TechnoKidsAI - AI Chat for K12 Education", "meta_description": "An engaging AI chat application for K12 teachers and students, fostering interactive learning and creativity."}, "Navbar": {"sign_in": "Sign In", "sign_up": "Sign Up", "product": "Product", "docs": "Docs", "blog": "Blog", "community": "Community", "company": "Company"}, "Hero": {"follow_twitter": "Follow @TechnoKidsAI on Twitter", "title": "Welcome to <important>TechnoKidsAI</important>: Sparking Curiosity in K12 Learning.", "description": "An engaging AI chat application for K12 teachers and students, fostering interactive learning and creativity. Built with Next.js, TypeScript, and Tailwind CSS.", "primary_button": "Explore TechnoKidsAI", "secondary_button": "Learn More"}, "Sponsors": {"title": "Supported by"}, "Features": {"section_subtitle": "Features", "section_title": "Discover the Power of TechnoKidsAI", "section_description": "TechnoKidsAI offers a suite of tools designed to enhance the K12 educational experience through interactive AI.", "feature1_title": "Interactive Chat", "feature2_title": "Curriculum Support", "feature3_title": "Teacher Dashboard", "feature4_title": "Student Analytics", "feature5_title": "Safe & Secure", "feature6_title": "Easy Integration", "feature_description": "TechnoKidsAI is designed to be a helpful and engaging tool for both students and teachers in the K12 environment."}, "Pricing": {"section_subtitle": "Plans", "section_title": "Choose Your TechnoKidsAI Plan", "section_description": "Flexible plans for individual classrooms, schools, and districts.", "button_text": "Get Started"}, "PricingPlan": {"basic_classroom_plan_name": "Basic Classroom", "basic_classroom_plan_description": "For individual teachers and small group exploration.", "school_wide_plan_name": "School Wide", "school_wide_plan_description": "Comprehensive access for all teachers and students in a single school.", "district_level_plan_name": "District Level", "district_level_plan_description": "Tailored solutions and support for entire school districts.", "free_plan_name": "Basic Classroom", "premium_plan_name": "School Wide", "enterprise_plan_name": "District Level", "free_plan_description": "For individual teachers", "premium_plan_description": "For entire schools", "enterprise_plan_description": "For school districts", "feature_team_member": "{number} Team Members", "feature_website": "{number} Websites", "feature_storage": "{number} GB Storage", "feature_transfer": "{number} TB Transfer", "feature_email_support": "Email Support", "plan_interval_month": "month", "plan_interval_year": "year", "next_renew_date": "Your subscription renews on {date}"}, "FAQ": {"question": "How can TechnoKidsAI help my students?", "answer": "TechnoKidsAI provides a fun and interactive way for students to learn, explore new topics, and get instant feedback. It can supplement classroom instruction and provide personalized learning experiences."}, "CTA": {"title": "Ready to transform learning?", "description": "Bring the power of AI to your classroom with TechnoKidsAI.", "button_text": "Get Started with TechnoKidsAI"}, "Footer": {"product": "Product", "docs": "Docs", "blog": "Blog", "community": "Community", "company": "Company", "terms_of_service": "Terms Of Service", "privacy_policy": "Privacy Policy", "designed_by": "Built by TechnoKidsAI."}, "ProtectFallback": {"not_enough_permission": "You do not have the permissions to perform this action"}, "SignIn": {"meta_title": "Sign in", "meta_description": "Seamlessly sign in to your account with our user-friendly login process."}, "SignUp": {"meta_title": "Sign up", "meta_description": "Effortlessly create an account through our intuitive sign-up process."}, "DashboardLayout": {"home": "Home", "todos": "Todos", "members": "Members", "billing": "Billing", "settings": "Settings"}, "Dashboard": {"meta_title": "TechnoKidsAI Dashboard", "meta_description": "Manage your TechnoKidsAI classes, students, and activities."}, "DashboardIndex": {"title_bar": "TechnoKidsAI Dashboard", "title_bar_description": "Welcome to your TechnoKidsAI portal", "message_state_title": "Let's get started with TechnoKidsAI!", "message_state_description": "You can customize this page by editing the file at <code>dashboard/page.tsx</code>", "message_state_button": "Explore Features", "message_state_alternative": "Need help? Visit our <url>support page</url>."}, "UserProfile": {"title_bar": "User Profile", "title_bar_description": "View and manage your user profile"}, "OrganizationProfile": {"title_bar": "Organization Management", "title_bar_description": "Manage your organization"}, "Billing": {"title_bar": "Billing", "title_bar_description": "Manage your billing and subscription", "current_section_title": "Current Plan", "current_section_description": "Adjust your payment plan to best suit your requirements", "manage_subscription_button": "Manage Subscription"}, "BillingOptions": {"current_plan": "Current Plan", "upgrade_plan": "Get Started"}, "CheckoutConfirmation": {"title_bar": "Payment Confirmation", "message_state_title": "Payment successful", "message_state_description": "Your payment has been successfully processed. Thank you for your purchase!", "message_state_button": "Go back to Billing"}, "DataTable": {"no_results": "No results."}, "Todos": {"title_bar": "Todo List", "title_bar_description": "View and manage your todo list", "add_todo_button": "New todo"}, "TodoTableColumns": {"open_menu": "Open menu", "edit": "Edit", "delete": "Delete", "title_header": "Title", "message_header": "Message", "created_at_header": "Created at"}, "AddTodo": {"title_bar": "Add <PERSON>", "add_todo_section_title": "Create a new todo", "add_todo_section_description": "Fill in the form below to create a new todo"}, "EditTodo": {"title_bar": "Edit todo", "edit_todo_section_title": "Modify todo", "edit_todo_section_description": "Fill in the form below to edit the todo"}, "TodoForm": {"title_label": "Title", "title_description": "Enter a descriptive title for your todo.", "message_title": "Message", "message_description": "Enter a detailed message for your todo.", "submit_button": "Submit"}}
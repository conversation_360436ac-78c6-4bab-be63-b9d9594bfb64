'use client';
import { useEffect, useRef, useState } from 'react';

export function useIntersectionObserver(options?: IntersectionObserverInit) {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [hasBeenVisible, setHasBeenVisible] = useState(false);
  const ref = useRef<HTMLDivElement>(null); // Specify the element type for the ref

  useEffect(() => {
    const observer = new IntersectionObserver((entries) => {
      const entry = entries[0];
      if (entry?.isIntersecting) {
        setIsIntersecting(true);
        setHasBeenVisible(true); // Stay visible once seen
        // Optionally, unobserve after it has become visible if animation is one-time
        // if (ref.current) {
        //   observer.unobserve(ref.current);
        // }
      }
    }, options);

    const currentRef = ref.current; // Capture ref.current in a variable

    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef); // Use the captured variable in cleanup
      }
    };
  }, [ref, options]); // ref should be stable, options might change

  return { ref, isIntersecting, hasBeenVisible };
}

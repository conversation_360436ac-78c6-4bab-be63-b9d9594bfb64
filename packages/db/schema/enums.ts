// SCH-ENUM: Core Enum Definitions
import { pgEnum } from 'drizzle-orm/pg-core';

// SCH-ENUM-APP-USER-ROLE: Application-specific roles for users within educational contexts
export const appUserRoleEnum = pgEnum('app_user_role_enum', [
  'teacher', 'student', 'admin', 'parent', 'guardian', 'aide', 'relative', 'proctor', 'system_support', 'observer'
]);

// SCH-ENUM-APP-ACAD-SESS-TYPE: Defines types of academic periods
export const appAcademicSessionTypeEnum = pgEnum('app_academic_session_type_enum', [
  'semester', 'schoolYear', 'gradingPeriod', 'term', 'summerSession', 'intersession'
]);

// SCH-ENUM-APP-ENTITY-STATUS: Defines statuses for various entities
export const appEntityStatusEnum = pgEnum('app_entity_status_enum', [
  'active', 'inactive', 'archived', 'pending', 'declined', 'deleted', 'tobedeleted', 'provisioned', 'completed', 'planned'
]);

// SCH-ENUM-APP-STREAM-ITEM-TYPE: Defines types of content items in a section's stream
export const appStreamItemTypeEnum = pgEnum('app_stream_item_type_enum', [
  'assignment', 'material', 'announcement', 'question'
]);

// SCH-ENUM-APP-SUBMISSION-STATE: Defines states for student submissions
export const appSubmissionStateEnum = pgEnum('app_submission_state_enum', [
  'new', 'created', 'submitted', 'returned', 'reclaimed_by_student', 'graded', 'late', 'missing'
]);

// SCH-ENUM-APP-INVITATION-STATUS: Defines statuses for invitations
export const appInvitationStatusEnum = pgEnum('app_invitation_status_enum', [
  'pending', 'accepted', 'declined', 'expired'
]);

// SCH-ENUM-APP-EXTERNAL-SYSTEM: Defines types of external systems
export const appExternalSystemEnum = pgEnum('app_external_system_enum', [
  'google_classroom', 'oneroster_webhook', 'lti_tool', 'custom_api'
]);

// SCH-ENUM-APP-SECTION-ALIAS-NAMESPACE: Defines namespaces for section aliases
export const appSectionAliasNamespaceEnum = pgEnum('app_section_alias_namespace_enum', [
  'DOMAIN', 'DEVELOPER_PROJECT'
]);

// SCH-ENUM-APP-ORG-TYPE: Defines types for organizational units
export const appOrgTypeEnum = pgEnum('app_org_type_enum', [
  'district', 'school', 'department', 'team', 'group', 'state', 'national', 'agency', 'consortium', 'other'
]);

// SCH-ENUM-APP-ONEROSTER-USER-STATUS: Defines specific user statuses from OneRoster
export const appOneRosterUserStatusEnum = pgEnum('app_one_roster_user_status_enum', [
  'active', 'tobedeleted', 'inactive'
]);

// SCH-ENUM-APP-ATTACHMENT-TYPE: Defines generic types for attachments
export const appAttachmentTypeEnum = pgEnum('app_attachment_type_enum', [
  'driveFile', 'youtubeVideo', 'link', 'form', 'add_on', 'file', 'image', 'video', 'audio', 'pdf', 'document', 'spreadsheet', 'presentation', 'other'
]);

// SCH-ENUM-APP-ASSIGNMENT-WORK-TYPE: Defines the type of work expected for an assignment
export const appAssignmentWorkTypeEnum = pgEnum('app_assignment_work_type_enum', [
  'ASSIGNMENT', 'SHORT_ANSWER_QUESTION', 'MULTIPLE_CHOICE_QUESTION', 'EXTERNAL_TOOL'
]);

// SCH-ENUM-APP-GLOBAL-ROLE: Defines application-specific global role for a user
export const appGlobalRoleEnum = pgEnum('app_global_role_enum', [
  'SuperAdmin', 'AppCreator', 'StandardUser'
]);

// SCH-ENUM-APP-API-KEY-STATUS: Defines the status of an API Key
export const appApiKeyStatusEnum = pgEnum('app_api_key_status_enum', ['active', 'revoked', 'expired']);

// SCH-ENUM-APP-SAAS-ENTITY-STATUS: Defines the status of a general SaaS entity
export const appSaaSEntityStatusEnum = pgEnum('app_saas_entity_status_enum', [
  'active', 'archived', 'pending', 'completed', 'on_hold', 'cancelled', 'draft', 'published'
]);

// SCH-ENUM-APP-PRIORITY: Defines priority levels
export const appPriorityEnum = pgEnum('app_priority_enum', ['low', 'medium', 'high', 'critical']);

// SCH-ENUM-AI-JOB-STATUS: Defines AI Chat Job Status
export const aiJobStatusEnum = pgEnum('ai_job_status_enum', [
  'active', 'archived', 'processing_user_input', 'awaiting_ai_stream_initiation', 'streaming_ai_response', 'completed_turn', 'failed_ai_response', 'stopped_by_user'
]);

// SCH-ENUM-AI-MESSAGE-ROLE: Defines AI Message Roles
export const aiMessageRoleEnum = pgEnum('ai_message_role_enum', ['user', 'model', 'system_info', 'error_info', 'tool_request', 'tool_response']);

// SCH-ENUM-AI-FILE-UPLOAD-STATUS: Defines AI File Upload Status
export const aiFileUploadStatusEnum = pgEnum('ai_file_upload_status_enum', [
  'pending_client_upload', 'uploading_to_storage', 'uploaded_to_storage', 'pending_processing', 'processing_text_extraction', 'processing_vision_analysis', 'processed_successfully', 'error_client_upload', 'error_storage_upload', 'error_processing'
]);

// SCH-ENUM-AI-MODEL-CONFIG-STATUS: Defines AI Model Configuration Status
export const aiModelConfigStatusEnum = pgEnum('ai_model_config_status_enum', ['active', 'beta', 'deprecated', 'restricted', 'disabled']);

// SCH-ENUM-AI-FEEDBACK-RATING: Defines AI Feedback Rating
export const aiFeedbackRatingEnum = pgEnum('ai_feedback_rating_enum', ['thumbs_up', 'thumbs_down']);

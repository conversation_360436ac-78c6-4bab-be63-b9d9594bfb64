// SCH-INDEX: Barrel File for All Schema Exports
// This file exports all tables, enums, and relations for use throughout the application

// SCH-ENUM: Export all enums
export * from './enums';

// SCH-CORE-TBL: Export core tables
export * from './core';

// SCH-RBAC-TBL: Export RBAC tables
export * from './rbac';

// SCH-SAAS-TBL: Export SaaS support tables
export * from './saas';

// SCH-SAAS-CRUD-TBL: Export SaaS CRUD example tables
export * from './saas_crud_examples';

// SCH-AI-TBL: Export AI tables
export * from './ai';

// SCH-EDU-TBL: Export educational data tables
export * from './ed_data';

// SCH-REL: Export all relations
export * from './relations';

// SCH-INDEX-LEGACY: Legacy exports for backward compatibility
// These maintain compatibility with existing code that imports from the old schema location
export {
  organizationSchema as organization,
  todoSchema as todo
} from './core';

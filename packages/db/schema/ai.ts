// SCH-AI-TBL: AI Chat Application Data Tables
import { pgTable, text, timestamp, jsonb, varchar, serial, index, integer, boolean, bigint, uniqueIndex } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { users, organizations } from './core'; // SCH-CORE-TBL
import {
  aiJobStatusEnum, aiMessageRoleEnum, aiFileUploadStatusEnum, aiModelConfigStatusEnum, aiFeedbackRatingEnum
} from './enums'; // SCH-ENUM

// SCH-TBL-AI-JOB: ai_jobs Table (AI Chat Sessions)
export const aiJobs = pgTable('ai_jobs', {
  id: serial('id').primaryKey(), // SCH-TBL-AI-JOB-ID

  userId: varchar('user_id', { length: 255 }).references(() => users.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-AI-JOB-USER-ID
  organizationId: varchar('organization_id', { length: 255 }).references(() => organizations.id, { onDelete: 'cascade' }), // SCH-TBL-AI-JOB-ORG-ID

  jobNamePreview: text('job_name_preview').notNull(), // SCH-TBL-AI-JOB-NAME-PREVIEW

  modelConfigurationSnapshotJsonb: jsonb('model_configuration_snapshot_jsonb').notNull().$type<{ // SCH-TBL-AI-JOB-MODEL-CFG-SNAP
    modelConfigId?: number | null;
    modelSlug: string;
    modelApiIdentifier: string;
    provider: string;
    configParams?: Record<string, any> | null;
    safetySettings?: Record<string, any>[] | null;
    generationConfig?: Record<string, any> | null;
    capabilities?: string[] | null;
    maxInputTokensSnapshot?: number | null;
    maxOutputTokensSnapshot?: number | null;
  }>(),

  status: aiJobStatusEnum('status').default('active').notNull(), // SCH-TBL-AI-JOB-STATUS

  errorDetailsJsonb: jsonb('error_details_jsonb').$type<{ // SCH-TBL-AI-JOB-ERROR-DETAILS
    code?: string | null;
    message?: string | null;
    timestamp?: string | null;
    isRetryable?: boolean | null;
    providerErrorDetails?: string | null;
  }>().default({}),

  totalUserMessages: integer('total_user_messages').default(0).notNull(), // SCH-TBL-AI-JOB-TOTAL-USER-MSG
  totalAiMessages: integer('total_ai_messages').default(0).notNull(), // SCH-TBL-AI-JOB-TOTAL-AI-MSG
  totalPromptTokensInJob: integer('total_prompt_tokens_in_job').default(0).notNull(), // SCH-TBL-AI-JOB-TOTAL-PROMPT-TOK
  totalCompletionTokensInJob: integer('total_completion_tokens_in_job').default(0).notNull(), // SCH-TBL-AI-JOB-TOTAL-COMPLETION-TOK

  tagsJsonb: jsonb('tags_jsonb').$type<string[]>().default([]), // SCH-TBL-AI-JOB-TAGS

  visibility: text('visibility').default('private').notNull(), // SCH-TBL-AI-JOB-VISIBILITY
  shareToken: varchar('share_token', { length: 128 }).unique(), // SCH-TBL-AI-JOB-SHARE-TOKEN

  lastMessagePreview: text('last_message_preview'), // SCH-TBL-AI-JOB-LAST-MSG-PREVIEW

  contextCacheJsonb: jsonb('context_cache_jsonb').$type<{ // SCH-TBL-AI-JOB-CONTEXT-CACHE
    cacheId?: string | null;
    lastUsedAt?: string | null;
    tokenCount?: number | null;
    modelApiIdentifierUsed?: string | null;
  }>().default({}),

  activeFileContextIdsJsonb: jsonb('active_file_context_ids_jsonb').$type<number[]>().default([]), // SCH-TBL-AI-JOB-ACTIVE-FILE-CTX-IDS

  pinnedAt: timestamp('pinned_at', { withTimezone: true, mode: 'date' }), // SCH-TBL-AI-JOB-PINNED-AT
  archivedAt: timestamp('archived_at', { withTimezone: true, mode: 'date' }), // SCH-TBL-AI-JOB-ARCHIVED-AT
  lastActivityAt: timestamp('last_activity_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-AI-JOB-LAST-ACTIVITY

  createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-AI-JOB-CREATED-AT
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-AI-JOB-UPDATED-AT
}, (table) => {
  return {
    userIdIdx: index('ai_job_user_id_idx').on(table.userId),
    organizationIdIdx: index('ai_job_org_id_idx').on(table.organizationId).nullsLast(),
    statusIdx: index('ai_job_status_idx').on(table.status),
    lastActivityAtIdx: index('ai_job_last_activity_at_idx').on(table.lastActivityAt).desc(),
    pinnedAtIdx: index('ai_job_pinned_at_idx').on(table.pinnedAt).desc().nullsLast(),
    shareTokenIdx: uniqueIndex('ai_job_share_token_idx').on(table.shareToken),
  };
});

// SCH-TBL-AI-MSG: ai_messages Table (AI Chat Messages)
export const aiMessages = pgTable('ai_messages', {
  id: serial('id').primaryKey(), // SCH-TBL-AI-MSG-ID
  jobId: integer('job_id').references(() => aiJobs.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-AI-MSG-JOB-ID

  senderIdentifier: text('sender_identifier').notNull(), // SCH-TBL-AI-MSG-SENDER-ID
  role: aiMessageRoleEnum('role').notNull(), // SCH-TBL-AI-MSG-ROLE

  content: text('content').notNull(), // SCH-TBL-AI-MSG-CONTENT
  orderIndex: integer('order_index').notNull(), // SCH-TBL-AI-MSG-ORDER-INDEX

  modelRequestDetailsJsonb: jsonb('model_request_details_jsonb').$type<{ // SCH-TBL-AI-MSG-REQ-DETAILS
    promptTokens?: number | null;
    includedHistoryMessageOrderIndices?: number[] | null;
    includedFileContextIds?: number[] | null;
    modelConfigSnapshotForTurn?: Record<string, any> | null;
    contextCacheUsedId?: string | null;
  }>().default({}),

  modelResponseMetadataJsonb: jsonb('model_response_metadata_jsonb').$type<{ // SCH-TBL-AI-MSG-RES-METADATA
    completionTokens?: number | null;
    finishReason?: string | null;
    safetyRatings?: Record<string, any>[] | null;
    rawApiResponsePreview?: string | null;
    timeToFirstTokenMs?: number | null;
    totalGenerationTimeMs?: number | null;
    toolCalls?: Record<string, any>[] | null;
  }>().default({}),

  streamStateJsonb: jsonb('stream_state_jsonb').$type<{ // SCH-TBL-AI-MSG-STREAM-STATE
    isStreamingComplete?: boolean | null;
  }>().default({ isStreamingComplete: false }),

  feedbackJsonb: jsonb('feedback_jsonb').$type<{ // SCH-TBL-AI-MSG-FEEDBACK
    rating?: 'thumbs_up' | 'thumbs_down' | null;
    comment?: string | null;
    tags?: string[] | null;
    correctedContent?: string | null;
    feedbackSubmittedAt?: string | null;
  }>().default({}),

  isErrorPlaceholder: boolean('is_error_placeholder').default(false).notNull(), // SCH-TBL-AI-MSG-IS-ERROR-PLACEHOLDER

  attachmentsJsonb: jsonb('attachments_jsonb').$type<Array<{ // SCH-TBL-AI-MSG-ATTACHMENTS
    fileId: number;
    fileNameSnapshot: string;
    fileTypeSnapshot: string;
  }>>().default([]),

  suggestedFollowUpPromptsJsonb: jsonb('suggested_follow_up_prompts_jsonb').$type<string[]>().default([]), // SCH-TBL-AI-MSG-SUGGESTED-PROMPTS
  isPinned: boolean('is_pinned').default(false).notNull(), // SCH-TBL-AI-MSG-IS-PINNED

  createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-AI-MSG-CREATED-AT
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-AI-MSG-UPDATED-AT
  editedAt: timestamp('edited_at', { withTimezone: true, mode: 'date' }), // SCH-TBL-AI-MSG-EDITED-AT
}, (table) => {
  return {
    jobIdOrderIndexUniqueIdx: index('ai_msg_job_id_order_idx').on(table.jobId, table.orderIndex).unique(),
    jobIdIdx: index('ai_msg_job_id_idx').on(table.jobId),
    senderIdentifierIdx: index('ai_msg_sender_id_idx').on(table.senderIdentifier),
    roleIdx: index('ai_msg_role_idx').on(table.role),
  };
});

// SCH-TBL-AI-FILE: ai_uploaded_files_metadata Table (AI Chat File Uploads Metadata)
export const aiUploadedFilesMetadata = pgTable('ai_uploaded_files_metadata', {
  id: serial('id').primaryKey(), // SCH-TBL-AI-FILE-ID

  userId: varchar('user_id', { length: 255 }).references(() => users.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-AI-FILE-USER-ID
  organizationId: varchar('organization_id', { length: 255 }).references(() => organizations.id, { onDelete: 'cascade' }), // SCH-TBL-AI-FILE-ORG-ID

  storagePath: text('storage_path').notNull(), // SCH-TBL-AI-FILE-STORAGE-PATH
  fileName: text('file_name').notNull(),         // SCH-TBL-AI-FILE-NAME
  fileType: text('file_type').notNull(),         // SCH-TBL-AI-FILE-TYPE
  fileSizeBytes: bigint('file_size_bytes', {mode: 'number'}).notNull(), // SCH-TBL-AI-FILE-SIZE

  status: aiFileUploadStatusEnum('status').notNull(), // SCH-TBL-AI-FILE-STATUS

  processingErrorJsonb: jsonb('processing_error_jsonb').$type<{ // SCH-TBL-AI-FILE-PROCESSING-ERROR
    code?: string | null;
    message?: string | null;
    stage?: 'upload' | 'text_extraction' | 'vision_analysis' | 'general_processing' | null;
    retriesAttempted?: number | null;
  }>().default({}),

  extractedTextStoragePath: text('extracted_text_storage_path'), // SCH-TBL-AI-FILE-EXTRACTED-TEXT-PATH
  extractedTextCharCount: integer('extracted_text_char_count'), // SCH-TBL-AI-FILE-EXTRACTED-TEXT-COUNT

  extractedImageEntitiesJsonb: jsonb('extracted_image_entities_jsonb').$type<Array<{ // SCH-TBL-AI-FILE-EXTRACTED-IMG-ENTITIES
    label: string;
    score: number;
    boundingBoxes?: Array<{ x: number, y: number, width: number, height: number }> | null;
    ocrText?: string | null;
  }>>().default([]),

  checksum: varchar('checksum', {length: 64}), // SCH-TBL-AI-FILE-CHECKSUM
  userProvidedDescription: text('user_provided_description'), // SCH-TBL-AI-FILE-USER-DESC

  createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-AI-FILE-CREATED-AT
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-AI-FILE-UPDATED-AT
  processedAt: timestamp('processed_at', { withTimezone: true, mode: 'date' }), // SCH-TBL-AI-FILE-PROCESSED-AT
}, (table) => {
  return {
    userIdIdx: index('ai_file_user_id_idx').on(table.userId),
    organizationIdIdx: index('ai_file_org_id_idx').on(table.organizationId).nullsLast(),
    statusIdx: index('ai_file_status_idx').on(table.status),
    fileTypeIdx: index('ai_file_type_idx').on(table.fileType),
    checksumIdx: index('ai_file_checksum_idx').on(table.checksum).nullsLast(),
  };
});

// SCH-TBL-AI-MODEL-CFG: ai_model_configurations Table (AI Model Configurations)
export const aiModelConfigurations = pgTable('ai_model_configurations', {
  id: serial('id').primaryKey(), // SCH-TBL-AI-MODEL-CFG-ID

  modelSlug: varchar('model_slug', { length: 100 }).notNull().unique(), // SCH-TBL-AI-MODEL-CFG-SLUG
  modelApiIdentifier: varchar('model_api_identifier', { length: 255 }).notNull(), // SCH-TBL-AI-MODEL-CFG-API-ID
  provider: varchar('provider', { length: 100 }).notNull(), // SCH-TBL-AI-MODEL-CFG-PROVIDER

  displayName: text('display_name').notNull(), // SCH-TBL-AI-MODEL-CFG-DISPLAY-NAME
  description: text('description'), // SCH-TBL-AI-MODEL-CFG-DESC

  status: aiModelConfigStatusEnum('status').default('active').notNull(), // SCH-TBL-AI-MODEL-CFG-STATUS

  configParamsJsonb: jsonb('config_params_jsonb').$type<Record<string, any>>().default({}), // SCH-TBL-AI-MODEL-CFG-PARAMS
  safetySettingsJsonb: jsonb('safety_settings_jsonb').$type<Record<string, any>[]>().default([]), // SCH-TBL-AI-MODEL-CFG-SAFETY
  generationConfigJsonb: jsonb('generation_config_jsonb').$type<Record<string, any>>().default({}), // SCH-TBL-AI-MODEL-CFG-GEN-CONFIG

  capabilitiesJsonb: jsonb('capabilities_jsonb').$type<string[]>().default([]), // SCH-TBL-AI-MODEL-CFG-CAPABILITIES

  maxInputTokens: integer('max_input_tokens'), // SCH-TBL-AI-MODEL-CFG-MAX-INPUT-TOK
  maxOutputTokens: integer('max_output_tokens'), // SCH-TBL-AI-MODEL-CFG-MAX-OUTPUT-TOK

  costPerInputToken: text('cost_per_input_token'), // SCH-TBL-AI-MODEL-CFG-COST-INPUT
  costPerOutputToken: text('cost_per_output_token'), // SCH-TBL-AI-MODEL-CFG-COST-OUTPUT

  createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-AI-MODEL-CFG-CREATED-AT
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-AI-MODEL-CFG-UPDATED-AT
}, (table) => {
  return {
    modelSlugIdx: uniqueIndex('ai_model_config_slug_idx').on(table.modelSlug),
    providerIdx: index('ai_model_config_provider_idx').on(table.provider),
    statusIdx: index('ai_model_config_status_idx').on(table.status),
  };
});

// SCH-TBL-AI-SYS-SET: ai_global_system_settings Table (AI Global System Settings)
export const aiGlobalSystemSettings = pgTable('ai_global_system_settings', {
  id: serial('id').primaryKey(), // SCH-TBL-AI-SYS-SET-ID
  settingKey: varchar('setting_key', { length: 255 }).notNull().unique(), // SCH-TBL-AI-SYS-SET-KEY
  settingValueJsonb: jsonb('setting_value_jsonb').$type<any>().notNull(), // SCH-TBL-AI-SYS-SET-VALUE
  description: text('description'), // SCH-TBL-AI-SYS-SET-DESC
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-AI-SYS-SET-UPDATED-AT
}, (table) => {
  return {
    settingKeyIdx: uniqueIndex('ai_global_setting_key_idx').on(table.settingKey),
  };
});

// SCH-TBL-AI-STREAM-TOK: ai_stream_tokens Table (AI Stream Tokens)
export const aiStreamTokens = pgTable('ai_stream_tokens', {
  id: serial('id').primaryKey(), // SCH-TBL-AI-STREAM-TOK-ID
  messageId: integer('message_id').references(() => aiMessages.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-AI-STREAM-TOK-MSG-ID
  tokenIndex: integer('token_index').notNull(), // SCH-TBL-AI-STREAM-TOK-INDEX
  tokenContent: text('token_content').notNull(), // SCH-TBL-AI-STREAM-TOK-CONTENT
  receivedAt: timestamp('received_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-AI-STREAM-TOK-RECEIVED-AT
}, (table) => {
  return {
    messageIdTokenIndexUniqueIdx: index('ai_stream_token_msg_id_token_idx').on(table.messageId, table.tokenIndex).unique(),
    messageIdIdx: index('ai_stream_token_msg_id_idx').on(table.messageId),
  };
});

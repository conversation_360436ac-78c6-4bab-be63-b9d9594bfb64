// SCH-SAAS-TBL: General SaaS Support Tables
import { pgTable, text, timestamp, serial, jsonb, varchar, index, uniqueIndex, boolean } from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { users, organizations } from './core'; // SCH-CORE-TBL
import { appApiKeyStatusEnum } from './enums'; // SCH-ENUM

// SCH-TBL-AUDIT: audit_logs Table
export const auditLogs = pgTable('audit_logs', {
  id: serial('id').primaryKey(), // SCH-TBL-AUDIT-ID

  organizationId: varchar('organization_id', { length: 255 }).references(() => organizations.id, { onDelete: 'set null' }), // SCH-TBL-AUDIT-ORG-ID
  actorUserId: varchar('actor_user_id', { length: 255 }).references(() => users.id, { onDelete: 'set null' }), // SCH-TBL-AUDIT-ACTOR-ID
  actorUserDisplayNameSnapshot: text('actor_user_display_name_snapshot'), // SCH-TBL-AUDIT-ACTOR-NAME-SNAP
  actorUserEmailSnapshot: text('actor_user_email_snapshot'), // SCH-TBL-AUDIT-ACTOR-EMAIL-SNAP

  impersonatorUserId: varchar('impersonator_user_id', { length: 255 }).references(() => users.id, { onDelete: 'set null' }), // SCH-TBL-AUDIT-IMPERSONATOR-ID

  actionSlug: varchar('action_slug', { length: 255 }).notNull(), // SCH-TBL-AUDIT-ACTION-SLUG
  targetResource: jsonb('target_resource').$type<{ // SCH-TBL-AUDIT-TARGET-RES
    type?: string | null;
    id?: string | null;
    name?: string | null;
  }>().default({}),

  changeDetails: jsonb('change_details').$type<{ // SCH-TBL-AUDIT-CHANGE-DETAILS
    before?: Record<string, any> | null;
    after?: Record<string, any> | null;
    diffSummary?: string | null;
    fieldsUpdated?: string[] | null;
  }>().default({}),

  ipAddress: varchar('ip_address', { length: 100 }), // SCH-TBL-AUDIT-IP
  userAgent: text('user_agent'),                   // SCH-TBL-AUDIT-USER-AGENT

  status: text('status').default('success').notNull(), // SCH-TBL-AUDIT-STATUS
  failureReason: text('failure_reason'),              // SCH-TBL-AUDIT-FAIL-REASON

  clientContext: jsonb('client_context').$type<{ // SCH-TBL-AUDIT-CLIENT-CTX
    appVersion?: string | null;
    routeName?: string | null;
    clientRequestId?: string | null;
  }>().default({}),

  sessionId: varchar('session_id', { length: 255 }), // SCH-TBL-AUDIT-SESSION-ID
  traceId: varchar('trace_id', { length: 255 }),    // SCH-TBL-AUDIT-TRACE-ID

  eventTimestamp: timestamp('event_timestamp', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-AUDIT-TIMESTAMP
}, (table) => {
  return {
    organizationIdIdx: index('audit_log_org_id_idx').on(table.organizationId),
    actorUserIdIdx: index('audit_log_actor_user_id_idx').on(table.actorUserId),
    actionSlugIdx: index('audit_log_action_slug_idx').on(table.actionSlug),
    eventTimestampIdx: index('audit_log_event_timestamp_idx').on(table.eventTimestamp).desc(),
    targetResourceTypeIdx: index('audit_log_target_res_type_idx').on(sql`(target_resource->>'type')`),
    targetResourceIdIdx: index('audit_log_target_res_id_idx').on(sql`(target_resource->>'id')`),
    statusIdx: index('audit_log_status_idx').on(table.status),
    impersonatorUserIdIdx: index('audit_log_impersonator_user_id_idx').on(table.impersonatorUserId),
  };
});

// SCH-TBL-NOTIF: notifications Table
export const notifications = pgTable('notifications', {
  id: serial('id').primaryKey(), // SCH-TBL-NOTIF-ID

  recipientUserId: varchar('recipient_user_id', { length: 255 }).references(() => users.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-NOTIF-RECIPIENT-ID
  organizationId: varchar('organization_id', { length: 255 }).references(() => organizations.id, { onDelete: 'cascade' }), // SCH-TBL-NOTIF-ORG-ID

  typeSlug: varchar('type_slug', { length: 100 }).notNull(), // SCH-TBL-NOTIF-TYPE-SLUG
  titleLocalizationKey: text('title_localization_key').notNull(), // SCH-TBL-NOTIF-TITLE-KEY
  messageLocalizationKey: text('message_localization_key').notNull(), // SCH-TBL-NOTIF-MSG-KEY
  messagePayloadJsonb: jsonb('message_payload_jsonb').$type<Record<string, string | number | boolean>>(), // SCH-TBL-NOTIF-MSG-PAYLOAD

  actionLink: text('action_link'),               // SCH-TBL-NOTIF-ACTION-LINK
  actionTextLocalizationKey: text('action_text_localization_key'), // SCH-TBL-NOTIF-ACTION-TEXT-KEY

  isRead: boolean('is_read').default(false).notNull(), // SCH-TBL-NOTIF-IS-READ
  readAt: timestamp('read_at', { withTimezone: true, mode: 'date' }), // SCH-TBL-NOTIF-READ-AT
  isArchived: boolean('is_archived').default(false).notNull(), // SCH-TBL-NOTIF-IS-ARCHIVED
  archivedAt: timestamp('archived_at', { withTimezone: true, mode: 'date' }), // SCH-TBL-NOTIF-ARCHIVED-AT

  priority: text('priority').default('medium'), // SCH-TBL-NOTIF-PRIORITY
  expiresAt: timestamp('expires_at', { withTimezone: true, mode: 'date' }), // SCH-TBL-NOTIF-EXPIRES-AT

  senderInfoJsonb: jsonb('sender_info_jsonb').$type<{ // SCH-TBL-NOTIF-SENDER-INFO
    type: "user" | "system" | "ai_model" | "organization_event";
    id?: string | null;
    name?: string | null;
    avatarUrl?: string | null;
  }>().default({type: 'system'}),

  metadataJsonb: jsonb('metadata_jsonb').$type<Record<string, any>>().default({}), // SCH-TBL-NOTIF-METADATA

  createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-NOTIF-CREATED-AT
}, (table) => {
  return {
    recipientIsReadCreatedIdx: index('notif_recipient_read_created_idx').on(table.recipientUserId, table.isRead, table.createdAt).desc(table.createdAt),
    recipientUserIdIdx: index('notif_recipient_user_id_idx').on(table.recipientUserId),
    organizationIdIdx: index('notif_org_id_idx').on(table.organizationId),
    typeSlugIdx: index('notif_type_slug_idx').on(table.typeSlug),
    expiresAtIdx: index('notif_expires_at_idx').on(table.expiresAt),
  };
});

// SCH-TBL-API-KEYS: app_api_keys Table (If custom application-specific API keys are implemented)
export const appApiKeys = pgTable('app_api_keys', {
  id: serial('id').primaryKey(), // SCH-TBL-API-KEYS-ID
  keyId: varchar('key_id', {length: 100}).notNull().unique(), // SCH-TBL-API-KEYS-KEY-ID

  keyHash: varchar('key_hash', { length: 255 }).notNull(), // SCH-TBL-API-KEYS-KEY-HASH
  keyPrefix: varchar('key_prefix', { length: 20 }).notNull(), // SCH-TBL-API-KEYS-KEY-PREFIX

  label: text('label').notNull(), // SCH-TBL-API-KEYS-LABEL
  scopes: jsonb('scopes').$type<string[]>().default([]).notNull(), // SCH-TBL-API-KEYS-SCOPES

  userId: varchar('user_id', { length: 255 }).references(() => users.id, { onDelete: 'cascade' }), // SCH-TBL-API-KEYS-USER-ID
  organizationId: varchar('organization_id', { length: 255 }).references(() => organizations.id, { onDelete: 'cascade' }), // SCH-TBL-API-KEYS-ORG-ID

  status: appApiKeyStatusEnum('status').default('active').notNull(), // SCH-TBL-API-KEYS-STATUS

  expiresAt: timestamp('expires_at', { withTimezone: true, mode: 'date' }), // SCH-TBL-API-KEYS-EXPIRES-AT
  lastUsedAt: timestamp('last_used_at', { withTimezone: true, mode: 'date' }), // SCH-TBL-API-KEYS-LAST-USED-AT
  ipRestrictionsJsonb: jsonb('ip_restrictions_jsonb').$type<string[]>().default([]), // SCH-TBL-API-KEYS-IP-RESTRICTIONS

  createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-API-KEYS-CREATED-AT
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-API-KEYS-UPDATED-AT
}, (table) => {
  return {
    keyIdIdx: uniqueIndex('app_api_key_key_id_idx').on(table.keyId),
    userIdIdx: index('app_api_key_user_id_idx').on(table.userId).nullsLast(),
    organizationIdIdx: index('app_api_key_org_id_idx').on(table.organizationId).nullsLast(),
  };
});

// SCH-EDU-TBL: Unified Educational Data Model Tables
import { pgTable, text, timestamp, uuid, varchar, index, jsonb, integer, boolean, numeric, primaryKey, date } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { users, organizations } from './core'; // SCH-CORE-TBL
import {
  appAcademicSessionTypeEnum, appEntityStatusEnum, appUserRoleEnum, appInvitationStatusEnum,
  appStreamItemTypeEnum, appAssignmentWorkTypeEnum, appAttachmentTypeEnum, appSubmissionStateEnum,
  appSectionAliasNamespaceEnum, appExternalSystemEnum
} from './enums'; // SCH-ENUM

// SCH-TBL-EDU-SESS: app_academic_sessions Table (Academic Sessions)
export const appAcademicSessions = pgTable('app_academic_sessions', {
  id: uuid('id').primaryKey().defaultRandom(), // SCH-TBL-EDU-SESS-ID

  schoolOrgId: varchar('school_org_id', { length: 255 }).references(() => organizations.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-EDU-SESS-SCHOOL-ORG-ID

  name: text('name').notNull(), // SCH-TBL-EDU-SESS-NAME
  sessionType: appAcademicSessionTypeEnum('session_type').notNull(), // SCH-TBL-EDU-SESS-TYPE
  status: appEntityStatusEnum('status').default('active').notNull(), // SCH-TBL-EDU-SESS-STATUS

  startDate: date('start_date'), // SCH-TBL-EDU-SESS-START-DATE
  endDate: date('end_date'), // SCH-TBL-EDU-SESS-END-DATE

  parentSessionId: uuid('parent_session_id'), // SCH-TBL-EDU-SESS-PARENT-ID

  onerosterAcademicSessionSourcedId: varchar('oneroster_academic_session_sourced_id', { length: 255 }).unique(), // SCH-TBL-EDU-SESS-ONEROSTER-SOURCED-ID
  googleClassroomCourseId: varchar('google_classroom_course_id', { length: 255 }).unique(), // SCH-TBL-EDU-SESS-GOOGLE-COURSE-ID

  sourceSpecificMetadataJsonb: jsonb('source_specific_metadata_jsonb').$type<Record<string, any>>().default({}), // SCH-TBL-EDU-SESS-SOURCE-META

  createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-EDU-SESS-CREATED-AT
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-EDU-SESS-UPDATED-AT
}, (table) => {
  return {
    schoolOrgIdIdx: index('app_session_school_org_id_idx').on(table.schoolOrgId),
    sessionTypeIdx: index('app_session_type_idx').on(table.sessionType),
    statusIdx: index('app_session_status_idx').on(table.status),
    onerosterSourcedIdIdx: index('app_session_oneroster_sourced_id_idx').on(table.onerosterAcademicSessionSourcedId),
    googleCourseIdIdx: index('app_session_google_course_id_idx').on(table.googleClassroomCourseId),
    parentSessionIdIdx: index('app_session_parent_id_idx').on(table.parentSessionId).nullsLast(),
  };
});

// SCH-TBL-EDU-COURSE: app_courses Table (Courses)
export const appCourses = pgTable('app_courses', {
  id: uuid('id').primaryKey().defaultRandom(), // SCH-TBL-EDU-COURSE-ID

  schoolOrgId: varchar('school_org_id', { length: 255 }).references(() => organizations.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-EDU-COURSE-SCHOOL-ORG-ID

  name: text('name').notNull(), // SCH-TBL-EDU-COURSE-NAME
  courseCode: varchar('course_code', { length: 100 }), // SCH-TBL-EDU-COURSE-CODE
  description: text('description'), // SCH-TBL-EDU-COURSE-DESC
  status: appEntityStatusEnum('status').default('active').notNull(), // SCH-TBL-EDU-COURSE-STATUS

  subjectArea: text('subject_area'), // SCH-TBL-EDU-COURSE-SUBJECT-AREA
  gradeLevel: text('grade_level'), // SCH-TBL-EDU-COURSE-GRADE-LEVEL

  onerosterCourseSourcedId: varchar('oneroster_course_sourced_id', { length: 255 }).unique(), // SCH-TBL-EDU-COURSE-ONEROSTER-SOURCED-ID
  googleClassroomCourseId: varchar('google_classroom_course_id', { length: 255 }).unique(), // SCH-TBL-EDU-COURSE-GOOGLE-COURSE-ID

  sourceSpecificMetadataJsonb: jsonb('source_specific_metadata_jsonb').$type<Record<string, any>>().default({}), // SCH-TBL-EDU-COURSE-SOURCE-META

  createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-EDU-COURSE-CREATED-AT
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-EDU-COURSE-UPDATED-AT
}, (table) => {
  return {
    schoolOrgIdIdx: index('app_course_school_org_id_idx').on(table.schoolOrgId),
    courseCodeIdx: index('app_course_code_idx').on(table.courseCode).nullsLast(),
    statusIdx: index('app_course_status_idx').on(table.status),
    subjectAreaIdx: index('app_course_subject_area_idx').on(table.subjectArea).nullsLast(),
    onerosterSourcedIdIdx: index('app_course_oneroster_sourced_id_idx').on(table.onerosterCourseSourcedId),
    googleCourseIdIdx: index('app_course_google_course_id_idx').on(table.googleClassroomCourseId),
  };
});

// SCH-TBL-EDU-SECTION: app_sections Table (Class Sections)
export const appSections = pgTable('app_sections', {
  id: uuid('id').primaryKey().defaultRandom(), // SCH-TBL-EDU-SECTION-ID

  appCourseId: uuid('app_course_id').references(() => appCourses.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-EDU-SECTION-COURSE-ID
  schoolOrgId: varchar('school_org_id', { length: 255 }).references(() => organizations.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-EDU-SECTION-SCHOOL-ORG-ID
  primaryAcademicSessionId: uuid('primary_academic_session_id').references(() => appAcademicSessions.id, { onDelete: 'set null' }), // SCH-TBL-EDU-SECTION-PRIMARY-SESS-ID

  name: text('name').notNull(), // SCH-TBL-EDU-SECTION-NAME
  sectionCode: varchar('section_code', { length: 100 }), // SCH-TBL-EDU-SECTION-CODE
  description: text('description'), // SCH-TBL-EDU-SECTION-DESC
  status: appEntityStatusEnum('status').default('active').notNull(), // SCH-TBL-EDU-SECTION-STATUS

  room: text('room'), // SCH-TBL-EDU-SECTION-ROOM
  meetingTimes: text('meeting_times'), // SCH-TBL-EDU-SECTION-MEETING-TIMES

  onerosterClassSourcedId: varchar('oneroster_class_sourced_id', { length: 255 }).unique(), // SCH-TBL-EDU-SECTION-ONEROSTER-SOURCED-ID
  googleClassroomCourseId: varchar('google_classroom_course_id', { length: 255 }).unique(), // SCH-TBL-EDU-SECTION-GOOGLE-COURSE-ID

  sourceSpecificMetadataJsonb: jsonb('source_specific_metadata_jsonb').$type<Record<string, any>>().default({}), // SCH-TBL-EDU-SECTION-SOURCE-META

  createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-EDU-SECTION-CREATED-AT
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-EDU-SECTION-UPDATED-AT
}, (table) => {
  return {
    appCourseIdIdx: index('app_section_course_id_idx').on(table.appCourseId),
    schoolOrgIdIdx: index('app_section_school_org_id_idx').on(table.schoolOrgId),
    primaryAcademicSessionIdIdx: index('app_section_primary_session_id_idx').on(table.primaryAcademicSessionId).nullsLast(),
    sectionCodeIdx: index('app_section_code_idx').on(table.sectionCode).nullsLast(),
    statusIdx: index('app_section_status_idx').on(table.status),
    onerosterSourcedIdIdx: index('app_section_oneroster_sourced_id_idx').on(table.onerosterClassSourcedId),
    googleCourseIdIdx: index('app_section_google_course_id_idx').on(table.googleClassroomCourseId),
  };
});

// SCH-TBL-EDU-ENROLL: app_enrollments Table (Student/Teacher Enrollments in Sections)
export const appEnrollments = pgTable('app_enrollments', {
  id: uuid('id').primaryKey().defaultRandom(), // SCH-TBL-EDU-ENROLL-ID

  sectionId: uuid('section_id').references(() => appSections.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-EDU-ENROLL-SECTION-ID
  userId: varchar('user_id', { length: 255 }).references(() => users.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-EDU-ENROLL-USER-ID

  role: appUserRoleEnum('role').notNull(), // SCH-TBL-EDU-ENROLL-ROLE
  status: appEntityStatusEnum('status').default('active').notNull(), // SCH-TBL-EDU-ENROLL-STATUS

  enrolledAt: timestamp('enrolled_at', { withTimezone: true, mode: 'date' }).defaultNow(), // SCH-TBL-EDU-ENROLL-ENROLLED-AT
  droppedAt: timestamp('dropped_at', { withTimezone: true, mode: 'date' }), // SCH-TBL-EDU-ENROLL-DROPPED-AT

  onerosterEnrollmentSourcedId: varchar('oneroster_enrollment_sourced_id', { length: 255 }).unique(), // SCH-TBL-EDU-ENROLL-ONEROSTER-SOURCED-ID
  googleClassroomUserId: varchar('google_classroom_user_id', { length: 255 }), // SCH-TBL-EDU-ENROLL-GOOGLE-USER-ID

  sourceSpecificMetadataJsonb: jsonb('source_specific_metadata_jsonb').$type<Record<string, any>>().default({}), // SCH-TBL-EDU-ENROLL-SOURCE-META

  createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-EDU-ENROLL-CREATED-AT
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-EDU-ENROLL-UPDATED-AT
}, (table) => {
  return {
    sectionUserUniqueIdx: index('app_enrollment_section_user_idx').on(table.sectionId, table.userId).unique(),
    sectionIdIdx: index('app_enrollment_section_id_idx').on(table.sectionId),
    userIdIdx: index('app_enrollment_user_id_idx').on(table.userId),
    roleIdx: index('app_enrollment_role_idx').on(table.role),
    statusIdx: index('app_enrollment_status_idx').on(table.status),
    onerosterSourcedIdIdx: index('app_enrollment_oneroster_sourced_id_idx').on(table.onerosterEnrollmentSourcedId),
  };
});

// Additional educational tables can be added here as needed
// For now, we'll include the core tables to get the basic structure working

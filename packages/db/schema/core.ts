// SCH-CORE-TBL: Core Application Tables
import { pgTable, text, timestamp, jsonb, varchar, boolean, index, primaryKey, uniqueIndex, bigint } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import {
  appGlobalRoleEnum, appOneRosterUserStatusEnum, appOrgTypeEnum, appApiKeyStatusEnum
} from './enums'; // SCH-ENUM

// SCH-TBL-USERS: Users Table (Extended)
export const users = pgTable('users', {
  id: varchar('id', { length: 255 }).primaryKey(), // SCH-TBL-USERS-ID: Clerk User ID
  email: text('email').notNull().unique(),         // SCH-TBL-USERS-EMAIL: User's primary email
  name: text('name'),                            // SCH-TBL-USERS-NAME: User's full display name
  profileImageUrl: text('profile_image_url'),    // SCH-TBL-USERS-PROFILE-IMG: URL to user's profile image

  appGlobalRole: appGlobalRoleEnum('app_global_role').default('StandardUser').notNull(), // SCH-TBL-USERS-APP-GLOBAL-ROLE: Global app role (e.g., SuperAdmin)

  givenName: text('given_name'),                   // SCH-TBL-USERS-GIVEN-NAME: User's first name (OneRoster)
  familyName: text('family_name'),                 // SCH-TBL-USERS-FAMILY-NAME: User's last name (OneRoster)
  middleName: text('middle_name'),                 // SCH-TBL-USERS-MIDDLE-NAME: User's middle name (OneRoster)
  usernameApp: text('username_app'),               // SCH-TBL-USERS-USERNAME-APP: App-specific username or OneRoster username
  phone: text('phone'),                            // SCH-TBL-USERS-PHONE: User's phone number (OneRoster)

  userProfileJsonb: jsonb('user_profile_jsonb').$type<{ // SCH-TBL-USERS-PROFILE-JSONB: Flexible app-specific profile data
    bio?: string | null;
    socialLinks?: { linkedin?: string | null; twitter?: string | null; github?: string | null; website?: string | null } | null;
    jobTitle?: string | null;
    pronouns?: string | null;
    location?: string | null;
  }>().default({}),

  preferencesJsonb: jsonb('preferences_jsonb').$type<{ // SCH-TBL-USERS-PREFS-JSONB: User-configurable app preferences
    theme?: 'light' | 'dark' | 'system' | null;
    language?: string | null;
    timezone?: string | null;
    notifications?: {
      emailEnabled?: boolean | null;
      inAppEnabled?: boolean | null;
      digestFrequency?: 'daily' | 'weekly' | 'never' | null;
    } | null;
    aiChatDefaults?: {
      defaultModelSlug?: string | null;
      sendOnEnter?: boolean | null;
      autoScrollChat?: boolean | null;
    } | null;
  }>().default({ theme: 'system', notifications: { emailEnabled: true, inAppEnabled: true, digestFrequency: 'daily' } }),

  apiKeysJsonb: jsonb('api_keys_jsonb').$type<Array<{ // SCH-TBL-USERS-API-KEYS-JSONB: (Alternative to app_api_keys table)
    keyId: string;
    keyHash: string;
    prefix: string;
    label: string;
    scopes: string[];
    createdAt: string;
    lastUsedAt?: string | null;
    expiresAt?: string | null;
    status: 'active' | 'revoked' | 'expired';
    ipRestrictions?: string[] | null;
  }>>().default([]),

  // SCH-TBL-USERS-ONEROSTER-SOURCED-ID: OneRoster User.sourcedId
  onerosterUserSourcedId: varchar('oneroster_user_sourced_id', { length: 255 }).unique(),
  // SCH-TBL-USERS-ONEROSTER-STATUS: OneRoster User.status
  onerosterUserStatus: appOneRosterUserStatusEnum('oneroster_user_status'),
  // SCH-TBL-USERS-ONEROSTER-LAST-MOD: OneRoster User.dateLastModified
  onerosterUserDateLastModified: timestamp('oneroster_user_date_last_modified', { withTimezone: true, mode: 'date' }),

  googleUserId: varchar('google_user_id', { length: 255 }).unique(), // SCH-TBL-USERS-GOOGLE-ID: Google User Profile ID

  sourceSpecificMetadataJsonb: jsonb('source_specific_metadata_jsonb').$type<Record<string, any>>().default({}), // SCH-TBL-USERS-SOURCE-META: Flexible field for additional source-specific data

  appCreatedAt: timestamp('app_created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-USERS-APP-CREATED-AT
  appUpdatedAt: timestamp('app_updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-USERS-APP-UPDATED-AT
  appLastActivityAt: timestamp('app_last_activity_at', { withTimezone: true, mode: 'date' }), // SCH-TBL-USERS-APP-LAST-ACTIVITY
  appLastLoginAt: timestamp('app_last_login_at', { withTimezone: true, mode: 'date' }),     // SCH-TBL-USERS-APP-LAST-LOGIN
}, (table) => {
  return {
    emailIdx: index('user_email_idx').on(table.email),
    appGlobalRoleIdx: index('user_app_global_role_idx').on(table.appGlobalRole),
    onerosterSourcedIdIdx: uniqueIndex('user_oneroster_sourced_id_idx').on(table.onerosterUserSourcedId),
    googleUserIdIdx: uniqueIndex('user_google_user_id_idx').on(table.googleUserId),
    usernameAppIdx: index('user_username_app_idx').on(table.usernameApp),
  };
});

// SCH-TBL-ORGS: Organizations Table (Extended)
export const organizations = pgTable('organizations', {
  id: varchar('id', { length: 255 }).primaryKey(), // SCH-TBL-ORGS-ID: Clerk Organization ID
  name: text('name').notNull(),                    // SCH-TBL-ORGS-NAME: Organization's display name
  slug: varchar('slug', { length: 255 }).notNull().unique(), // SCH-TBL-ORGS-SLUG: Application-generated URL-friendly unique identifier
  imageUrl: text('image_url'),                   // SCH-TBL-ORGS-IMG-URL: URL to organization's logo
  createdByClerkUserId: varchar('created_by_clerk_user_id', { length: 255 }), // SCH-TBL-ORGS-CREATED-BY: Clerk User ID of the creator

  appSettingsJsonb: jsonb('app_settings_jsonb').$type<{ // SCH-TBL-ORGS-APP-SETTINGS-JSONB: App-specific settings for this organization
    defaultAiModelSlug?: string | null;
    defaultLocale?: string | null;
    defaultTimezone?: string | null;
    featureFlags?: Record<string, boolean>;
    branding?: { primaryColor?: string; secondaryColor?: string; appFont?: string; appLogoOverrideUrl?: string };
    dataRetentionPolicyDays?: number | null;
    aiChatOrgContextFileIds?: string[];
  }>().default({}),

  subscriptionJsonb: jsonb('subscription_jsonb').$type<{ // SCH-TBL-ORGS-SUB-JSONB: Stubs for billing provider integration
    provider?: 'stripe' | 'paddle' | 'none' | string;
    providerCustomerId?: string | null;
    providerSubscriptionId?: string | null;
    planId?: string;
    status?: 'active' | 'trialing' | 'past_due' | 'canceled' | 'incomplete' | 'unpaid' | 'paused';
    currentPeriodStart?: string;
    currentPeriodEnd?: string;
    trialEndsAt?: string | null;
    cancelAtPeriodEnd?: boolean;
    canceledAt?: string | null;
    usageLimits?: {
      maxUsers?: number;
      maxAiPromptTokensPerMonth?: number;
      maxAiCompletionTokensPerMonth?: number;
      maxStorageGb?: number;
      maxActiveSections?: number;
      maxStudentsPerOrg?: number;
    };
    currentUsage?: {
      activeUsers?: number;
      aiPromptTokensThisPeriod?: number;
      aiCompletionTokensThisPeriod?: number;
      storageUsedGb?: number;
      activeSections?: number;
      studentsEnrolled?: number;
    };
  }>(),

  statsJsonb: jsonb('stats_jsonb').$type<{ // SCH-TBL-ORGS-STATS-JSONB: Denormalized counters for quick display
    activeAppMemberCount?: number;
    projectCount?: number;
    documentCount?: number;
    courseCount?: number;
    sectionCount?: number;
    teacherCount?: number;
    studentCount?: number;
    totalStorageUsedBytes?: number;
  }>(),

  orgType: appOrgTypeEnum('org_type'), // SCH-TBL-ORGS-ORG-TYPE: Type of organization (e.g., 'district', 'school')
  externalIdentifier: text('external_identifier'), // SCH-TBL-ORGS-EXT-ID: Human-readable external ID (e.g., NCES ID)
  parentAppOrganizationId: varchar('parent_app_organization_id', { length: 255 }), // SCH-TBL-ORGS-PARENT-ID: FK to self for hierarchical app organizations

  onerosterOrgSourcedId: varchar('oneroster_org_sourced_id', { length: 255 }).unique(), // SCH-TBL-ORGS-ONEROSTER-SOURCED-ID: OneRoster Organization.sourcedId
  onerosterOrgParentSourcedId: varchar('oneroster_org_parent_sourced_id', { length: 255 }), // SCH-TBL-ORGS-ONEROSTER-PARENT-SOURCED-ID: OneRoster Organization.parent.sourcedId

  googleDomain: text('google_domain'), // SCH-TBL-ORGS-GOOGLE-DOMAIN: Google Workspace domain linked to this organization
  googleCustomerId: text('google_customer_id'), // SCH-TBL-ORGS-GOOGLE-CUSTOMER-ID: Google Workspace Customer ID

  sourceSpecificMetadataJsonb: jsonb('source_specific_metadata_jsonb').$type<Record<string, any>>(), // SCH-TBL-ORGS-SOURCE-META: Flexible field for additional source-specific data

  appCreatedAt: timestamp('app_created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-ORGS-APP-CREATED-AT
  appUpdatedAt: timestamp('app_updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-ORGS-APP-UPDATED-AT
}, (table) => {
  return {
    slugIdx: uniqueIndex('org_slug_idx').on(table.slug),
    onerosterOrgSourcedIdIdx: uniqueIndex('org_oneroster_sourced_id_idx').on(table.onerosterOrgSourcedId),
    googleDomainIdx: uniqueIndex('org_google_domain_idx').on(table.googleDomain),
    orgTypeIdx: index('org_org_type_idx').on(table.orgType),
    parentAppOrganizationIdIdx: index('org_parent_app_org_id_idx').on(table.parentAppOrganizationId),
  };
});

// Legacy organization table for backward compatibility
export const organizationSchema = pgTable(
  'organization',
  {
    id: text('id').primaryKey(),
    stripeCustomerId: text('stripe_customer_id'),
    stripeSubscriptionId: text('stripe_subscription_id'),
    stripeSubscriptionPriceId: text('stripe_subscription_price_id'),
    stripeSubscriptionStatus: text('stripe_subscription_status'),
    stripeSubscriptionCurrentPeriodEnd: bigint(
      'stripe_subscription_current_period_end',
      { mode: 'number' },
    ),
    updatedAt: timestamp('updated_at', { mode: 'date' })
      .defaultNow()
      .$onUpdateFn(() => new Date())
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
  },
  (table) => {
    return {
      stripeCustomerIdIdx: uniqueIndex('stripe_customer_id_idx').on(
        table.stripeCustomerId,
      ),
    };
  },
);

// Legacy todo table for backward compatibility
export const todoSchema = pgTable('todo', {
  id: text('id').primaryKey(),
  ownerId: text('owner_id').notNull(),
  title: text('title').notNull(),
  message: text('message').notNull(),
  updatedAt: timestamp('updated_at', { mode: 'date' })
    .defaultNow()
    .$onUpdateFn(() => new Date())
    .notNull(),
  createdAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
});

// SCH-REL: Drizzle Relations for All Tables
import { relations } from 'drizzle-orm';

// Import ALL your table schemas
import { users, organizations, organizationSchema, todoSchema } from './core'; // SCH-CORE-TBL
import { organizationMembers, appRoles, appPermissions, rolePermissions } from './rbac'; // SCH-RBAC-TBL
import { auditLogs, notifications, appApiKeys } from './saas'; // SCH-SAAS-TBL
import {
  appCollaborativeDocuments, appDocumentVersions, appDocumentComments,
  appProjects, appProjectMembers, appTasks, appTaskComments
} from './saas_crud_examples'; // SCH-SAAS-CRUD-TBL
import {
  aiJobs, aiMessages, aiUploadedFilesMetadata,
  aiModelConfigurations, aiGlobalSystemSettings, aiStreamTokens
} from './ai'; // SCH-AI-TBL
import {
  appAcademicSessions, appCourses, appSections, appEnrollments
} from './ed_data'; // SCH-EDU-TBL

// SCH-REL-USERS: Relations for Users Table
export const usersRelations = relations(users, ({ many }) => ({
  appOrganizationMemberships: many(organizationMembers, { relationName: 'user_to_app_organization_memberships' }), // SCH-REL-USERS-ORG-MEM
  ownedAppCollaborativeDocuments: many(appCollaborativeDocuments, { relationName: 'document_owner', foreignKeys: [appCollaborativeDocuments.ownerUserId] }), // SCH-REL-USERS-OWNED-DOCS
  lastUpdatedAppCollaborativeDocuments: many(appCollaborativeDocuments, { relationName: 'document_last_updater', foreignKeys: [appCollaborativeDocuments.lastUpdatedByUserId] }), // SCH-REL-USERS-UPDATED-DOCS
  appDocumentVersions: many(appDocumentVersions, { relationName: 'version_saver', foreignKeys: [appDocumentVersions.savedByUserId] }), // SCH-REL-USERS-DOC-VERSIONS
  appDocumentComments: many(appDocumentComments, { relationName: 'document_commenter', foreignKeys: [appDocumentComments.userId] }), // SCH-REL-USERS-DOC-COMMENTS
  resolvedAppDocumentComments: many(appDocumentComments, { relationName: 'comment_resolver', foreignKeys: [appDocumentComments.resolvedByUserId] }), // SCH-REL-USERS-RESOLVED-COMMENTS
  ownedAppProjects: many(appProjects, { relationName: 'project_owner', foreignKeys: [appProjects.ownerUserId] }), // SCH-REL-USERS-OWNED-PROJECTS
  appProjectMemberships: many(appProjectMembers, { relationName: 'project_member', foreignKeys: [appProjectMembers.userId] }), // SCH-REL-USERS-PROJECT-MEMBERSHIPS
  assignedAppTasks: many(appTasks, { relationName: 'task_assignee', foreignKeys: [appTasks.assigneeUserId] }), // SCH-REL-USERS-ASSIGNED-TASKS
  reportedAppTasks: many(appTasks, { relationName: 'task_reporter', foreignKeys: [appTasks.reporterUserId] }), // SCH-REL-USERS-REPORTED-TASKS
  appTaskComments: many(appTaskComments, { relationName: 'task_commenter', foreignKeys: [appTaskComments.userId] }), // SCH-REL-USERS-TASK-COMMENTS
  aiJobs: many(aiJobs, { relationName: 'user_ai_jobs', foreignKeys: [aiJobs.userId] }), // SCH-REL-USERS-AI-JOBS
  aiUploadedFiles: many(aiUploadedFilesMetadata, { relationName: 'user_ai_files', foreignKeys: [aiUploadedFilesMetadata.userId] }), // SCH-REL-USERS-AI-FILES
  receivedNotifications: many(notifications, { relationName: 'user_received_notifications', foreignKeys: [notifications.recipientUserId] }), // SCH-REL-USERS-NOTIFICATIONS
  actorAuditLogs: many(auditLogs, { relationName: 'audit_log_actor', foreignKeys: [auditLogs.actorUserId] }), // SCH-REL-USERS-AUDIT-LOGS
  impersonatorAuditLogs: many(auditLogs, { relationName: 'audit_log_impersonator', foreignKeys: [auditLogs.impersonatorUserId] }), // SCH-REL-USERS-IMPERSONATOR-LOGS
  appApiKeys: many(appApiKeys, { relationName: 'user_api_keys', foreignKeys: [appApiKeys.userId] }), // SCH-REL-USERS-API-KEYS
  appEnrollments: many(appEnrollments, { relationName: 'user_enrollments', foreignKeys: [appEnrollments.userId] }), // SCH-REL-USERS-ENROLLMENTS
}));

// SCH-REL-ORGS: Relations for Organizations Table
export const organizationsRelations = relations(organizations, ({ one, many }) => ({
  parentAppOrganization: one(organizations, { // SCH-REL-ORGS-PARENT
    fields: [organizations.parentAppOrganizationId],
    references: [organizations.id],
    relationName: 'child_organizations_of_parent'
  }),
  childAppOrganizations: many(organizations, { relationName: 'child_organizations_of_parent' }), // SCH-REL-ORGS-CHILDREN
  appOrganizationMemberships: many(organizationMembers, { relationName: 'org_to_app_organization_memberships' }), // SCH-REL-ORGS-MEMBERSHIPS
  appCollaborativeDocuments: many(appCollaborativeDocuments, { relationName: 'org_documents' }), // SCH-REL-ORGS-DOCUMENTS
  appProjects: many(appProjects, { relationName: 'org_projects' }), // SCH-REL-ORGS-PROJECTS
  appTasks: many(appTasks, { relationName: 'org_tasks' }), // SCH-REL-ORGS-TASKS
  aiJobs: many(aiJobs, { relationName: 'org_ai_jobs' }), // SCH-REL-ORGS-AI-JOBS
  aiUploadedFiles: many(aiUploadedFilesMetadata, { relationName: 'org_ai_files' }), // SCH-REL-ORGS-AI-FILES
  notifications: many(notifications, { relationName: 'org_notifications' }), // SCH-REL-ORGS-NOTIFICATIONS
  auditLogs: many(auditLogs, { relationName: 'org_audit_logs' }), // SCH-REL-ORGS-AUDIT-LOGS
  appApiKeys: many(appApiKeys, { relationName: 'org_api_keys' }), // SCH-REL-ORGS-API-KEYS
  appAcademicSessions: many(appAcademicSessions, { relationName: 'org_academic_sessions' }), // SCH-REL-ORGS-ACADEMIC-SESSIONS
  appCourses: many(appCourses, { relationName: 'org_courses' }), // SCH-REL-ORGS-COURSES
  appSections: many(appSections, { relationName: 'org_sections' }), // SCH-REL-ORGS-SECTIONS
}));

// SCH-REL-ORG-MEM: Relations for organizationMembers Table
export const organizationMembersRelations = relations(organizationMembers, ({ one }) => ({
  user: one(users, { fields: [organizationMembers.userId], references: [users.id], relationName: 'user_to_app_organization_memberships' }), // SCH-REL-ORG-MEM-USER
  organization: one(organizations, { fields: [organizationMembers.organizationId], references: [organizations.id], relationName: 'org_to_app_organization_memberships' }), // SCH-REL-ORG-MEM-ORG
}));

// SCH-REL-APP-ROLES: Relations for appRoles Table
export const appRolesRelations = relations(appRoles, ({ many }) => ({
  rolePermissions: many(rolePermissions, { relationName: 'app_role_to_role_permissions' }), // SCH-REL-APP-ROLES-PERM
}));

// SCH-REL-APP-PERM: Relations for appPermissions Table
export const appPermissionsRelations = relations(appPermissions, ({ many }) => ({
  rolePermissions: many(rolePermissions, { relationName: 'app_permission_to_role_permissions' }), // SCH-REL-APP-PERM-ROLES
}));

// SCH-REL-ROLE-PERM: Relations for rolePermissions Table
export const rolePermissionsRelations = relations(rolePermissions, ({ one }) => ({
  role: one(appRoles, { fields: [rolePermissions.roleId], references: [appRoles.id], relationName: 'app_role_to_role_permissions' }), // SCH-REL-ROLE-PERM-ROLE
  permission: one(appPermissions, { fields: [rolePermissions.permissionId], references: [appPermissions.id], relationName: 'app_permission_to_role_permissions' }), // SCH-REL-ROLE-PERM-PERM
}));

// SCH-REL-AUDIT: Relations for auditLogs Table
export const auditLogsRelations = relations(auditLogs, ({ one }) => ({
  organization: one(organizations, { fields: [auditLogs.organizationId], references: [organizations.id], relationName: 'org_audit_logs' }), // SCH-REL-AUDIT-ORG
  actorUser: one(users, { fields: [auditLogs.actorUserId], references: [users.id], relationName: 'audit_log_actor' }), // SCH-REL-AUDIT-ACTOR
  impersonatorUser: one(users, { fields: [auditLogs.impersonatorUserId], references: [users.id], relationName: 'audit_log_impersonator' }), // SCH-REL-AUDIT-IMPERSONATOR
}));

// SCH-REL-NOTIF: Relations for notifications Table
export const notificationsRelations = relations(notifications, ({ one }) => ({
  recipientUser: one(users, { fields: [notifications.recipientUserId], references: [users.id], relationName: 'user_received_notifications' }), // SCH-REL-NOTIF-RECIPIENT
  organization: one(organizations, { fields: [notifications.organizationId], references: [organizations.id], relationName: 'org_notifications' }), // SCH-REL-NOTIF-ORG
}));

// SCH-REL-API-KEYS: Relations for appApiKeys Table
export const appApiKeysRelations = relations(appApiKeys, ({ one }) => ({
  user: one(users, { fields: [appApiKeys.userId], references: [users.id], relationName: 'user_api_keys' }), // SCH-REL-API-KEYS-USER
  organization: one(organizations, { fields: [appApiKeys.organizationId], references: [organizations.id], relationName: 'org_api_keys' }), // SCH-REL-API-KEYS-ORG
}));

// SCH-REL-DOCS: Relations for appCollaborativeDocuments Table
export const appCollaborativeDocumentsRelations = relations(appCollaborativeDocuments, ({ one, many }) => ({
  organization: one(organizations, { fields: [appCollaborativeDocuments.organizationId], references: [organizations.id], relationName: 'org_documents' }), // SCH-REL-DOCS-ORG
  owner: one(users, { fields: [appCollaborativeDocuments.ownerUserId], references: [users.id], relationName: 'document_owner' }), // SCH-REL-DOCS-OWNER
  lastUpdatedBy: one(users, { fields: [appCollaborativeDocuments.lastUpdatedByUserId], references: [users.id], relationName: 'document_last_updater' }), // SCH-REL-DOCS-LAST-UPDATER
  versions: many(appDocumentVersions, { relationName: 'document_to_versions' }), // SCH-REL-DOCS-VERSIONS
  comments: many(appDocumentComments, { relationName: 'document_to_comments' }), // SCH-REL-DOCS-COMMENTS
}));

// SCH-REL-DOC-VER: Relations for appDocumentVersions Table
export const appDocumentVersionsRelations = relations(appDocumentVersions, ({ one }) => ({
  document: one(appCollaborativeDocuments, { fields: [appDocumentVersions.documentId], references: [appCollaborativeDocuments.id], relationName: 'document_to_versions' }), // SCH-REL-DOC-VER-DOC
  savedByUser: one(users, { fields: [appDocumentVersions.savedByUserId], references: [users.id], relationName: 'version_saver' }), // SCH-REL-DOC-VER-SAVED-BY
}));

// SCH-REL-DOC-COMM: Relations for appDocumentComments Table
export const appDocumentCommentsRelations = relations(appDocumentComments, ({ one }) => ({
  document: one(appCollaborativeDocuments, { fields: [appDocumentComments.documentId], references: [appCollaborativeDocuments.id], relationName: 'document_to_comments' }), // SCH-REL-DOC-COMM-DOC
  user: one(users, { fields: [appDocumentComments.userId], references: [users.id], relationName: 'document_commenter' }), // SCH-REL-DOC-COMM-USER
  resolvedByUser: one(users, { fields: [appDocumentComments.resolvedByUserId], references: [users.id], relationName: 'comment_resolver' }), // SCH-REL-DOC-COMM-RESOLVED-BY
}));

// SCH-REL-PROJ: Relations for appProjects Table
export const appProjectsRelations = relations(appProjects, ({ one, many }) => ({
    organization: one(organizations, { fields: [appProjects.organizationId], references: [organizations.id], relationName: 'org_projects' }), // SCH-REL-PROJ-ORG
    owner: one(users, { fields: [appProjects.ownerUserId], references: [users.id], relationName: 'project_owner' }), // SCH-REL-PROJ-OWNER
    projectMembers: many(appProjectMembers, { relationName: 'project_to_members' }), // SCH-REL-PROJ-MEMBERS
    tasks: many(appTasks, { relationName: 'project_to_tasks' }), // SCH-REL-PROJ-TASKS
}));

// SCH-REL-PROJ-MEM: Relations for appProjectMembers Table
export const appProjectMembersRelations = relations(appProjectMembers, ({ one }) => ({
    project: one(appProjects, { fields: [appProjectMembers.projectId], references: [appProjects.id], relationName: 'project_to_members' }), // SCH-REL-PROJ-MEM-PROJ
    user: one(users, { fields: [appProjectMembers.userId], references: [users.id], relationName: 'project_member' }), // SCH-REL-PROJ-MEM-USER
}));

// SCH-REL-TASK: Relations for appTasks Table
export const appTasksRelations = relations(appTasks, ({ one, many }) => ({
    project: one(appProjects, { fields: [appTasks.projectId], references: [appProjects.id], relationName: 'project_to_tasks' }), // SCH-REL-TASK-PROJ
    organization: one(organizations, { fields: [appTasks.organizationId], references: [organizations.id] }), // SCH-REL-TASK-ORG
    assignee: one(users, { fields: [appTasks.assigneeUserId], references: [users.id], relationName: 'task_assignee' }), // SCH-REL-TASK-ASSIGNEE
    reporter: one(users, { fields: [appTasks.reporterUserId], references: [users.id], relationName: 'task_reporter' }), // SCH-REL-TASK-REPORTER
    comments: many(appTaskComments, { relationName: 'task_to_comments' }), // SCH-REL-TASK-COMMENTS
}));

// SCH-REL-TASK-COMM: Relations for appTaskComments Table
export const appTaskCommentsRelations = relations(appTaskComments, ({ one }) => ({
    task: one(appTasks, { fields: [appTaskComments.taskId], references: [appTasks.id], relationName: 'task_to_comments' }), // SCH-REL-TASK-COMM-TASK
    user: one(users, { fields: [appTaskComments.userId], references: [users.id], relationName: 'task_commenter' }), // SCH-REL-TASK-COMM-USER
}));

// SCH-REL-AI-JOB: Relations for aiJobs Table
export const aiJobsRelations = relations(aiJobs, ({ one, many }) => ({
  user: one(users, { fields: [aiJobs.userId], references: [users.id], relationName: 'user_ai_jobs' }), // SCH-REL-AI-JOB-USER
  organization: one(organizations, { fields: [aiJobs.organizationId], references: [organizations.id], relationName: 'org_ai_jobs' }), // SCH-REL-AI-JOB-ORG
  messages: many(aiMessages, { relationName: 'job_to_ai_messages' }), // SCH-REL-AI-JOB-MESSAGES
}));

// SCH-REL-AI-MSG: Relations for aiMessages Table
export const aiMessagesRelations = relations(aiMessages, ({ one, many }) => ({
  job: one(aiJobs, { fields: [aiMessages.jobId], references: [aiJobs.id], relationName: 'job_to_ai_messages' }), // SCH-REL-AI-MSG-JOB
  streamTokens: many(aiStreamTokens, { relationName: 'message_to_stream_tokens' }), // SCH-REL-AI-MSG-STREAM-TOKENS
}));

// SCH-REL-AI-FILE: Relations for aiUploadedFilesMetadata Table
export const aiUploadedFilesMetadataRelations = relations(aiUploadedFilesMetadata, ({ one }) => ({
  user: one(users, { fields: [aiUploadedFilesMetadata.userId], references: [users.id], relationName: 'user_ai_files' }), // SCH-REL-AI-FILE-USER
  organization: one(organizations, { fields: [aiUploadedFilesMetadata.organizationId], references: [organizations.id], relationName: 'org_ai_files' }), // SCH-REL-AI-FILE-ORG
}));

// SCH-REL-AI-STREAM-TOK: Relations for aiStreamTokens Table
export const aiStreamTokensRelations = relations(aiStreamTokens, ({ one }) => ({
  message: one(aiMessages, { fields: [aiStreamTokens.messageId], references: [aiMessages.id], relationName: 'message_to_stream_tokens' }), // SCH-REL-AI-STREAM-TOK-MSG
}));

// SCH-REL-EDU-SESS: Relations for appAcademicSessions Table
export const appAcademicSessionsRelations = relations(appAcademicSessions, ({ one, many }) => ({
  school: one(organizations, { fields: [appAcademicSessions.schoolOrgId], references: [organizations.id], relationName: 'org_academic_sessions' }), // SCH-REL-EDU-SESS-SCHOOL
  parentSession: one(appAcademicSessions, { fields: [appAcademicSessions.parentSessionId], references: [appAcademicSessions.id], relationName: 'child_sessions_of_parent' }), // SCH-REL-EDU-SESS-PARENT
  childSessions: many(appAcademicSessions, { relationName: 'child_sessions_of_parent' }), // SCH-REL-EDU-SESS-CHILDREN
  primarySections: many(appSections, { relationName: 'primary_sections_in_session' }), // SCH-REL-EDU-SESS-PRIMARY-SECTIONS
}));

// SCH-REL-EDU-COURSE: Relations for appCourses Table
export const appCoursesRelations = relations(appCourses, ({ one, many }) => ({
  school: one(organizations, { fields: [appCourses.schoolOrgId], references: [organizations.id], relationName: 'org_courses' }), // SCH-REL-EDU-COURSE-SCHOOL
  sections: many(appSections, { relationName: 'course_to_sections' }), // SCH-REL-EDU-COURSE-SECTIONS
}));

// SCH-REL-EDU-SECTION: Relations for appSections Table
export const appSectionsRelations = relations(appSections, ({ one, many }) => ({
  course: one(appCourses, { fields: [appSections.appCourseId], references: [appCourses.id], relationName: 'course_to_sections' }), // SCH-REL-EDU-SECTION-COURSE
  school: one(organizations, { fields: [appSections.schoolOrgId], references: [organizations.id], relationName: 'org_sections' }), // SCH-REL-EDU-SECTION-SCHOOL
  primaryAcademicSession: one(appAcademicSessions, { fields: [appSections.primaryAcademicSessionId], references: [appAcademicSessions.id], relationName: 'primary_sections_in_session' }), // SCH-REL-EDU-SECTION-PRIMARY-SESS
  enrollments: many(appEnrollments, { relationName: 'section_to_enrollments' }), // SCH-REL-EDU-SECTION-ENROLLMENTS
}));

// SCH-REL-EDU-ENROLL: Relations for appEnrollments Table
export const appEnrollmentsRelations = relations(appEnrollments, ({ one }) => ({
  section: one(appSections, { fields: [appEnrollments.sectionId], references: [appSections.id], relationName: 'section_to_enrollments' }), // SCH-REL-EDU-ENROLL-SECTION
  user: one(users, { fields: [appEnrollments.userId], references: [users.id], relationName: 'user_enrollments' }), // SCH-REL-EDU-ENROLL-USER
}));

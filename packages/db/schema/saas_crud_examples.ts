// SCH-SAAS-CRUD-TBL: Example General-Purpose CRUD Entity Tables
import { pgTable, text, timestamp, serial, jsonb, varchar, integer, index, uuid, bigint, primaryKey } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { organizations, users } from './core'; // SCH-CORE-TBL
import { appSaaSEntityStatusEnum, appPriorityEnum } from './enums'; // SCH-ENUM

// SCH-TBL-DOCS: app_collaborative_documents Table
export const appCollaborativeDocuments = pgTable('app_collaborative_documents', {
  id: serial('id').primaryKey(), // SCH-TBL-DOCS-ID

  organizationId: varchar('organization_id', { length: 255 }).references(() => organizations.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-DOCS-ORG-ID

  title: text('title').notNull(), // SCH-TBL-DOCS-TITLE
  description: text('description'), // SCH-TBL-DOCS-DESC

  contentJson: jsonb('content_json').default('{}'), // SCH-TBL-DOCS-CONTENT-JSON

  ownerUserId: varchar('user_id', { length: 255 }).references(() => users.id, { onDelete: 'set null' }).notNull(), // SCH-TBL-DOCS-OWNER-ID

  status: appSaaSEntityStatusEnum('status').default('draft').notNull(), // SCH-TBL-DOCS-STATUS

  lastUpdatedByUserId: varchar('last_updated_by_user_id', { length: 255 }).references(() => users.id, { onDelete: 'set null' }), // SCH-TBL-DOCS-LAST-UPDATED-BY

  tagsJsonb: jsonb('tags_jsonb').$type<string[]>().default([]), // SCH-TBL-DOCS-TAGS

  accessControlJsonb: jsonb('access_control_jsonb').$type<{ // SCH-TBL-DOCS-ACCESS-CONTROL
    visibility: 'private_to_owner_and_explicit_shares' | 'organization_members_can_view' | 'organization_members_can_comment' | 'organization_members_can_edit';
    sharedWithUsers?: Array<{
      userId: string;
      permissionLevel: 'view' | 'comment' | 'edit' | 'manage_sharing';
      sharedByUserId: string;
      sharedAt: string;
    }> | null;
  }>().default({ visibility: 'private_to_owner_and_explicit_shares' }),

  currentVersionNumber: integer('current_version_number').default(1).notNull(), // SCH-TBL-DOCS-CURRENT-VER

  wordCount: integer('word_count').default(0), // SCH-TBL-DOCS-WORD-COUNT
  characterCount: integer('character_count').default(0), // SCH-TBL-DOCS-CHAR-COUNT

  createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-DOCS-CREATED-AT
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-DOCS-UPDATED-AT
  lastSignificantSaveAt: timestamp('last_significant_save_at', { withTimezone: true, mode: 'date' }), // SCH-TBL-DOCS-LAST-SIG-SAVE
}, (table) => {
  return {
    organizationIdIdx: index('app_doc_org_id_idx').on(table.organizationId),
    ownerUserIdIdx: index('app_doc_owner_user_id_idx').on(table.ownerUserId),
    statusIdx: index('app_doc_status_idx').on(table.status),
    titleSearchIdx: index('app_doc_title_search_idx').on(table.title),
  };
});

// SCH-TBL-DOC-VER: app_document_versions Table
export const appDocumentVersions = pgTable('app_document_versions', {
  id: serial('id').primaryKey(), // SCH-TBL-DOC-VER-ID
  documentId: integer('document_id').references(() => appCollaborativeDocuments.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-DOC-VER-DOC-ID

  versionNumber: integer('version_number').notNull(), // SCH-TBL-DOC-VER-NUMBER

  contentJsonSnapshot: jsonb('content_json_snapshot').notNull(), // SCH-TBL-DOC-VER-CONTENT-SNAP

  savedByUserId: varchar('saved_by_user_id', { length: 255 }).references(() => users.id, { onDelete: 'set null' }), // SCH-TBL-DOC-VER-SAVED-BY

  savedAt: timestamp('saved_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-DOC-VER-SAVED-AT

  changeSummary: text('change_summary'), // SCH-TBL-DOC-VER-CHANGE-SUMMARY
}, (table) => {
  return {
    documentIdVersionNumberUniqueIdx: index('app_doc_ver_doc_id_ver_num_idx').on(table.documentId, table.versionNumber).unique(),
    documentIdIdx: index('app_doc_ver_doc_id_idx').on(table.documentId),
    savedByUserIdIdx: index('app_doc_ver_saved_by_idx').on(table.savedByUserId),
  };
});

// SCH-TBL-DOC-COMM: app_document_comments Table
export const appDocumentComments = pgTable('app_document_comments', {
  id: serial('id').primaryKey(), // SCH-TBL-DOC-COMM-ID
  documentId: integer('document_id').references(() => appCollaborativeDocuments.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-DOC-COMM-DOC-ID

  userId: varchar('user_id', { length: 255 }).references(() => users.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-DOC-COMM-USER-ID

  content: text('content').notNull(), // SCH-TBL-DOC-COMM-CONTENT

  selectionAnchorPathJsonb: jsonb('selection_anchor_path_jsonb').$type<any>(), // SCH-TBL-DOC-COMM-ANCHOR-PATH

  threadId: integer('thread_id'), // SCH-TBL-DOC-COMM-THREAD-ID
  parentCommentId: integer('parent_comment_id'), // SCH-TBL-DOC-COMM-PARENT-ID

  status: text('status').default('active').notNull(), // SCH-TBL-DOC-COMM-STATUS
  resolvedByUserId: varchar('resolved_by_user_id', { length: 255 }).references(() => users.id, { onDelete: 'set null' }), // SCH-TBL-DOC-COMM-RESOLVED-BY
  resolvedAt: timestamp('resolved_at', { withTimezone: true, mode: 'date' }), // SCH-TBL-DOC-COMM-RESOLVED-AT

  reactionsJsonb: jsonb('reactions_jsonb').$type<Record<string, string[]>>().default({}), // SCH-TBL-DOC-COMM-REACTIONS

  createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-DOC-COMM-CREATED-AT
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-DOC-COMM-UPDATED-AT
}, (table) => {
  return {
    documentIdIdx: index('app_doc_comment_doc_id_idx').on(table.documentId),
    userIdIdx: index('app_doc_comment_user_id_idx').on(table.userId),
    threadIdIdx: index('app_doc_comment_thread_id_idx').on(table.threadId).nullsLast(),
    parentCommentIdIdx: index('app_doc_comment_parent_id_idx').on(table.parentCommentId).nullsLast(),
  };
});

// SCH-TBL-PROJ: app_projects Table
export const appProjects = pgTable('app_projects', {
  id: serial('id').primaryKey(), // SCH-TBL-PROJ-ID
  organizationId: varchar('organization_id', { length: 255 }).references(() => organizations.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-PROJ-ORG-ID
  name: text('name').notNull(), // SCH-TBL-PROJ-NAME
  description: text('description'), // SCH-TBL-PROJ-DESC
  status: appSaaSEntityStatusEnum('status').default('active').notNull(), // SCH-TBL-PROJ-STATUS
  startDate: timestamp('start_date', { mode: 'date', withTimezone: false }), // SCH-TBL-PROJ-START-DATE
  endDate: timestamp('end_date', { mode: 'date', withTimezone: false }), // SCH-TBL-PROJ-END-DATE
  ownerUserId: varchar('owner_user_id', { length: 255 }).references(() => users.id, { onDelete: 'set null' }).notNull(), // SCH-TBL-PROJ-OWNER-ID
  projectSettingsJsonb: jsonb('project_settings_jsonb').$type<Record<string, any>>(), // SCH-TBL-PROJ-SETTINGS
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-PROJ-CREATED-AT
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-PROJ-UPDATED-AT
}, (table) => {
  return {
    organizationIdIdx: index('app_proj_org_id_idx').on(table.organizationId),
    ownerUserIdIdx: index('app_proj_owner_user_id_idx').on(table.ownerUserId),
    statusIdx: index('app_proj_status_idx').on(table.status),
    nameSearchIdx: index('app_proj_name_search_idx').on(table.name),
  };
});

// SCH-TBL-PROJ-MEM: app_project_members Table
export const appProjectMembers = pgTable('app_project_members', {
  projectId: integer('project_id').references(() => appProjects.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-PROJ-MEM-PROJ-ID
  userId: varchar('user_id', { length: 255 }).references(() => users.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-PROJ-MEM-USER-ID
  projectRole: text('project_role'), // SCH-TBL-PROJ-MEM-ROLE
  assignedAt: timestamp('assigned_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-PROJ-MEM-ASSIGNED-AT
}, (table) => {
  return {
    pk: primaryKey({ columns: [table.projectId, table.userId] }),
    projectIdIdx: index('app_proj_mem_proj_id_idx').on(table.projectId),
    userIdIdx: index('app_proj_mem_user_id_idx').on(table.userId),
  };
});

// SCH-TBL-TASK: app_tasks Table
export const appTasks = pgTable('app_tasks', {
  id: serial('id').primaryKey(), // SCH-TBL-TASK-ID
  projectId: integer('project_id').references(() => appProjects.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-TASK-PROJ-ID
  organizationId: varchar('organization_id', { length: 255 }).references(() => organizations.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-TASK-ORG-ID
  title: text('title').notNull(), // SCH-TBL-TASK-TITLE
  description: text('description'), // SCH-TBL-TASK-DESC
  status: appSaaSEntityStatusEnum('status').default('pending').notNull(), // SCH-TBL-TASK-STATUS
  assigneeUserId: varchar('assignee_user_id', { length: 255 }).references(() => users.id, { onDelete: 'set null' }), // SCH-TBL-TASK-ASSIGNEE-ID
  reporterUserId: varchar('reporter_user_id', { length: 255 }).references(() => users.id, { onDelete: 'set null' }).notNull(), // SCH-TBL-TASK-REPORTER-ID
  priority: appPriorityEnum('priority').default('medium'), // SCH-TBL-TASK-PRIORITY
  dueDate: timestamp('due_date', { mode: 'date', withTimezone: false }), // SCH-TBL-TASK-DUE-DATE
  storyPoints: integer('story_points'), // SCH-TBL-TASK-STORY-POINTS
  tagsJsonb: jsonb('tags_jsonb').$type<string[]>().default([]), // SCH-TBL-TASK-TAGS
  attachmentsJsonb: jsonb('attachments_jsonb').$type<Array<{ // SCH-TBL-TASK-ATTACHMENTS
    fileName: string;
    filePath: string;
    fileType: string;
    uploadedByUserId: string;
    uploadedAt: string;
  }>>().default([]),
  parentTaskId: integer('parent_task_id'), // SCH-TBL-TASK-PARENT-ID
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-TASK-CREATED-AT
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-TASK-UPDATED-AT
}, (table) => {
  return {
    projectIdIdx: index('app_task_proj_id_idx').on(table.projectId),
    organizationIdIdx: index('app_task_org_id_idx').on(table.organizationId),
    assigneeUserIdIdx: index('app_task_assignee_id_idx').on(table.assigneeUserId),
    statusIdx: index('app_task_status_idx').on(table.status),
    priorityIdx: index('app_task_priority_idx').on(table.priority),
    dueDateIdx: index('app_task_due_date_idx').on(table.dueDate),
    parentTaskIdIdx: index('app_task_parent_id_idx').on(table.parentTaskId).nullsLast(),
  };
});

// SCH-TBL-TASK-COMM: app_task_comments Table
export const appTaskComments = pgTable('app_task_comments', {
  id: serial('id').primaryKey(), // SCH-TBL-TASK-COMM-ID
  taskId: integer('task_id').references(() => appTasks.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-TASK-COMM-TASK-ID
  userId: varchar('user_id', { length: 255 }).references(() => users.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-TASK-COMM-USER-ID
  content: text('content').notNull(), // SCH-TBL-TASK-COMM-CONTENT
  threadId: integer('thread_id'), // SCH-TBL-TASK-COMM-THREAD-ID
  parentCommentId: integer('parent_comment_id'), // SCH-TBL-TASK-COMM-PARENT-ID
  status: text('status').default('active').notNull(), // SCH-TBL-TASK-COMM-STATUS
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-TASK-COMM-CREATED-AT
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-TASK-COMM-UPDATED-AT
}, (table) => {
  return {
    taskIdIdx: index('app_task_comment_task_id_idx').on(table.taskId),
    userIdIdx: index('app_task_comment_user_id_idx').on(table.userId),
    threadIdIdx: index('app_task_comment_thread_id_idx').on(table.threadId).nullsLast(),
    parentCommentIdIdx: index('app_task_comment_parent_id_idx').on(table.parentCommentId).nullsLast(),
  };
});

// SCH-RBAC-TBL: Application-Specific Role-Based Access Control (RBAC) Tables
import { pgTable, text, timestamp, serial, jsonb, varchar, index, primaryKey, uniqueIndex, integer, boolean } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { users, organizations } from './core'; // SCH-CORE-TBL

// SCH-TBL-ORG-MEM: organization_members Table (Application-Level Membership & Roles)
export const organizationMembers = pgTable('organization_members', {
  userId: varchar('user_id', { length: 255 }).references(() => users.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-ORG-MEM-USER-ID
  organizationId: varchar('organization_id', { length: 255 }).references(() => organizations.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-ORG-MEM-ORG-ID

  appRoleSlugs: jsonb('app_role_slugs').$type<string[]>().default([]).notNull(), // SCH-TBL-ORG-MEM-APP-ROLE-SLUGS: Array of app-specific role slugs

  appMembershipStatus: text('app_membership_status').default('active_app_access').notNull(), // SCH-TBL-ORG-MEM-STATUS

  appJoinedAt: timestamp('app_joined_at', { withTimezone: true, mode: 'date' }).defaultNow(), // SCH-TBL-ORG-MEM-JOINED-AT
  appLastAccessedOrgAt: timestamp('app_last_accessed_org_at', { withTimezone: true, mode: 'date' }), // SCH-TBL-ORG-MEM-LAST-ACCESSED

  appMembershipCreatedAt: timestamp('app_membership_created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-ORG-MEM-CREATED-AT
  appMembershipUpdatedAt: timestamp('app_membership_updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-ORG-MEM-UPDATED-AT
}, (table) => {
  return {
    pk: primaryKey({ columns: [table.userId, table.organizationId] }),
    userIdIdx: index('org_member_user_id_idx').on(table.userId),
    organizationIdIdx: index('org_member_organization_id_idx').on(table.organizationId),
  };
});

// SCH-TBL-APP-ROLES: app_roles Table (Defines Application-Specific Roles)
export const appRoles = pgTable('app_roles', {
  id: serial('id').primaryKey(), // SCH-TBL-APP-ROLES-ID
  roleSlug: varchar('role_slug', { length: 100 }).notNull().unique(), // SCH-TBL-APP-ROLES-SLUG: Programmatic, unique name
  displayName: text('display_name').notNull(), // SCH-TBL-APP-ROLES-DISPLAY-NAME: User-friendly name
  description: text('description'), // SCH-TBL-APP-ROLES-DESC

  scope: text('scope').default('organization').notNull(), // SCH-TBL-APP-ROLES-SCOPE: 'organization', 'system', 'project'
  isSystemDefined: boolean('is_system_defined').default(true).notNull(), // SCH-TBL-APP-ROLES-IS-SYSTEM-DEF
  category: text('category'), // SCH-TBL-APP-ROLES-CATEGORY

  appCreatedAt: timestamp('app_created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-APP-ROLES-CREATED-AT
  appUpdatedAt: timestamp('app_updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-APP-ROLES-UPDATED-AT
}, (table) => {
  return {
    roleSlugIdx: uniqueIndex('app_role_slug_idx').on(table.roleSlug),
    scopeCategoryIdx: index('app_role_scope_category_idx').on(table.scope, table.category),
  };
});

// SCH-TBL-APP-PERM: app_permissions Table (Defines Granular Application Permissions)
export const appPermissions = pgTable('app_permissions', {
  id: serial('id').primaryKey(), // SCH-TBL-APP-PERM-ID
  permissionSlug: varchar('permission_slug', { length: 150 }).notNull().unique(), // SCH-TBL-APP-PERM-SLUG: Programmatic, unique name
  description: text('description').notNull(), // SCH-TBL-APP-PERM-DESC
  category: text('category').notNull(), // SCH-TBL-APP-PERM-CATEGORY

  appCreatedAt: timestamp('app_created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(), // SCH-TBL-APP-PERM-CREATED-AT
  appUpdatedAt: timestamp('app_updated_at', { withTimezone: true, mode: 'date' }).defaultNow().$onUpdateFn(() => new Date()).notNull(), // SCH-TBL-APP-PERM-UPDATED-AT
}, (table) => {
  return {
    permissionSlugIdx: uniqueIndex('app_permission_slug_idx').on(table.permissionSlug),
    categoryIdx: index('app_permission_category_idx').on(table.category),
  };
});

// SCH-TBL-ROLE-PERM: role_permissions Join Table (Maps app_roles to app_permissions)
export const rolePermissions = pgTable('role_permissions', {
  roleId: integer('role_id').references(() => appRoles.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-ROLE-PERM-ROLE-ID
  permissionId: integer('permission_id').references(() => appPermissions.id, { onDelete: 'cascade' }).notNull(), // SCH-TBL-ROLE-PERM-PERM-ID
}, (table) => {
  return {
    pk: primaryKey({ columns: [table.roleId, table.permissionId] }),
  };
});
